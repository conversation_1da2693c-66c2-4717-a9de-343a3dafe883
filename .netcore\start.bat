@echo off
chcp 65001 >nul
echo ================================
echo ScanOnWeb .NET Core Server
echo ================================

REM Check .NET environment
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: .NET not found, please install .NET 8.0 or higher
    echo Visit: https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo Building project...
dotnet build

if %errorlevel% neq 0 (
    echo Build failed, please check error messages
    pause
    exit /b 1
)

echo Starting .NET Core server...
echo Access URL: http://localhost:8080
echo API Documentation: http://localhost:8080/swagger
echo Press Ctrl+C to stop server

dotnet run

pause
