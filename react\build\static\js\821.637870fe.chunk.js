/*! For license information please see 821.637870fe.chunk.js.LICENSE.txt */
(self.webpackChunkreact_scan_demo=self.webpackChunkreact_scan_demo||[]).push([[821],{821:function(e){e.exports=function(){"use strict";class t{constructor(){this.scaner_work_config={showUI:!1,dpi_x:300,dpi_y:300,deviceIndex:0,showDialog:!1,autoFeedEnable:!0,autoFeed:!1,dupxMode:!1,autoDeskew:!1,autoBorderDetection:!1,colorMode:"RGB",transMode:"memory"},this.h5socket=null,this.imageCount=0,this.tryConnect()}getConnectedServer(e){return console.log("\u5c1d\u8bd5\u8fde\u63a5\u6258\u76d8\u626b\u63cf\u670d\u52a1websocket\u670d\u52a1\u5668..."),new Promise(((t,s)=>{const o=new WebSocket(e[0]);o.onopen=()=>{t(o)},o.onerror=e=>{s(e)}})).then((e=>(console.log("\u8fde\u63a5websocket\u670d\u52a1\u5668\u6210\u529f!"),this.initWebsocketCallback(e),console.log("\u5c1d\u8bd5\u83b7\u53d6\u626b\u63cf\u8bbe\u5907\u5217\u8868..."),this.loadDevices(),e)),(t=>{if(e.length>1)return this.getConnectedServer(e.slice(1));throw t}))}tryConnect(){const e=["ws://127.0.0.1:1001","ws://127.0.0.1:2001","ws://127.0.0.1:3001","ws://127.0.0.1:4001","ws://127.0.0.1:5001"];this.getConnectedServer(e)}initWebsocketCallback(e){this.h5socket=e,this.h5socket.onerror=this.onSocketError.bind(this),this.h5socket.onmessage=this.onSocketMessage.bind(this)}onSocketError(e){alert("\u65e0\u6cd5\u8fde\u63a5\u626b\u63cf\u670d\u52a1\u7a0b\u5e8f,\u8bf7\u68c0\u67e5\u626b\u63cf\u670d\u52a1\u7a0b\u5e8f\u662f\u5426\u5df2\u7ecf\u542f\u52a8\uff01"),console.log("WebSocket error: "+e.data)}isCallbackExist(e){return!(!e||"undefined"===typeof e||void 0===e)&&"function"===typeof e}onSocketMessage(e){const t=JSON.parse(e.data);switch(t.cmd_type){case"getDevicesList":this.isCallbackExist(this.onGetDevicesListEvent)&&this.onGetDevicesListEvent(t);break;case"scanComplete":this.imageCount=t.imageCount,this.isCallbackExist(this.onScanFinishedEvent)&&this.onScanFinishedEvent(t);break;case"selectScanDevice":this.scaner_work_config.deviceIndex=t.currentIndex,this.scaner_work_config.showDialog=t.showDialog,this.scaner_work_config.autoFeedEnable=t.autoFeedEnable,this.scaner_work_config.autoFeed=t.autoFeed,this.scaner_work_config.dupxMode=t.dupxMode,this.scaner_work_config.autoDeskew=t.autoDeskew,this.scaner_work_config.autoBorderDetection=t.autoBorderDetection,this.isCallbackExist(this.onSelectScanDeviceEvent)&&this.onSelectScanDeviceEvent(t);break;case"getImageCount":this.imageCount=t.imageCount,this.isCallbackExist(this.onGetImageCountEvent)&&this.onGetImageCountEvent(t);break;case"getAllImage":this.imageCount=t.imageCount,this.isCallbackExist(this.onGetAllImageEvent)&&this.onGetAllImageEvent(t);break;case"getImageById":this.imageCount=t.imageCount,this.isCallbackExist(this.onGetImageByIdEvent)&&this.onGetImageByIdEvent(t);break;case"loadImageFromUrl":this.imageCount=t.imageCount,this.isCallbackExist(this.onLoadImageFromUrlEvent)&&this.onLoadImageFromUrlEvent(t);break;case"rotateImage":this.imageCount=t.imageCount,this.isCallbackExist(this.onRotateImageEvent)&&this.onRotateImageEvent(t);break;case"getImageSize":this.imageCount=t.imageCount,this.isCallbackExist(this.onGetImageSizeEvent)&&this.onGetImageSizeEvent(t);break;case"uploadAllImageAsPdfToUrl":this.isCallbackExist(this.onUploadAllImageAsPdfToUrlEvent)&&this.onUploadAllImageAsPdfToUrlEvent(t);break;case"uploadAllImageAsTiffToUrl":this.isCallbackExist(this.onUploadAllImageAsTiffToUrlEvent)&&this.onUploadAllImageAsTiffToUrlEvent(t);break;case"uploadJpgImageByIndex":this.isCallbackExist(this.onUploadJpgImageByIndexEvent)&&this.onUploadJpgImageByIndexEvent(t);break;case"upload":this.imageCount=t.imageCount,this.isCallbackExist(this.onUploadEvent)&&this.onUploadEvent(t);break;case"imageEdited":this.isCallbackExist(this.onImageEditedEvent)&&this.onImageEditedEvent(t);break;case"imageDrap":this.isCallbackExist(this.onImageDrapEvent)&&this.onImageDrapEvent(t)}}sendWebSocketCommand(e){try{1===this.h5socket.readyState?this.h5socket.send(JSON.stringify(e)):alert("\u53d1\u9001\u626b\u63cf\u6307\u4ee4\u5931\u8d25\uff01\u8bf7\u5237\u65b0\u9875\u9762\u6216\u8005\u68c0\u67e5\u6258\u76d8\u626b\u63cf\u7a0b\u5e8f\u662f\u5426\u5df2\u7ecf\u6b63\u5e38\u8fd0\u884c!")}catch(t){alert("\u53d1\u9001\u626b\u63cf\u6307\u4ee4\u5931\u8d25\uff01"+t)}}setLicenseKey(e,t,s,o){const n={cmd_type:"setLicenseKey",licenseMode:e,key1:t,key2:s,url:o};this.sendWebSocketCommand(n)}loadDevices(){const e={cmd_type:"getDevicesList"};this.sendWebSocketCommand(e)}selectScanDevice(e){const t={cmd_type:"selectScanDevice",deviceIndex:e};this.sendWebSocketCommand(t)}startScan(){const e={cmd_type:"startScan",config:this.scaner_work_config};this.sendWebSocketCommand(e)}clearAll(){const e={cmd_type:"clearAll"};this.sendWebSocketCommand(e)}getImageCount(){const e={cmd_type:"getImageCount"};this.sendWebSocketCommand(e)}getAllImage(){const e={cmd_type:"getAllImage"};this.sendWebSocketCommand(e)}getImageById(e){const t={cmd_type:"getImageById",index:e};this.sendWebSocketCommand(t)}loadImageFromUrl(e){const t={cmd_type:"loadImageFromUrl",url:e};this.sendWebSocketCommand(t)}rotateImage(e,t){const s={cmd_type:"rotateImage",index:e,angle:t};this.sendWebSocketCommand(s)}getImageSize(e){const t={cmd_type:"getImageSize",index:e};this.sendWebSocketCommand(t)}deleteImageByIndex(e){const t={cmd_type:"deleteImageByIndex",index:e};this.sendWebSocketCommand(t)}uploadAllImageAsPdfToUrl(e,t,s){const o={cmd_type:"uploadAllImageAsPdfToUrl",url:e,id:t,desc:s};this.sendWebSocketCommand(o)}uploadAllImageAsTiffToUrl(e,t,s){const o={cmd_type:"uploadAllImageAsTiffToUrl",url:e,id:t,desc:s};this.sendWebSocketCommand(o)}uploadJpgImageByIndex(e,t,s,o){const n={cmd_type:"uploadJpgImageByIndex",index:o,url:e,id:t,desc:s};this.sendWebSocketCommand(n)}saveAllImageToLocal(e){const t={cmd_type:"saveAllImageToLocal",filename:e};this.sendWebSocketCommand(t)}openClientLocalfile(){const e={cmd_type:"openClientLocalfile"};this.sendWebSocketCommand(e)}ftpUploadAllImage(e,t,s,o,n,a){const i={cmd_type:"ftpUploadAllImage",serverIp:e,port:t,username:s,password:o,serverPath:n,filename:a};this.sendWebSocketCommand(i)}setUploadButtonVisible(e){const t={cmd_type:"setUploadButtonVisible",visible:e};this.sendWebSocketCommand(t)}setFocus(){const e={cmd_type:"focus"};this.sendWebSocketCommand(e)}hidden(){const e={cmd_type:"hidden"};this.sendWebSocketCommand(e)}closeWebSocket(){this.h5socket.close()}}return e.exports&&(e.exports=t),"undefined"!==typeof window&&(window.ScanOnWeb=t),t}()}}]);
//# sourceMappingURL=821.637870fe.chunk.js.map