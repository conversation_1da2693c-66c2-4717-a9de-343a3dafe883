package com.scanonweb.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;

/**
 * 响应工具类
 */
public class ResponseUtil {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 发送JSON响应
     */
    public static void sendJsonResponse(HttpServletResponse response, Object data) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setCharacterEncoding("UTF-8");
        
        PrintWriter out = response.getWriter();
        out.print(objectMapper.writeValueAsString(data));
        out.flush();
    }
    
    /**
     * 发送成功响应
     */
    public static void sendSuccess(HttpServletResponse response, String message, String fileId, String path, Map<String, Object> data) throws IOException {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", message);
        result.put("code", 200);
        result.put("fileId", fileId);
        result.put("path", path);
        result.put("data", data);
        
        response.setStatus(HttpServletResponse.SC_OK);
        sendJsonResponse(response, result);
    }
    
    /**
     * 发送错误响应
     */
    public static void sendError(HttpServletResponse response, String message, int statusCode) throws IOException {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", message);
        result.put("code", statusCode);
        
        response.setStatus(statusCode);
        sendJsonResponse(response, result);
    }
    
    /**
     * 发送错误响应（默认400状态码）
     */
    public static void sendError(HttpServletResponse response, String message) throws IOException {
        sendError(response, message, HttpServletResponse.SC_BAD_REQUEST);
    }
}
