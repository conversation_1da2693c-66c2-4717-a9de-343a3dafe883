package com.scanonweb.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 应用配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "app")
public class AppConfig {

    /**
     * 上传配置
     */
    private Upload upload = new Upload();

    @Data
    public static class Upload {
        /**
         * 上传文件存储路径
         */
        private String path = "./uploads";

        /**
         * 允许的文件类型
         */
        private List<String> allowedTypes = List.of("pdf", "tiff", "tif", "jpg", "jpeg", "png");

        /**
         * 最大文件大小 (字节)
         */
        private long maxSize = 52428800L; // 50MB
    }
}
