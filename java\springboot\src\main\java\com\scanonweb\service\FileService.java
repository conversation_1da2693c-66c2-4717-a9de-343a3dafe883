package com.scanonweb.service;

import com.scanonweb.config.AppConfig;
import com.scanonweb.dto.FileInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.UUID;

/**
 * 文件服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileService {

    private final AppConfig appConfig;

    /**
     * 初始化上传目录
     */
    public void initUploadDirectory() {
        try {
            // 获取绝对路径，避免相对路径问题
            String uploadPathStr = appConfig.getUpload().getPath();
            Path uploadPath;

            if (uploadPathStr.startsWith("./")) {
                // 相对路径，基于当前工作目录
                uploadPath = Paths.get(System.getProperty("user.dir"), uploadPathStr.substring(2));
            } else {
                uploadPath = Paths.get(uploadPathStr);
            }

            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
                log.info("创建上传目录: {}", uploadPath.toAbsolutePath());
            } else {
                log.info("上传目录已存在: {}", uploadPath.toAbsolutePath());
            }
        } catch (IOException e) {
            log.error("创建上传目录失败", e);
            throw new RuntimeException("创建上传目录失败", e);
        }
    }

    /**
     * 获取上传目录路径
     */
    private Path getUploadPath() {
        String uploadPathStr = appConfig.getUpload().getPath();
        if (uploadPathStr.startsWith("./")) {
            return Paths.get(System.getProperty("user.dir"), uploadPathStr.substring(2));
        } else {
            return Paths.get(uploadPathStr);
        }
    }

    /**
     * 保存multipart文件
     */
    public String saveMultipartFile(MultipartFile file, String id, String format) throws IOException {
        initUploadDirectory();

        String filename = generateFilename(id, format);
        Path filePath = getUploadPath().resolve(filename);

        file.transferTo(filePath.toFile());

        log.info("文件保存成功: {} (原文件名: {}, 大小: {} bytes)",
                filename, file.getOriginalFilename(), file.getSize());

        return filename;
    }

    /**
     * 保存Base64编码的文件
     */
    public String saveBase64File(String base64Data, String id, String format) throws IOException {
        initUploadDirectory();

        // 解码Base64数据
        byte[] data = Base64.getDecoder().decode(base64Data);

        String filename = generateFilename(id, format);
        Path filePath = getUploadPath().resolve(filename);

        Files.write(filePath, data);

        log.info("Base64文件保存成功: {} (大小: {} bytes)", filename, data.length);

        return filename;
    }

    /**
     * 生成文件名
     */
    public String generateFilename(String id, String extension) {
        if (id == null || id.trim().isEmpty()) {
            id = UUID.randomUUID().toString().replace("-", "");
        }

        String timestamp = String.valueOf(System.currentTimeMillis());
        return String.format("%s_%s.%s", id, timestamp, extension);
    }

    /**
     * 获取文件列表
     */
    public List<FileInfo> getFileList() {
        List<FileInfo> fileList = new ArrayList<>();

        try {
            Path uploadPath = getUploadPath();
            if (!Files.exists(uploadPath)) {
                return fileList;
            }

            Files.list(uploadPath)
                    .filter(Files::isRegularFile)
                    .forEach(path -> {
                        try {
                            File file = path.toFile();
                            String filename = file.getName();
                            String extension = getFileExtension(filename);

                            FileInfo fileInfo = FileInfo.builder()
                                    .name(filename)
                                    .size(file.length())
                                    .modTime(LocalDateTime.ofInstant(
                                            Files.getLastModifiedTime(path).toInstant(),
                                            ZoneId.systemDefault()))
                                    .url("/uploads/" + filename)
                                    .type(extension)
                                    .build();

                            fileList.add(fileInfo);
                        } catch (IOException e) {
                            log.error("读取文件信息失败: {}", path, e);
                        }
                    });

        } catch (IOException e) {
            log.error("读取文件列表失败", e);
        }

        return fileList;
    }

    /**
     * 删除文件
     */
    public boolean deleteFile(String filename) {
        try {
            Path filePath = getUploadPath().resolve(filename);
            boolean deleted = Files.deleteIfExists(filePath);

            if (deleted) {
                log.info("文件删除成功: {}", filename);
            } else {
                log.warn("文件不存在: {}", filename);
            }

            return deleted;
        } catch (IOException e) {
            log.error("删除文件失败: {}", filename, e);
            return false;
        }
    }

    /**
     * 验证文件类型
     */
    public boolean isValidFileType(String extension) {
        return appConfig.getUpload().getAllowedTypes().contains(extension.toLowerCase());
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filename.length() - 1) {
            return filename.substring(lastDotIndex + 1).toLowerCase();
        }
        return "";
    }
}
