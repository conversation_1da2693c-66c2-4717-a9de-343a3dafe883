# ScanOnWeb SpringBoot Server

基于SpringBoot的文档扫描系统后台服务，用于接收Vue3前端页面提交的上传数据。

## 功能特性

- ✅ 支持PDF、TIFF、JPG格式文件上传
- ✅ 支持multipart/form-data和JSON两种上传方式
- ✅ 自动文件类型验证和大小限制
- ✅ 完整的文件管理功能（列表、删除、下载、预览）
- ✅ CORS跨域支持
- ✅ 全局异常处理
- ✅ 详细的日志记录
- ✅ 健康检查接口

## 技术栈

- **Java**: 17+
- **SpringBoot**: 3.2.0
- **Maven**: 3.6+
- **Lombok**: 简化代码
- **Jackson**: JSON处理
- **Apache Commons**: 工具类

## 快速开始

### 环境要求

- Java 17 或更高版本
- Maven 3.6 或更高版本

### 启动方式

#### 方式1: 使用启动脚本（推荐）
```bash
# Windows
start.bat

# Linux/Mac
chmod +x start.sh
./start.sh
```

#### 方式2: 使用Maven命令
```bash
# 编译项目
mvn clean compile

# 启动服务
mvn spring-boot:run
```

#### 方式3: 打包运行
```bash
# 打包
mvn clean package

# 运行jar包
java -jar target/scanonweb-server-1.0.0.jar
```

### 访问地址

- **服务地址**: http://localhost:8080
- **健康检查**: http://localhost:8080/health
- **文件列表**: http://localhost:8080/files

## API接口

### 1. 健康检查
```
GET /health
```

### 2. PDF上传
```
POST /upload
Content-Type: multipart/form-data 或 application/json

# multipart方式（扫描控件）
FormData:
- image: 文件
- id: 文档ID
- desc: 描述

# JSON方式（前端axios）
{
  "id": "文档ID",
  "desc": "描述",
  "imageData": "base64编码的图像数据"
}
```

### 3. TIFF上传
```
POST /upload/tiff
# 参数同PDF上传
```

### 4. JPG上传
```
POST /upload/jpg
# 参数同PDF上传，额外支持index参数
```

### 5. 文件管理
```
# 获取文件列表
GET /files

# 下载文件
GET /uploads/{filename}

# 预览文件
GET /preview/{filename}
```

## 响应格式

### 成功响应
```json
{
  "success": true,
  "message": "PDF文件上传成功",
  "code": 200,
  "fileId": "scan_123456",
  "path": "/uploads/scan_123456_1701234567890.pdf",
  "data": {
    "filename": "scan_123456_1701234567890.pdf",
    "originalName": "document.pdf",
    "size": 1024000,
    "id": "scan_123456",
    "format": "pdf"
  }
}
```

### 错误响应
```json
{
  "success": false,
  "message": "文件大小超过限制",
  "code": 413
}
```

## 配置说明

### application.yml主要配置

```yaml
server:
  port: 8090                    # 服务端口
  
app:
  upload:
    path: ./uploads             # 上传文件存储路径
    max-size: 52428800         # 最大文件大小(50MB)
    allowed-types:             # 允许的文件类型
      - pdf
      - tiff
      - jpg
```

## 目录结构

```
java/springboot/
├── src/main/java/com/scanonweb/
│   ├── ScanOnWebApplication.java      # 主应用类
│   ├── config/                        # 配置类
│   │   ├── AppConfig.java
│   │   └── CorsConfig.java
│   ├── controller/                    # 控制器
│   │   ├── UploadController.java
│   │   └── FileController.java
│   ├── dto/                          # 数据传输对象
│   │   ├── UploadRequest.java
│   │   ├── UploadResponse.java
│   │   └── FileInfo.java
│   ├── service/                      # 服务层
│   │   └── FileService.java
│   └── exception/                    # 异常处理
│       └── GlobalExceptionHandler.java
├── src/main/resources/
│   └── application.yml               # 配置文件
├── pom.xml                          # Maven配置
├── start.bat                        # Windows启动脚本
└── README.md                        # 说明文档
```

## 开发说明

### 添加新的上传格式

1. 在`UploadController`中添加新的接口方法
2. 在`AppConfig`中添加允许的文件类型
3. 更新前端调用地址

### 自定义配置

修改`application.yml`中的配置项，重启服务即可生效。

## 故障排除

### 常见问题

1. **端口冲突**: 修改`application.yml`中的`server.port`
2. **文件上传失败**: 检查`app.upload.path`目录权限
3. **跨域问题**: 检查`CorsConfig`配置
4. **文件大小限制**: 修改`spring.servlet.multipart.max-file-size`

### 日志查看

日志文件位置: `logs/scanonweb-server.log`
