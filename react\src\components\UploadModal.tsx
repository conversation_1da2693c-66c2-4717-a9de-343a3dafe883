import React, { useState, useEffect } from 'react';
import './UploadModal.css';

interface UploadModalProps {
  visible: boolean;
  loading: boolean;
  onConfirm: (data: { id: string; description: string; format: string }) => void;
  onCancel: () => void;
}

const UploadModal: React.FC<UploadModalProps> = ({
  visible,
  loading,
  onConfirm,
  onCancel
}) => {
  const [formData, setFormData] = useState({
    id: '',
    description: '',
    format: 'pdf'
  });

  // 生成唯一ID
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  };

  // 当模态框显示时重置表单
  useEffect(() => {
    if (visible) {
      setFormData({
        id: generateId(),
        description: `扫描文档_${new Date().toLocaleString()}`,
        format: 'pdf'
      });
    }
  }, [visible]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.id.trim()) {
      alert('请输入文档ID');
      return;
    }

    onConfirm(formData);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (!visible) return null;

  return (
    <div className="modal-overlay" onClick={onCancel}>
      <div className="modal-container" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3 className="modal-title">
            <i className="fas fa-upload"></i>
            上传文档
          </h3>
          <button className="modal-close" onClick={onCancel}>
            <i className="fas fa-times"></i>
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="modal-body">
            <div className="form-group">
              <label htmlFor="upload-id">文档ID</label>
              <input
                id="upload-id"
                type="text"
                className="form-control"
                value={formData.id}
                onChange={(e) => handleInputChange('id', e.target.value)}
                placeholder="请输入文档ID"
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="upload-desc">文档描述</label>
              <textarea
                id="upload-desc"
                className="form-control"
                rows={3}
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="请输入文档描述"
              />
            </div>

            <div className="form-group">
              <label>上传格式</label>
              <div className="radio-group">
                <label className="radio-item">
                  <input
                    type="radio"
                    name="format"
                    value="pdf"
                    checked={formData.format === 'pdf'}
                    onChange={(e) => handleInputChange('format', e.target.value)}
                  />
                  <span className="radio-label">
                    <i className="fas fa-file-pdf"></i>
                    PDF文档
                  </span>
                  <span className="radio-desc">推荐格式，适合文档存档</span>
                </label>

                <label className="radio-item">
                  <input
                    type="radio"
                    name="format"
                    value="tiff"
                    checked={formData.format === 'tiff'}
                    onChange={(e) => handleInputChange('format', e.target.value)}
                  />
                  <span className="radio-label">
                    <i className="fas fa-file-image"></i>
                    TIFF图像
                  </span>
                  <span className="radio-desc">高质量图像格式</span>
                </label>

                <label className="radio-item">
                  <input
                    type="radio"
                    name="format"
                    value="jpg"
                    checked={formData.format === 'jpg'}
                    onChange={(e) => handleInputChange('format', e.target.value)}
                  />
                  <span className="radio-label">
                    <i className="fas fa-file-image"></i>
                    JPG图像
                  </span>
                  <span className="radio-desc">通用图像格式</span>
                </label>
              </div>
            </div>

            <div className="upload-info">
              <div className="info-item">
                <i className="fas fa-info-circle"></i>
                <span>上传接口: http://localhost:8080/upload</span>
              </div>
              <div className="info-item">
                <i className="fas fa-server"></i>
                <span>请确保后台服务已启动</span>
              </div>
            </div>
          </div>

          <div className="modal-footer">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={onCancel}
              disabled={loading}
            >
              取消
            </button>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={loading}
            >
              {loading ? (
                <>
                  <i className="fas fa-spinner fa-spin"></i>
                  上传中...
                </>
              ) : (
                <>
                  <i className="fas fa-upload"></i>
                  确认上传
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UploadModal;
