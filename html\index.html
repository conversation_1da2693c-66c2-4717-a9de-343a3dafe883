<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档扫描系统 - HTML版本</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="scan-container">
        <!-- 页面标题 -->
        <div class="header-card">
            <h1 class="page-title">
                <i class="fas fa-camera"></i>
                文档扫描系统
            </h1>
            <p class="page-subtitle">专业的文档扫描与管理解决方案</p>
        </div>

        <!-- 连接状态指示器 -->
        <div id="connection-alert" class="connection-alert" style="display: none;">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <div class="alert-content">
                    <strong>扫描服务未连接</strong>
                    <p>请确保扫描服务程序已启动并正在运行</p>
                </div>
            </div>
        </div>

        <!-- 扫描配置区域 -->
        <div class="config-card">
            <div class="card-header">
                <i class="fas fa-cog"></i>
                <span>扫描配置</span>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- 设备选择 -->
                    <div class="col-6">
                        <div class="form-item">
                            <label>扫描设备</label>
                            <select id="device-select" class="form-control">
                                <option value="-1">请选择扫描设备</option>
                            </select>
                        </div>
                    </div>

                    <!-- 色彩模式 -->
                    <div class="col-6">
                        <div class="form-item">
                            <label>色彩模式</label>
                            <select id="color-mode" class="form-control">
                                <option value="RGB">彩色</option>
                                <option value="GRAY">灰度</option>
                                <option value="BW">黑白</option>
                            </select>
                        </div>
                    </div>

                    <!-- 分辨率设置 -->
                    <div class="col-6">
                        <div class="form-item">
                            <label>分辨率 (DPI)</label>
                            <div class="dpi-row">
                                <input type="number" id="dpi-x" class="form-control" value="300" min="75" max="1200" step="25">
                                <span class="dpi-separator">×</span>
                                <input type="number" id="dpi-y" class="form-control" value="300" min="75" max="1200" step="25">
                            </div>
                        </div>
                    </div>

                    <!-- 高级选项 -->
                    <div class="col-6">
                        <div class="form-item">
                            <label>高级选项</label>
                            <div class="advanced-options">
                                <div class="options-row">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="show-dialog">
                                        <span>设备对话框</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="auto-feed" checked>
                                        <span>自动进纸</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="duplex-mode">
                                        <span>双面扫描</span>
                                    </label>
                                </div>
                                <div class="options-row">
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="auto-deskew">
                                        <span>自动纠偏</span>
                                    </label>
                                    <label class="checkbox-label">
                                        <input type="checkbox" id="auto-border">
                                        <span>边框检测</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮区域 -->
        <div class="action-card">
            <div class="button-row">
                <button id="refresh-devices" class="btn btn-primary">
                    <i class="fas fa-sync-alt"></i>
                    刷新设备
                </button>
                <button id="start-scan" class="btn btn-success" disabled>
                    <i class="fas fa-camera"></i>
                    开始扫描
                </button>
                <button id="get-images" class="btn btn-info">
                    <i class="fas fa-images"></i>
                    获取图像
                </button>
                <button id="clear-all" class="btn btn-warning">
                    <i class="fas fa-trash"></i>
                    清空结果
                </button>
                <button id="upload-docs" class="btn btn-primary" disabled>
                    <i class="fas fa-upload"></i>
                    上传文档
                </button>
                <button id="save-local" class="btn btn-secondary" disabled>
                    <i class="fas fa-save"></i>
                    本地保存
                </button>
            </div>
        </div>

        <!-- 扫描结果展示区域 -->
        <div id="result-card" class="result-card" style="display: none;">
            <div class="card-header">
                <div class="header-left">
                    <i class="fas fa-images"></i>
                    <span id="result-title">扫描结果 (0 张图像)</span>
                </div>
                <div class="header-actions">
                    <div class="btn-group">
                        <button id="view-grid" class="btn btn-sm btn-primary">
                            <i class="fas fa-th"></i> 网格
                        </button>
                        <button id="view-list" class="btn btn-sm btn-default">
                            <i class="fas fa-list"></i> 列表
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <!-- 网格视图 -->
                <div id="image-grid" class="image-grid"></div>
                
                <!-- 列表视图 -->
                <div id="image-list" class="image-list" style="display: none;">
                    <table class="table">
                        <thead>
                            <tr>
                                <th width="120">预览</th>
                                <th width="100">图像编号</th>
                                <th width="100">格式</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="image-table-body">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 空状态 -->
        <div id="empty-state" class="empty-state">
            <div class="empty-content">
                <i class="fas fa-images empty-icon"></i>
                <h3>暂无扫描结果</h3>
                <p>点击开始扫描按钮开始扫描文档</p>
                <button id="empty-scan" class="btn btn-primary" disabled>
                    开始扫描
                </button>
            </div>
        </div>
    </div>

    <!-- 上传对话框 -->
    <div id="upload-dialog" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>上传文档</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-item">
                    <label>文档ID</label>
                    <input type="text" id="upload-id" class="form-control" placeholder="请输入文档ID">
                </div>
                <div class="form-item">
                    <label>文档描述</label>
                    <textarea id="upload-desc" class="form-control" rows="3" placeholder="请输入文档描述"></textarea>
                </div>
                <div class="form-item">
                    <label>上传格式</label>
                    <div class="radio-group">
                        <label class="radio-label">
                            <input type="radio" name="upload-format" value="pdf" checked>
                            <span>PDF文档</span>
                        </label>
                        <label class="radio-label">
                            <input type="radio" name="upload-format" value="tiff">
                            <span>TIFF图像</span>
                        </label>
                        <label class="radio-label">
                            <input type="radio" name="upload-format" value="jpg">
                            <span>JPG图像</span>
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button id="upload-cancel" class="btn btn-default">取消</button>
                <button id="upload-confirm" class="btn btn-primary">
                    <span class="btn-text">确认上传</span>
                    <span class="btn-loading" style="display: none;">
                        <i class="fas fa-spinner fa-spin"></i> 上传中...
                    </span>
                </button>
            </div>
        </div>
    </div>

    <!-- 图像预览对话框 -->
    <div id="preview-dialog" class="modal" style="display: none;">
        <div class="modal-content modal-large">
            <div class="modal-header">
                <h3>图像预览</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div class="preview-container">
                    <img id="preview-image" class="preview-image" alt="预览图像">
                </div>
            </div>
            <div class="modal-footer">
                <div class="preview-info">
                    <span id="preview-info-text">图像 1 / 1</span>
                </div>
                <div class="preview-actions">
                    <button id="prev-image" class="btn btn-default">
                        <i class="fas fa-chevron-left"></i> 上一张
                    </button>
                    <button id="next-image" class="btn btn-default">
                        下一张 <i class="fas fa-chevron-right"></i>
                    </button>
                    <button id="download-current" class="btn btn-success">
                        <i class="fas fa-download"></i> 下载
                    </button>
                    <button id="preview-close" class="btn btn-default">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-content">
            <i class="fas fa-spinner fa-spin"></i>
            <span id="loading-text">处理中...</span>
        </div>
    </div>

    <!-- 引入脚本 -->
    <script src="scanonweb.js"></script>
    <script src="script.js"></script>
</body>
</html>
