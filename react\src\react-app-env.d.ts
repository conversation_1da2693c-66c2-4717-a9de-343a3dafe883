/// <reference types="react-scripts" />

declare module 'scanonweb' {
  export default class ScanOnWeb {
    constructor();
    
    // 配置属性
    scaner_work_config: any;
    
    // 事件回调
    onGetDevicesListEvent: (msg: any) => void;
    onScanFinishedEvent: (msg: any) => void;
    onGetAllImageEvent: (msg: any) => void;
    onGetImageByIdEvent: (msg: any) => void;
    onImageEditedEvent: (msg: any) => void;
    onUploadEvent: () => void;
    onUploadAllImageAsPdfToUrlEvent: (msg: any) => void;
    onUploadAllImageAsTiffToUrlEvent: (msg: any) => void;
    onUploadJpgImageByIndexEvent: (msg: any) => void;
    
    // 方法
    loadDevices(): void;
    selectScanDevice(index: number): void;
    startScan(): void;
    getAllImage(): void;
    clearAll(): void;
    uploadAllImageAsPdfToUrl(url: string, id: string, description: string): void;
    uploadAllImageAsTiffToUrl(url: string, id: string, description: string): void;
    uploadJpgImageByIndex(url: string, id: string, description: string, index: number): void;
    saveAllImageToLocal(filename: string): void;
  }
}
