/* 图像画廊样式 */
.image-gallery {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 画廊标题栏 */
.gallery-header {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.gallery-title {
  font-weight: 600;
  font-size: 1.1em;
}

.header-left i {
  font-size: 1.2em;
}

/* 视图切换按钮 */
.view-toggle {
  display: flex;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  overflow: hidden;
}

.toggle-btn {
  padding: 8px 12px;
  border: none;
  background: transparent;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.toggle-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.toggle-btn.active {
  background: rgba(255, 255, 255, 0.3);
}

/* 画廊内容区域 */
.gallery-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

/* 空状态 */
.empty-state {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-content {
  text-align: center;
  color: #6c757d;
}

.empty-icon {
  font-size: 4em;
  margin-bottom: 20px;
  color: #dee2e6;
}

.empty-content h3 {
  margin-bottom: 10px;
  color: #495057;
}

.empty-content p {
  margin: 0;
  font-size: 0.9em;
}

/* 网格视图 */
.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.image-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.image-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.image-wrapper {
  position: relative;
  overflow: hidden;
}

.scan-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.scan-image:hover {
  transform: scale(1.05);
}

/* 图像覆盖层 */
.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-wrapper:hover .image-overlay {
  opacity: 1;
}

.overlay-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.action-btn:hover {
  transform: scale(1.1);
}

.preview-btn {
  background: #667eea;
}

.download-btn {
  background: #28a745;
}

.delete-btn {
  background: #dc3545;
}

/* 图像信息 */
.image-info {
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.image-number {
  font-weight: 600;
  color: #495057;
}

.image-format {
  background: #e9ecef;
  color: #6c757d;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.8em;
  font-weight: 500;
}

/* 列表视图 */
.image-list {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.list-header {
  display: grid;
  grid-template-columns: 100px 1fr 100px 150px;
  gap: 20px;
  padding: 15px 20px;
  background: #f8f9fa;
  font-weight: 600;
  color: #495057;
  border-bottom: 1px solid #dee2e6;
}

.list-body {
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

.list-item {
  display: grid;
  grid-template-columns: 100px 1fr 100px 150px;
  gap: 20px;
  padding: 15px 20px;
  border-bottom: 1px solid #f1f3f4;
  align-items: center;
  transition: background-color 0.2s ease;
}

.list-item:hover {
  background-color: #f8f9fa;
}

.list-item:last-child {
  border-bottom: none;
}

.thumbnail {
  width: 80px;
  height: 60px;
  object-fit: cover;
  border-radius: 6px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.thumbnail:hover {
  transform: scale(1.1);
}

.format-tag {
  background: #667eea;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8em;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.list-action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.list-action-btn:hover {
  transform: scale(1.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .image-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
  }
  
  .gallery-content {
    padding: 15px;
  }
  
  .gallery-header {
    padding: 15px;
    flex-direction: column;
    gap: 15px;
  }
  
  .list-header,
  .list-item {
    grid-template-columns: 80px 1fr 80px 120px;
    gap: 15px;
    padding: 12px 15px;
  }
  
  .thumbnail {
    width: 60px;
    height: 45px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
  
  .list-action-btn {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .image-grid {
    grid-template-columns: 1fr;
  }
  
  .list-header,
  .list-item {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  
  .col-preview,
  .col-name,
  .col-format,
  .col-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .col-preview::before {
    content: "预览:";
    font-weight: 600;
  }
  
  .col-name::before {
    content: "名称:";
    font-weight: 600;
  }
  
  .col-format::before {
    content: "格式:";
    font-weight: 600;
  }
  
  .col-actions::before {
    content: "操作:";
    font-weight: 600;
  }
  
  .action-buttons {
    flex-direction: row;
  }
}
