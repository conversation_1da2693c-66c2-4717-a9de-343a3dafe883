# 文档扫描系统

一个完整的文档扫描解决方案，包含Vue3前端界面和Go后台服务，支持图像扫描、预览、编辑和上传功能。

## 项目结构

```
scanonwebh5_demo/
├── vue3/                 # Vue3 前端项目
│   ├── src/
│   │   ├── components/
│   │   │   ├── ScanOnWeb.vue      # 原始扫描组件
│   │   │   └── ScanOnWebNew.vue   # 新的美化扫描组件
│   │   ├── services/
│   │   │   └── api.js             # API服务模块
│   │   ├── scanonweb.js           # 扫描SDK
│   │   ├── App.vue
│   │   └── main.js
│   ├── package.json
│   └── ...
├── go/                   # Go 后台服务
│   ├── main.go          # 主服务文件
│   ├── go.mod           # Go模块配置
│   └── uploads/         # 上传文件存储目录
├── html/                # 原始HTML版本
└── README.md
```

## 功能特性

### 前端功能
- 🎨 **现代化UI设计** - 基于Element Plus的美观界面
- 📷 **设备管理** - 自动检测和选择扫描设备
- ⚙️ **扫描配置** - 分辨率、色彩模式、高级选项配置
- 🖼️ **图像预览** - 网格和列表两种查看模式
- 🔍 **图像操作** - 预览、下载、删除、旋转等操作
- 📤 **多格式上传** - 支持PDF、TIFF、JPG格式上传
- 📱 **响应式设计** - 适配不同屏幕尺寸

### 后端功能
- 🚀 **高性能服务** - 基于Gin框架的Go服务
- 📁 **文件管理** - 支持多种格式文件上传和存储
- 🔒 **CORS支持** - 跨域请求处理
- 📊 **文件列表** - 查看和管理已上传文件
- 🗑️ **文件删除** - 支持文件删除操作
- 💾 **Base64处理** - 支持Base64图像数据处理

## 快速开始

### 前置要求
- Node.js 16+ 
- Go 1.21+
- 扫描设备驱动程序
- 扫描服务程序（ScanOnWeb托盘程序）

### 1. 启动Go后台服务

```bash
# 进入Go目录
cd go

# 初始化Go模块（如果需要）
go mod tidy

# 启动服务
go run main.go
```

服务将在 `http://localhost:8080` 启动

### 2. 启动Vue3前端

```bash
# 进入Vue3目录
cd vue3

# 安装依赖
npm install

# 启动开发服务器
npm run serve
```

前端将在 `http://localhost:8081` 启动

### 3. 启动扫描服务

确保扫描服务程序（ScanOnWeb托盘程序）已启动并运行在以下端口之一：
- ws://127.0.0.1:1001
- ws://127.0.0.1:2001
- ws://127.0.0.1:3001
- ws://127.0.0.1:4001
- ws://127.0.0.1:5001

## API接口文档

### 后台API接口

#### 健康检查
```
GET /health
```

#### 上传PDF文档
```
POST /upload
Content-Type: application/json

{
  "id": "文档ID",
  "desc": "文档描述", 
  "imageData": "base64图像数据"
}
```

#### 上传TIFF图像
```
POST /upload/tiff
Content-Type: application/json

{
  "id": "文档ID",
  "desc": "文档描述",
  "imageData": "base64图像数据"
}
```

#### 上传JPG图像
```
POST /upload/jpg
Content-Type: application/json

{
  "id": "文档ID", 
  "desc": "文档描述",
  "index": 图像索引,
  "imageData": "base64图像数据"
}
```

#### 获取文件列表
```
GET /files
```

#### 删除文件
```
DELETE /files/{filename}
```

## 使用说明

### 扫描流程
1. **设备检测** - 点击"刷新设备"按钮检测可用扫描设备
2. **配置参数** - 设置分辨率、色彩模式等扫描参数
3. **开始扫描** - 点击"开始扫描"按钮进行文档扫描
4. **查看结果** - 扫描完成后自动显示图像列表
5. **图像操作** - 可以预览、下载、删除图像
6. **上传文档** - 选择格式并上传到后台服务

### 界面说明
- **配置区域** - 设置扫描参数和设备选择
- **操作按钮** - 扫描、上传、清空等功能按钮
- **结果展示** - 网格或列表模式查看扫描结果
- **预览功能** - 大图预览和图像导航

## 技术栈

### 前端
- **Vue 3** - 渐进式JavaScript框架
- **Element Plus** - Vue 3组件库
- **Axios** - HTTP客户端
- **Composition API** - Vue 3组合式API

### 后端  
- **Go** - 高性能编程语言
- **Gin** - Web框架
- **CORS** - 跨域支持

### 扫描SDK
- **ScanOnWeb** - 专业扫描控件
- **WebSocket** - 实时通信

## 开发说明

### 项目配置
- 前端开发服务器端口：8081
- 后台服务端口：8080
- WebSocket端口：1001-5001

### 文件上传
- 上传目录：`go/uploads/`
- 支持格式：PDF、TIFF、JPG
- 文件命名：`{ID}_{timestamp}.{ext}`

### 扫描配置
- 分辨率：75-1200 DPI
- 色彩模式：RGB、GRAY、BW
- 高级功能：自动进纸、双面扫描、自动纠偏等

## 故障排除

### 常见问题
1. **扫描服务连接失败**
   - 检查扫描服务程序是否启动
   - 确认WebSocket端口未被占用

2. **设备检测失败**
   - 检查扫描设备驱动是否正确安装
   - 确认设备连接正常

3. **上传失败**
   - 检查后台服务是否启动
   - 确认网络连接正常
   - 查看控制台错误信息

4. **图像显示异常**
   - 检查Base64数据格式
   - 确认图像数据完整性

## 许可证

本项目仅供学习和开发使用。

## 联系方式

如有问题或建议，请联系开发团队。
