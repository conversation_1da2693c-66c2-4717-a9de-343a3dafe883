package main

import (
	"encoding/base64"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

// UploadRequest 上传请求结构
type UploadRequest struct {
	ID          string   `json:"id"`
	Description string   `json:"desc"`
	Format      string   `json:"format"` // pdf, tiff, jpg
	ImageData   string   `json:"imageData,omitempty"`
	Images      []string `json:"images,omitempty"`
}

// UploadResponse 上传响应结构 - 匹配扫描控件期望的格式
type UploadResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	FileID  string      `json:"fileId,omitempty"`
	Path    string      `json:"path,omitempty"`
	Code    int         `json:"code,omitempty"` // 添加状态码
	Data    interface{} `json:"data,omitempty"` // 添加数据字段
}

// ImageUploadRequest 单个图像上传请求
type ImageUploadRequest struct {
	ID          string `json:"id"`
	Description string `json:"desc"`
	Index       int    `json:"index"`
	ImageData   string `json:"imageData"`
}

func main() {
	// 创建上传目录
	uploadDir := "./uploads"
	if err := os.MkdirAll(uploadDir, 0755); err != nil {
		log.Fatal("创建上传目录失败:", err)
	}

	// 创建Gin路由
	r := gin.Default()

	// 配置CORS
	config := cors.DefaultConfig()
	config.AllowAllOrigins = true
	config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	config.AllowHeaders = []string{"Origin", "Content-Length", "Content-Type", "Authorization"}
	r.Use(cors.New(config))

	// 静态文件服务
	r.Static("/uploads", "./uploads")

	// 健康检查接口
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status": "ok",
			"time":   time.Now().Format("2006-01-02 15:04:05"),
		})
	})

	// PDF格式上传接口
	r.POST("/upload", handlePDFUpload)

	// TIFF格式上传接口
	r.POST("/upload/tiff", handleTIFFUpload)

	// 为控件上传添加额外的路由
	r.POST("/upload-tiff", handleTIFFUpload)
	r.POST("/upload-jpg", handleJPGUpload)

	// 单个JPG图像上传接口
	r.POST("/upload/jpg", handleJPGUpload)

	// 获取上传文件列表
	r.GET("/files", getUploadedFiles)

	log.Println("服务器启动在端口 8080")
	log.Fatal(r.Run(":8080"))
}

// handlePDFUpload 处理PDF格式上传
func handlePDFUpload(c *gin.Context) {
	// 优先处理multipart/form-data上传（来自扫描控件）
	if c.ContentType() == "multipart/form-data" || c.Request.Header.Get("Content-Type") != "application/json" {
		// 从表单数据获取参数
		id := c.PostForm("id")
		desc := c.PostForm("desc")

		if id == "" {
			id = "scan_" + strconv.FormatInt(time.Now().Unix(), 10)
		}
		if desc == "" {
			desc = "扫描文档"
		}

		// 处理文件上传 - 控件上传的字段名是 "image"
		file, header, err := c.Request.FormFile("image")
		if err != nil {
			log.Printf("获取上传文件失败: %v", err)
			c.JSON(http.StatusBadRequest, UploadResponse{
				Success: false,
				Message: "获取上传文件失败: " + err.Error(),
				Code:    400,
			})
			return
		}
		defer file.Close()

		// 保存文件
		filename := generateFilename(id, "pdf")
		filepath := filepath.Join("./uploads", filename)

		out, err := os.Create(filepath)
		if err != nil {
			log.Printf("创建文件失败: %v", err)
			c.JSON(http.StatusInternalServerError, UploadResponse{
				Success: false,
				Message: "创建文件失败: " + err.Error(),
			})
			return
		}
		defer out.Close()

		_, err = io.Copy(out, file)
		if err != nil {
			log.Printf("保存文件失败: %v", err)
			c.JSON(http.StatusInternalServerError, UploadResponse{
				Success: false,
				Message: "保存文件失败: " + err.Error(),
			})
			return
		}

		log.Printf("PDF文件上传成功: %s (原文件名: %s, 大小: %d bytes, ID: %s)",
			filename, header.Filename, header.Size, id)

		// 返回扫描控件期望的响应格式
		c.JSON(http.StatusOK, UploadResponse{
			Success: true,
			Message: "PDF文件上传成功",
			Code:    200,
			FileID:  id,
			Path:    "/uploads/" + filename,
			Data: map[string]interface{}{
				"filename":     filename,
				"originalName": header.Filename,
				"size":         header.Size,
				"id":           id,
				"format":       "pdf",
			},
		})
		return
	}

	// 处理JSON格式的base64数据（来自前端axios请求）
	var req UploadRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, UploadResponse{
			Success: false,
			Message: "请求格式错误: " + err.Error(),
			Code:    400,
		})
		return
	}

	if req.ImageData != "" {
		filename := generateFilename(req.ID, "pdf")
		if err := saveBase64File(req.ImageData, filename); err != nil {
			c.JSON(http.StatusInternalServerError, UploadResponse{
				Success: false,
				Message: "保存文件失败: " + err.Error(),
			})
			return
		}

		log.Printf("PDF文件上传成功: %s (ID: %s)", filename, req.ID)

		c.JSON(http.StatusOK, UploadResponse{
			Success: true,
			Message: "PDF文件上传成功",
			Code:    200,
			FileID:  req.ID,
			Path:    "/uploads/" + filename,
			Data: map[string]interface{}{
				"filename": filename,
				"id":       req.ID,
				"format":   "pdf",
			},
		})
	} else {
		c.JSON(http.StatusBadRequest, UploadResponse{
			Success: false,
			Message: "缺少图像数据",
			Code:    400,
		})
	}
}

// handleTIFFUpload 处理TIFF格式上传
func handleTIFFUpload(c *gin.Context) {
	// 优先处理multipart/form-data上传（来自扫描控件）
	if c.ContentType() == "multipart/form-data" || c.Request.Header.Get("Content-Type") != "application/json" {
		// 从表单数据获取参数
		id := c.PostForm("id")
		desc := c.PostForm("desc")

		if id == "" {
			id = "scan_" + strconv.FormatInt(time.Now().Unix(), 10)
		}
		if desc == "" {
			desc = "扫描文档"
		}

		// 处理文件上传 - 控件上传的字段名是 "image"
		file, header, err := c.Request.FormFile("image")
		if err != nil {
			log.Printf("获取上传文件失败: %v", err)
			c.JSON(http.StatusBadRequest, UploadResponse{
				Success: false,
				Message: "获取上传文件失败: " + err.Error(),
			})
			return
		}
		defer file.Close()

		// 保存文件
		filename := generateFilename(id, "tiff")
		filepath := filepath.Join("./uploads", filename)

		out, err := os.Create(filepath)
		if err != nil {
			log.Printf("创建文件失败: %v", err)
			c.JSON(http.StatusInternalServerError, UploadResponse{
				Success: false,
				Message: "创建文件失败: " + err.Error(),
			})
			return
		}
		defer out.Close()

		_, err = io.Copy(out, file)
		if err != nil {
			log.Printf("保存文件失败: %v", err)
			c.JSON(http.StatusInternalServerError, UploadResponse{
				Success: false,
				Message: "保存文件失败: " + err.Error(),
			})
			return
		}

		log.Printf("TIFF文件上传成功: %s (原文件名: %s, 大小: %d bytes, ID: %s)",
			filename, header.Filename, header.Size, id)

		// 返回扫描控件期望的响应格式
		c.JSON(http.StatusOK, UploadResponse{
			Success: true,
			Message: "TIFF文件上传成功",
			Code:    200,
			FileID:  id,
			Path:    "/uploads/" + filename,
			Data: map[string]interface{}{
				"filename":     filename,
				"originalName": header.Filename,
				"size":         header.Size,
				"id":           id,
				"format":       "tiff",
			},
		})
		return
	}

	// 处理JSON格式的base64数据
	var req UploadRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, UploadResponse{
			Success: false,
			Message: "请求格式错误: " + err.Error(),
		})
		return
	}

	if req.ImageData == "" {
		c.JSON(http.StatusBadRequest, UploadResponse{
			Success: false,
			Message: "缺少图像数据",
		})
		return
	}

	filename := generateFilename(req.ID, "tiff")
	if err := saveBase64File(req.ImageData, filename); err != nil {
		c.JSON(http.StatusInternalServerError, UploadResponse{
			Success: false,
			Message: "保存文件失败: " + err.Error(),
		})
		return
	}

	log.Printf("TIFF文件上传成功: %s (ID: %s)", filename, req.ID)

	c.JSON(http.StatusOK, UploadResponse{
		Success: true,
		Message: "TIFF文件上传成功",
		FileID:  req.ID,
		Path:    "/uploads/" + filename,
	})
}

// handleJPGUpload 处理JPG格式上传
func handleJPGUpload(c *gin.Context) {
	// 优先处理multipart/form-data上传（来自扫描控件）
	if c.ContentType() == "multipart/form-data" || c.Request.Header.Get("Content-Type") != "application/json" {
		// 从表单数据获取参数
		id := c.PostForm("id")
		desc := c.PostForm("desc")
		indexStr := c.PostForm("index")

		if id == "" {
			id = "scan_" + strconv.FormatInt(time.Now().Unix(), 10)
		}
		if desc == "" {
			desc = "扫描图像"
		}

		index := 0
		if indexStr != "" {
			if i, err := strconv.Atoi(indexStr); err == nil {
				index = i
			}
		}

		// 处理文件上传 - 控件上传的字段名是 "image"
		file, header, err := c.Request.FormFile("image")
		if err != nil {
			log.Printf("获取上传文件失败: %v", err)
			c.JSON(http.StatusBadRequest, UploadResponse{
				Success: false,
				Message: "获取上传文件失败: " + err.Error(),
			})
			return
		}
		defer file.Close()

		// 保存文件
		filename := generateFilename(id+"_"+strconv.Itoa(index), "jpg")
		filepath := filepath.Join("./uploads", filename)

		out, err := os.Create(filepath)
		if err != nil {
			log.Printf("创建文件失败: %v", err)
			c.JSON(http.StatusInternalServerError, UploadResponse{
				Success: false,
				Message: "创建文件失败: " + err.Error(),
			})
			return
		}
		defer out.Close()

		_, err = io.Copy(out, file)
		if err != nil {
			log.Printf("保存文件失败: %v", err)
			c.JSON(http.StatusInternalServerError, UploadResponse{
				Success: false,
				Message: "保存文件失败: " + err.Error(),
			})
			return
		}

		log.Printf("JPG文件上传成功: %s (原文件名: %s, 大小: %d bytes, ID: %s, Index: %d)",
			filename, header.Filename, header.Size, id, index)

		// 返回扫描控件期望的响应格式
		c.JSON(http.StatusOK, UploadResponse{
			Success: true,
			Message: "JPG文件上传成功",
			Code:    200,
			FileID:  id,
			Path:    "/uploads/" + filename,
			Data: map[string]interface{}{
				"filename":     filename,
				"originalName": header.Filename,
				"size":         header.Size,
				"id":           id,
				"index":        index,
				"format":       "jpg",
			},
		})
		return
	}

	// 处理JSON格式的base64数据
	var req ImageUploadRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, UploadResponse{
			Success: false,
			Message: "请求格式错误: " + err.Error(),
		})
		return
	}

	if req.ImageData == "" {
		c.JSON(http.StatusBadRequest, UploadResponse{
			Success: false,
			Message: "缺少图像数据",
		})
		return
	}

	filename := generateFilename(req.ID+"_"+strconv.Itoa(req.Index), "jpg")
	if err := saveBase64File(req.ImageData, filename); err != nil {
		c.JSON(http.StatusInternalServerError, UploadResponse{
			Success: false,
			Message: "保存文件失败: " + err.Error(),
		})
		return
	}

	log.Printf("JPG文件上传成功: %s (ID: %s, Index: %d)", filename, req.ID, req.Index)

	c.JSON(http.StatusOK, UploadResponse{
		Success: true,
		Message: "JPG文件上传成功",
		FileID:  req.ID,
		Path:    "/uploads/" + filename,
	})
}

// saveBase64File 保存base64编码的文件
func saveBase64File(base64Data, filename string) error {
	// 解码base64数据
	data, err := base64.StdEncoding.DecodeString(base64Data)
	if err != nil {
		return fmt.Errorf("base64解码失败: %v", err)
	}

	// 保存文件
	filepath := filepath.Join("./uploads", filename)
	return os.WriteFile(filepath, data, 0644)
}

// generateFilename 生成文件名
func generateFilename(id, ext string) string {
	timestamp := time.Now().Format("20060102_150405")
	return fmt.Sprintf("%s_%s.%s", id, timestamp, ext)
}

// getUploadedFiles 获取已上传文件列表
func getUploadedFiles(c *gin.Context) {
	files, err := os.ReadDir("./uploads")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "读取文件列表失败: " + err.Error(),
		})
		return
	}

	var fileList []map[string]interface{}
	for _, file := range files {
		if !file.IsDir() {
			info, _ := file.Info()
			fileList = append(fileList, map[string]interface{}{
				"name":    file.Name(),
				"size":    info.Size(),
				"modTime": info.ModTime().Format("2006-01-02 15:04:05"),
				"url":     "/uploads/" + file.Name(),
			})
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"files":   fileList,
	})
}
