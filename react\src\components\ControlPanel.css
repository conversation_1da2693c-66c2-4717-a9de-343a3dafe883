/* 控制面板样式 */
.control-panel {
  height: 100%;
  overflow-y: auto;
  padding: 0;
}

/* 面板区域 */
.panel-section {
  border-bottom: 1px solid #e9ecef;
}

.panel-section:last-child {
  border-bottom: none;
}

/* 区域标题 */
.section-header {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  font-size: 1.1em;
}

.section-header i {
  font-size: 1.2em;
}

/* 区域内容 */
.section-content {
  padding: 20px;
}

/* 表单组 */
.form-group {
  margin-bottom: 20px;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #495057;
  font-size: 0.9em;
}

/* 表单控件 */
.form-control {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: white;
}

.form-control:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-control:disabled {
  background-color: #f8f9fa;
  color: #6c757d;
}

/* DPI输入框 */
.dpi-inputs {
  display: flex;
  align-items: center;
  gap: 10px;
}

.dpi-inputs .form-control {
  flex: 1;
}

.dpi-separator {
  font-weight: bold;
  color: #6c757d;
  font-size: 1.2em;
}

/* 复选框网格 */
.checkbox-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
  font-size: 0.9em;
}

.checkbox-item:hover {
  background-color: #f8f9fa;
}

.checkbox-item input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #667eea;
}

.checkbox-item span {
  color: #495057;
  font-weight: 500;
}

/* 按钮网格 */
.button-grid {
  display: grid;
  gap: 12px;
}

/* 按钮样式 */
.btn {
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-decoration: none;
  background: none;
  position: relative;
  overflow: hidden;
}

.btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn:active:not(:disabled) {
  transform: translateY(0);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 按钮颜色变体 */
.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-success {
  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
  color: white;
}

.btn-info {
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: white;
}

.btn-warning {
  background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
  color: white;
}

.btn-secondary {
  background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
  color: white;
}

/* 状态信息 */
.status-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-label {
  font-weight: 600;
  color: #495057;
  font-size: 0.9em;
}

.status-value {
  font-weight: 500;
  font-size: 0.9em;
}

.status-value.connected {
  color: #28a745;
}

.status-value.disconnected {
  color: #dc3545;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .checkbox-grid {
    grid-template-columns: 1fr;
  }
  
  .section-content {
    padding: 15px;
  }
  
  .section-header {
    padding: 15px;
    font-size: 1em;
  }
  
  .btn {
    padding: 10px 14px;
    font-size: 13px;
  }
}
