<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="https://jakarta.ee/xml/ns/jakartaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="https://jakarta.ee/xml/ns/jakartaee 
         https://jakarta.ee/xml/ns/jakartaee/web-app_6_0.xsd"
         version="6.0">

    <display-name>ScanOnWeb Servlet Application</display-name>
    <description>Simple Servlet backend for document scanning</description>

    <!-- 文件上传配置 -->
    <multipart-config>
        <max-file-size>52428800</max-file-size>        <!-- 50MB -->
        <max-request-size>104857600</max-request-size>  <!-- 100MB -->
        <file-size-threshold>1048576</file-size-threshold> <!-- 1MB -->
    </multipart-config>

    <!-- PDF上传Servlet -->
    <servlet>
        <servlet-name>UploadServlet</servlet-name>
        <servlet-class>com.scanonweb.servlet.UploadServlet</servlet-class>
        <multipart-config>
            <max-file-size>52428800</max-file-size>
            <max-request-size>104857600</max-request-size>
            <file-size-threshold>1048576</file-size-threshold>
        </multipart-config>
    </servlet>
    <servlet-mapping>
        <servlet-name>UploadServlet</servlet-name>
        <url-pattern>/upload</url-pattern>
    </servlet-mapping>

    <!-- TIFF上传Servlet -->
    <servlet>
        <servlet-name>TiffUploadServlet</servlet-name>
        <servlet-class>com.scanonweb.servlet.TiffUploadServlet</servlet-class>
        <multipart-config>
            <max-file-size>52428800</max-file-size>
            <max-request-size>104857600</max-request-size>
            <file-size-threshold>1048576</file-size-threshold>
        </multipart-config>
    </servlet>
    <servlet-mapping>
        <servlet-name>TiffUploadServlet</servlet-name>
        <url-pattern>/upload-tiff</url-pattern>
    </servlet-mapping>

    <!-- JPG上传Servlet -->
    <servlet>
        <servlet-name>JpgUploadServlet</servlet-name>
        <servlet-class>com.scanonweb.servlet.JpgUploadServlet</servlet-class>
        <multipart-config>
            <max-file-size>52428800</max-file-size>
            <max-request-size>104857600</max-request-size>
            <file-size-threshold>1048576</file-size-threshold>
        </multipart-config>
    </servlet>
    <servlet-mapping>
        <servlet-name>JpgUploadServlet</servlet-name>
        <url-pattern>/upload-jpg</url-pattern>
    </servlet-mapping>

    <!-- 健康检查Servlet -->
    <servlet>
        <servlet-name>HealthServlet</servlet-name>
        <servlet-class>com.scanonweb.servlet.HealthServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>HealthServlet</servlet-name>
        <url-pattern>/health</url-pattern>
    </servlet-mapping>

    <!-- 文件下载Servlet -->
    <servlet>
        <servlet-name>FileServlet</servlet-name>
        <servlet-class>com.scanonweb.servlet.FileServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>FileServlet</servlet-name>
        <url-pattern>/uploads/*</url-pattern>
    </servlet-mapping>

    <!-- CORS过滤器 -->
    <filter>
        <filter-name>CorsFilter</filter-name>
        <filter-class>com.scanonweb.filter.CorsFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>CorsFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- 欢迎页面 -->
    <welcome-file-list>
        <welcome-file>index.html</welcome-file>
    </welcome-file-list>

</web-app>
