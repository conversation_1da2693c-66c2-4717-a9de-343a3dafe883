/* 预览模态框样式 */
.preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
}

.preview-container {
  width: 95%;
  height: 95%;
  max-width: 1200px;
  max-height: 900px;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  animation: previewSlideIn 0.3s ease-out;
}

/* 预览标题栏 */
.preview-header {
  padding: 15px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.preview-title {
  font-size: 1.1em;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.preview-info {
  font-size: 0.9em;
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 12px;
  border-radius: 20px;
}

.preview-close {
  background: none;
  border: none;
  color: white;
  font-size: 1.2em;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.preview-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 预览内容区域 */
.preview-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: #f8f9fa;
  overflow: hidden;
}

/* 导航按钮 */
.nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 50px;
  height: 50px;
  background: rgba(0, 0, 0, 0.7);
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 1.2em;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-btn:hover:not(.disabled) {
  background: rgba(0, 0, 0, 0.9);
  transform: translateY(-50%) scale(1.1);
}

.nav-btn.disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.nav-prev {
  left: 20px;
}

.nav-next {
  right: 20px;
}

/* 图像容器 */
.image-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  height: 100%;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease;
}

.preview-image:hover {
  transform: scale(1.02);
}

/* 预览底部操作栏 */
.preview-footer {
  padding: 15px 20px;
  background: white;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.image-details {
  display: flex;
  gap: 20px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.9em;
  color: #6c757d;
}

.detail-item i {
  color: #667eea;
}

.preview-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.action-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.prev-btn,
.next-btn {
  background: #6c757d;
  color: white;
}

.download-btn {
  background: #28a745;
  color: white;
}

.close-btn {
  background: #dc3545;
  color: white;
}

/* 缩略图导航 */
.thumbnail-nav {
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  padding: 15px;
  flex-shrink: 0;
}

.thumbnail-list {
  display: flex;
  gap: 10px;
  overflow-x: auto;
  padding: 5px 0;
  justify-content: center;
}

.thumbnail-item {
  position: relative;
  cursor: pointer;
  border-radius: 6px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  flex-shrink: 0;
}

.thumbnail-item:hover {
  transform: scale(1.05);
}

.thumbnail-item.active {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.3);
}

.thumbnail-image {
  width: 60px;
  height: 45px;
  object-fit: cover;
  display: block;
}

.thumbnail-number {
  position: absolute;
  bottom: 2px;
  right: 2px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 0.7em;
  padding: 1px 4px;
  border-radius: 2px;
}

/* 动画效果 */
@keyframes previewSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .preview-container {
    width: 100%;
    height: 100%;
    border-radius: 0;
  }
  
  .preview-header {
    padding: 12px 15px;
  }
  
  .preview-title {
    font-size: 1em;
  }
  
  .nav-btn {
    width: 40px;
    height: 40px;
    font-size: 1em;
  }
  
  .nav-prev {
    left: 10px;
  }
  
  .nav-next {
    right: 10px;
  }
  
  .image-container {
    padding: 10px;
  }
  
  .preview-footer {
    padding: 12px 15px;
    flex-direction: column;
    gap: 15px;
  }
  
  .preview-actions {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .action-btn {
    flex: 1;
    min-width: 80px;
    justify-content: center;
  }
  
  .thumbnail-nav {
    padding: 10px;
  }
  
  .thumbnail-image {
    width: 50px;
    height: 38px;
  }
}

@media (max-width: 480px) {
  .image-details {
    flex-direction: column;
    gap: 8px;
    align-items: center;
  }
  
  .preview-actions {
    grid-template-columns: 1fr 1fr;
    gap: 8px;
  }
  
  .action-btn {
    font-size: 12px;
    padding: 6px 12px;
  }
}
