use base64::{engine::general_purpose, Engine as _};
use std::path::{Path, PathBuf};
use tokio::fs;
use tokio::io::AsyncWriteExt;
use uuid::Uuid;

const UPLOAD_DIR: &str = "./uploads";

/// 初始化上传目录
pub async fn init_upload_directory() {
    if let Err(e) = fs::create_dir_all(UPLOAD_DIR).await {
        tracing::error!("创建上传目录失败: {}", e);
        panic!("无法创建上传目录");
    }
    tracing::info!("上传目录已准备: {}", UPLOAD_DIR);
}

/// 生成文件名
pub fn generate_filename(id: Option<&str>, format: &str) -> String {
    let id = id.unwrap_or_else(|| {
        &Uuid::new_v4().to_string().replace('-', "")
    });
    
    let timestamp = chrono::Utc::now().timestamp_millis();
    format!("{}_{}.{}", id, timestamp, format)
}

/// 保存上传的文件
pub async fn save_uploaded_file(
    data: &[u8],
    id: Option<&str>,
    format: &str,
) -> Result<String, std::io::Error> {
    let filename = generate_filename(id, format);
    let file_path = Path::new(UPLOAD_DIR).join(&filename);
    
    let mut file = fs::File::create(&file_path).await?;
    file.write_all(data).await?;
    file.flush().await?;
    
    tracing::info!(
        "文件保存成功: {} (大小: {} bytes)",
        filename,
        data.len()
    );
    
    Ok(filename)
}

/// 保存Base64编码的文件
pub async fn save_base64_file(
    base64_data: &str,
    id: Option<&str>,
    format: &str,
) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
    // 解码Base64数据
    let data = general_purpose::STANDARD
        .decode(base64_data)
        .map_err(|e| format!("Base64解码失败: {}", e))?;
    
    let filename = save_uploaded_file(&data, id, format).await?;
    
    tracing::info!(
        "Base64文件保存成功: {} (大小: {} bytes)",
        filename,
        data.len()
    );
    
    Ok(filename)
}

/// 获取上传文件路径
pub fn get_upload_path(filename: &str) -> PathBuf {
    Path::new(UPLOAD_DIR).join(filename)
}

/// 验证文件类型
pub fn is_valid_file_type(extension: &str) -> bool {
    matches!(
        extension.to_lowercase().as_str(),
        "pdf" | "tiff" | "tif" | "jpg" | "jpeg" | "png"
    )
}

/// 获取文件扩展名
pub fn get_file_extension(filename: &str) -> Option<&str> {
    Path::new(filename)
        .extension()
        .and_then(|ext| ext.to_str())
}

/// 列出上传目录中的所有文件
pub async fn list_upload_files() -> Result<Vec<crate::models::FileInfo>, std::io::Error> {
    let mut files = Vec::new();
    let mut entries = fs::read_dir(UPLOAD_DIR).await?;
    
    while let Some(entry) = entries.next_entry().await? {
        let path = entry.path();
        
        if path.is_file() {
            if let Some(filename) = path.file_name().and_then(|n| n.to_str()) {
                let metadata = entry.metadata().await?;
                let size = metadata.len();
                
                // 获取修改时间
                let mod_time = metadata
                    .modified()
                    .map(|time| {
                        let datetime: chrono::DateTime<chrono::Utc> = time.into();
                        datetime.format("%Y-%m-%d %H:%M:%S").to_string()
                    })
                    .unwrap_or_else(|_| "Unknown".to_string());
                
                // 获取文件类型
                let file_type = get_file_extension(filename)
                    .unwrap_or("unknown")
                    .to_string();
                
                let file_info = crate::models::FileInfo::new(
                    filename.to_string(),
                    size,
                    mod_time,
                    file_type,
                );
                
                files.push(file_info);
            }
        }
    }
    
    // 按修改时间排序（最新的在前）
    files.sort_by(|a, b| b.mod_time.cmp(&a.mod_time));
    
    Ok(files)
}

/// 删除文件
pub async fn delete_file(filename: &str) -> Result<bool, std::io::Error> {
    let file_path = get_upload_path(filename);
    
    if file_path.exists() {
        fs::remove_file(&file_path).await?;
        tracing::info!("文件删除成功: {}", filename);
        Ok(true)
    } else {
        tracing::warn!("文件不存在: {}", filename);
        Ok(false)
    }
}
