/* 扫描应用主容器 */
.scanner-app {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

/* 页面标题 */
.app-header {
  text-align: center;
  margin-bottom: 20px;
  padding: 30px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.app-title {
  font-size: 2.5em;
  font-weight: 300;
  color: #2c3e50;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.app-title i {
  color: #667eea;
}

.app-subtitle {
  font-size: 1.1em;
  color: #7f8c8d;
  margin: 10px 0 0 0;
  font-weight: 400;
}

/* 连接状态指示器 */
.connection-alert {
  margin-bottom: 20px;
}

.alert {
  padding: 15px 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 15px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.alert-warning {
  border-left: 4px solid #f39c12;
  color: #e67e22;
}

.alert-content strong {
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
}

.alert-content p {
  margin: 0;
  opacity: 0.8;
}

/* 主要内容区域 - 左右布局 */
.main-content {
  display: flex;
  gap: 20px;
  min-height: calc(100vh - 200px);
}

/* 左侧控制面板 */
.left-panel {
  flex: 0 0 400px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  overflow: hidden;
}

/* 右侧图像展示区域 */
.right-panel {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .main-content {
    flex-direction: column;
  }
  
  .left-panel {
    flex: none;
  }
  
  .app-title {
    font-size: 2em;
    flex-direction: column;
    gap: 10px;
  }
}

@media (max-width: 768px) {
  .scanner-app {
    padding: 10px;
  }
  
  .app-header {
    padding: 20px;
  }
  
  .app-title {
    font-size: 1.8em;
  }
  
  .app-subtitle {
    font-size: 1em;
  }
  
  .main-content {
    gap: 15px;
  }
}
