@echo off
chcp 65001 >nul
echo ================================
echo ScanOnWeb Servlet Server
echo ================================

REM Check Java environment
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Java not found, please install Java 17 or higher
    pause
    exit /b 1
)

REM Check Maven environment
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Maven not found, please install Maven
    pause
    exit /b 1
)

echo Compiling project...
call mvn clean compile

if %errorlevel% neq 0 (
    echo Compilation failed, please check error messages
    pause
    exit /b 1
)

echo Starting Servlet server...
echo Access URL: http://localhost:8080
echo Press Ctrl+C to stop server

call mvn exec:java -Dexec.mainClass="com.scanonweb.ScanOnWebServer"

pause
