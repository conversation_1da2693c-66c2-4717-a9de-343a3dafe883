use axum::{
    extract::{Multipart, Request},
    http::{HeaderMap, StatusCode},
    response::J<PERSON>,
    body::Body,
};
use bytes::Bytes;
use std::collections::HashMap;

use crate::models::{UploadRequest, UploadResponse};
use crate::utils::file_utils;

/// PDF上传处理器
pub async fn upload_pdf(
    headers: HeaderMap,
    request: Request,
) -> Result<Json<UploadResponse>, (StatusCode, Json<UploadResponse>)> {
    handle_upload_request(headers, request, "pdf").await
}

/// TIFF上传处理器
pub async fn upload_tiff(
    headers: HeaderMap,
    request: Request,
) -> Result<Json<UploadResponse>, (StatusCode, Json<UploadResponse>)> {
    handle_upload_request(headers, request, "tiff").await
}

/// JPG上传处理器
pub async fn upload_jpg(
    headers: HeaderMap,
    request: Request,
) -> Result<Json<UploadResponse>, (StatusCode, Json<UploadResponse>)> {
    handle_upload_request(headers, request, "jpg").await
}

/// 通用上传处理函数
async fn handle_upload_request(
    headers: HeaderMap,
    request: Request,
    format: &str,
) -> Result<Json<UploadResponse>, (StatusCode, Json<UploadResponse>)> {
    let content_type = headers
        .get("content-type")
        .and_then(|v| v.to_str().ok())
        .unwrap_or("");

    tracing::info!("收到{}上传请求, Content-Type: {}", format.to_uppercase(), content_type);

    if content_type.starts_with("multipart/form-data") {
        // 将Request转换为Multipart
        let multipart = Multipart::from_request(request, &()).await.map_err(|e| {
            let response = UploadResponse::error(format!("解析multipart失败: {}", e), 400);
            (StatusCode::BAD_REQUEST, Json(response))
        })?;
        handle_multipart_upload(multipart, format).await
    } else if content_type.starts_with("application/json") {
        // 读取body数据
        let body = axum::body::to_bytes(request.into_body(), usize::MAX).await.map_err(|e| {
            let response = UploadResponse::error(format!("读取请求体失败: {}", e), 400);
            (StatusCode::BAD_REQUEST, Json(response))
        })?;
        handle_json_upload(body, format).await
    } else {
        let response = UploadResponse::error(
            format!("不支持的Content-Type: {}", content_type),
            400,
        );
        Err((StatusCode::BAD_REQUEST, Json(response)))
    }
}

/// 处理multipart/form-data上传
async fn handle_multipart_upload(
    mut multipart: Multipart,
    format: &str,
) -> Result<Json<UploadResponse>, (StatusCode, Json<UploadResponse>)> {
    let mut file_data: Option<Vec<u8>> = None;
    let mut id: Option<String> = None;
    let mut description: Option<String> = None;
    let mut index: Option<u32> = None;
    let mut original_filename: Option<String> = None;

    // 解析multipart字段
    while let Some(field) = multipart.next_field().await.map_err(|e| {
        let response = UploadResponse::error(format!("解析multipart失败: {}", e), 400);
        (StatusCode::BAD_REQUEST, Json(response))
    })? {
        let name = field.name().unwrap_or("").to_string();

        match name.as_str() {
            "image" => {
                original_filename = field.file_name().map(|s| s.to_string());
                let data = field.bytes().await.map_err(|e| {
                    let response = UploadResponse::error(format!("读取文件数据失败: {}", e), 400);
                    (StatusCode::BAD_REQUEST, Json(response))
                })?;
                file_data = Some(data.to_vec());
            }
            "id" => {
                let data = field.bytes().await.map_err(|e| {
                    let response = UploadResponse::error(format!("读取ID失败: {}", e), 400);
                    (StatusCode::BAD_REQUEST, Json(response))
                })?;
                id = Some(String::from_utf8_lossy(&data).to_string());
            }
            "desc" => {
                let data = field.bytes().await.map_err(|e| {
                    let response = UploadResponse::error(format!("读取描述失败: {}", e), 400);
                    (StatusCode::BAD_REQUEST, Json(response))
                })?;
                description = Some(String::from_utf8_lossy(&data).to_string());
            }
            "index" => {
                let data = field.bytes().await.map_err(|e| {
                    let response = UploadResponse::error(format!("读取索引失败: {}", e), 400);
                    (StatusCode::BAD_REQUEST, Json(response))
                })?;
                if let Ok(idx_str) = String::from_utf8(data.to_vec()) {
                    index = idx_str.parse().ok();
                }
            }
            _ => {
                // 忽略其他字段
                let _ = field.bytes().await;
            }
        }
    }

    // 检查是否有文件数据
    let file_data = file_data.ok_or_else(|| {
        let response = UploadResponse::error("缺少上传文件".to_string(), 400);
        (StatusCode::BAD_REQUEST, Json(response))
    })?;

    // 处理文件ID
    let mut file_id = id.unwrap_or_else(|| format!("scan_{}", chrono::Utc::now().timestamp_millis()));
    if let Some(idx) = index {
        file_id = format!("{}_{}", file_id, idx);
    }

    // 保存文件
    let filename = file_utils::save_uploaded_file(&file_data, Some(&file_id), format)
        .await
        .map_err(|e| {
            let response = UploadResponse::error(format!("保存文件失败: {}", e), 500);
            (StatusCode::INTERNAL_SERVER_ERROR, Json(response))
        })?;

    // 构建响应数据
    let mut data = HashMap::new();
    data.insert("filename".to_string(), serde_json::Value::String(filename.clone()));
    data.insert("id".to_string(), serde_json::Value::String(file_id.clone()));
    data.insert("format".to_string(), serde_json::Value::String(format.to_string()));
    data.insert("size".to_string(), serde_json::Value::Number(file_data.len().into()));
    
    if let Some(orig_name) = original_filename {
        data.insert("originalName".to_string(), serde_json::Value::String(orig_name));
    }
    if let Some(idx) = index {
        data.insert("index".to_string(), serde_json::Value::Number(idx.into()));
    }

    let message = format!("{}文件上传成功", format.to_uppercase());
    let path = format!("/uploads/{}", filename);

    let response = UploadResponse::success(message, file_id, path, data);
    Ok(Json(response))
}

/// 处理JSON上传
async fn handle_json_upload(
    body: Bytes,
    format: &str,
) -> Result<Json<UploadResponse>, (StatusCode, Json<UploadResponse>)> {
    // 解析JSON数据
    let upload_request: UploadRequest = serde_json::from_slice(&body).map_err(|e| {
        let response = UploadResponse::error(format!("JSON解析失败: {}", e), 400);
        (StatusCode::BAD_REQUEST, Json(response))
    })?;

    if upload_request.image_data.is_empty() {
        let response = UploadResponse::error("缺少图像数据".to_string(), 400);
        return Err((StatusCode::BAD_REQUEST, Json(response)));
    }

    // 处理文件ID
    let mut file_id = upload_request.id;
    if let Some(index) = upload_request.index {
        file_id = format!("{}_{}", file_id, index);
    }

    // 保存Base64文件
    let filename = file_utils::save_base64_file(&upload_request.image_data, Some(&file_id), format)
        .await
        .map_err(|e| {
            let response = UploadResponse::error(format!("保存文件失败: {}", e), 500);
            (StatusCode::INTERNAL_SERVER_ERROR, Json(response))
        })?;

    // 构建响应数据
    let mut data = HashMap::new();
    data.insert("filename".to_string(), serde_json::Value::String(filename.clone()));
    data.insert("id".to_string(), serde_json::Value::String(file_id.clone()));
    data.insert("format".to_string(), serde_json::Value::String(format.to_string()));
    
    if let Some(index) = upload_request.index {
        data.insert("index".to_string(), serde_json::Value::Number(index.into()));
    }

    let message = format!("{}文件上传成功", format.to_uppercase());
    let path = format!("/uploads/{}", filename);

    let response = UploadResponse::success(message, file_id, path, data);
    Ok(Json(response))
}


