import React, { useState } from 'react';
import './ImageGallery.css';
import { ScanImage } from './ScannerApp';

interface ImageGalleryProps {
  images: ScanImage[];
  onPreview: (index: number) => void;
  onDownload: (index: number) => void;
  onDelete: (index: number) => void;
}

const ImageGallery: React.FC<ImageGalleryProps> = ({
  images,
  onPreview,
  onDownload,
  onDelete
}) => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  return (
    <div className="image-gallery">
      {/* 图库标题栏 */}
      <div className="gallery-header">
        <div className="header-left">
          <i className="fas fa-images"></i>
          <span className="gallery-title">
            扫描结果 ({images.length} 张图像)
          </span>
        </div>
        <div className="header-right">
          <div className="view-toggle">
            <button
              className={`toggle-btn ${viewMode === 'grid' ? 'active' : ''}`}
              onClick={() => setViewMode('grid')}
              title="网格视图"
            >
              <i className="fas fa-th"></i>
            </button>
            <button
              className={`toggle-btn ${viewMode === 'list' ? 'active' : ''}`}
              onClick={() => setViewMode('list')}
              title="列表视图"
            >
              <i className="fas fa-list"></i>
            </button>
          </div>
        </div>
      </div>

      {/* 图像内容区域 */}
      <div className="gallery-content">
        {images.length === 0 ? (
          <div className="empty-state">
            <div className="empty-content">
              <i className="fas fa-images empty-icon"></i>
              <h3>暂无扫描结果</h3>
              <p>请使用左侧控制面板开始扫描文档</p>
            </div>
          </div>
        ) : (
          <>
            {viewMode === 'grid' ? (
              <div className="image-grid">
                {images.map((image, index) => (
                  <div key={index} className="image-card">
                    <div className="image-wrapper">
                      <img
                        src={image.src}
                        alt={`扫描图像 ${index + 1}`}
                        className="scan-image"
                        onClick={() => onPreview(index)}
                      />
                      <div className="image-overlay">
                        <div className="overlay-actions">
                          <button
                            className="action-btn preview-btn"
                            onClick={() => onPreview(index)}
                            title="预览"
                          >
                            <i className="fas fa-eye"></i>
                          </button>
                          <button
                            className="action-btn download-btn"
                            onClick={() => onDownload(index)}
                            title="下载"
                          >
                            <i className="fas fa-download"></i>
                          </button>
                          <button
                            className="action-btn delete-btn"
                            onClick={() => onDelete(index)}
                            title="删除"
                          >
                            <i className="fas fa-trash"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                    <div className="image-info">
                      <span className="image-number">图像 {index + 1}</span>
                      <span className="image-format">JPEG</span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="image-list">
                <div className="list-header">
                  <div className="col-preview">预览</div>
                  <div className="col-name">名称</div>
                  <div className="col-format">格式</div>
                  <div className="col-actions">操作</div>
                </div>
                <div className="list-body">
                  {images.map((image, index) => (
                    <div key={index} className="list-item">
                      <div className="col-preview">
                        <img
                          src={image.src}
                          alt={`预览 ${index + 1}`}
                          className="thumbnail"
                          onClick={() => onPreview(index)}
                        />
                      </div>
                      <div className="col-name">
                        图像 {index + 1}
                      </div>
                      <div className="col-format">
                        <span className="format-tag">JPEG</span>
                      </div>
                      <div className="col-actions">
                        <div className="action-buttons">
                          <button
                            className="list-action-btn preview-btn"
                            onClick={() => onPreview(index)}
                            title="预览"
                          >
                            <i className="fas fa-eye"></i>
                          </button>
                          <button
                            className="list-action-btn download-btn"
                            onClick={() => onDownload(index)}
                            title="下载"
                          >
                            <i className="fas fa-download"></i>
                          </button>
                          <button
                            className="list-action-btn delete-btn"
                            onClick={() => onDelete(index)}
                            title="删除"
                          >
                            <i className="fas fa-trash"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default ImageGallery;
