# 文档扫描系统 - React版本

这是一个基于React + TypeScript开发的文档扫描系统，采用左右布局设计，与Vue3版本功能完全一致但界面风格不同。

## 功能特性

- 🚀 **React + TypeScript**：现代化的React开发技术栈
- 📱 **左右布局设计**：左侧控制面板，右侧图像展示
- 🎨 **渐变背景**：美观的渐变色背景和毛玻璃效果
- 🔌 **设备管理**：自动检测和选择扫描设备
- ⚙️ **扫描配置**：支持分辨率、色彩模式、高级选项设置
- 📸 **图像预览**：网格和列表两种视图模式，支持全屏预览
- 📤 **文件上传**：支持PDF、TIFF、JPG格式上传
- 💾 **本地保存**：支持将扫描结果保存到本地
- 🔔 **消息提示**：优雅的消息提示系统
- 📱 **响应式设计**：支持桌面和移动设备

## 技术栈

- **React 18.2.0** - 前端框架
- **TypeScript** - 类型安全
- **CSS3** - 样式和动画
- **scanonweb** - 扫描控件npm包

## 项目结构

```
react/
├── public/
│   ├── index.html          # 主HTML文件
│   ├── manifest.json       # PWA配置
│   └── favicon.ico         # 图标
├── src/
│   ├── components/         # React组件
│   │   ├── ScannerApp.tsx     # 主应用组件
│   │   ├── ControlPanel.tsx   # 左侧控制面板
│   │   ├── ImageGallery.tsx   # 右侧图像展示
│   │   ├── UploadModal.tsx    # 上传对话框
│   │   ├── PreviewModal.tsx   # 图像预览对话框
│   │   ├── MessageToast.tsx   # 消息提示组件
│   │   └── *.css             # 对应的样式文件
│   ├── App.tsx             # 应用入口组件
│   ├── index.tsx           # 应用入口文件
│   ├── index.css           # 全局样式
│   └── react-app-env.d.ts  # TypeScript声明
├── package.json            # 项目配置
├── tsconfig.json          # TypeScript配置
└── README.md              # 说明文档
```

## 安装和运行

### 1. 安装依赖

```bash
cd react
npm install
```

### 2. 启动开发服务器

```bash
npm start
```

应用将在 http://localhost:3000 启动

### 3. 构建生产版本

```bash
npm run build
```

构建文件将生成在 `build/` 目录

## 使用方法

### 1. 启动后台服务

首先需要启动一个后台服务来处理文件上传：

- **Go版本**：`cd go && go run main.go`
- **Java Servlet版本**：部署到Tomcat等服务器
- **Java SpringBoot版本**：`cd java/springboot && mvn spring-boot:run`
- **Rust版本**：`cd rust && cargo run`

### 2. 启动扫描服务

确保扫描服务程序已启动并运行在以下端口之一：
- ws://127.0.0.1:1001
- ws://127.0.0.1:2001
- ws://127.0.0.1:3001
- ws://127.0.0.1:4001
- ws://127.0.0.1:5001

### 3. 操作流程

1. **连接检查**：页面加载后会自动尝试连接扫描服务
2. **设备配置**：在左侧控制面板选择扫描设备和配置参数
3. **开始扫描**：点击"开始扫描"按钮进行文档扫描
4. **查看结果**：扫描完成后在右侧查看扫描结果
5. **图像操作**：
   - 预览：点击图像查看大图
   - 下载：下载单个图像到本地
   - 删除：删除不需要的图像
6. **文档上传**：点击"上传文档"将扫描结果上传到服务器
7. **本地保存**：点击"本地保存"将所有图像保存为PDF文件

## 界面特色

### 左右布局设计
- **左侧控制面板**：包含设备配置、高级选项、操作按钮和状态信息
- **右侧图像展示**：支持网格和列表两种视图模式

### 视觉效果
- **渐变背景**：紫色渐变背景，营造专业感
- **毛玻璃效果**：半透明背景和模糊效果
- **流畅动画**：按钮悬停、图像预览等交互动画
- **响应式设计**：自适应不同屏幕尺寸

### 交互体验
- **键盘支持**：预览模式支持方向键切换图像，ESC键关闭
- **消息提示**：右上角优雅的消息提示
- **加载状态**：各种操作的加载状态指示
- **确认对话框**：重要操作的确认提示

## 上传接口

系统调用以下后台接口进行文件上传：

- **PDF上传**：`POST http://localhost:8080/upload`
- **TIFF上传**：`POST http://localhost:8080/upload-tiff`
- **JPG上传**：`POST http://localhost:8080/upload-jpg`

上传时会发送以下参数：
- `id`：文档ID
- `desc`：文档描述
- `image`：图像数据（字段名为'image'，不是'file'）

## 与Vue3版本的区别

1. **技术栈**：
   - Vue3版本：Vue3 + Element Plus + Composition API
   - React版本：React + TypeScript + 自定义组件

2. **界面布局**：
   - Vue3版本：传统的上下布局
   - React版本：左右布局设计

3. **视觉风格**：
   - Vue3版本：Element Plus组件库风格
   - React版本：渐变背景 + 毛玻璃效果

4. **功能**：
   - 两个版本功能完全一致
   - 都使用相同的scanonweb npm包
   - 都调用相同的后台接口

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 开发说明

### 组件结构
- `ScannerApp`：主应用组件，管理全局状态
- `ControlPanel`：左侧控制面板，处理扫描配置
- `ImageGallery`：右侧图像展示，支持多种视图
- `UploadModal`：上传对话框，处理文件上传
- `PreviewModal`：图像预览对话框，支持键盘操作
- `MessageToast`：消息提示组件，显示操作反馈

### 状态管理
使用React Hooks进行状态管理：
- `useState`：组件状态
- `useEffect`：生命周期和副作用
- `useRef`：DOM引用和实例引用

### 样式设计
- 使用CSS3的渐变、阴影、模糊等效果
- 响应式设计，支持移动设备
- 流畅的过渡动画和交互效果

## 故障排除

### 连接问题
- 检查扫描服务是否启动
- 检查WebSocket端口是否被占用
- 尝试刷新页面重新连接

### 编译问题
- 确保Node.js版本 >= 14
- 删除node_modules重新安装依赖
- 检查TypeScript配置

### 上传问题
- 检查后台服务是否启动
- 检查网络连接是否正常
- 查看浏览器控制台错误信息

## 技术支持

如需更多帮助，请访问：https://www.brainysoft.cn
