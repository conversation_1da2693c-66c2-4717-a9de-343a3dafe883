package com.scanonweb.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 上传请求DTO
 */
@Data
public class UploadRequest {

    /**
     * 文档ID
     */
    @NotBlank(message = "文档ID不能为空")
    private String id;

    /**
     * 文档描述
     */
    @JsonProperty("desc")
    private String description;

    /**
     * 文件格式
     */
    private String format;

    /**
     * Base64编码的图像数据
     */
    @JsonProperty("imageData")
    private String imageData;

    /**
     * 图像索引（用于JPG上传）
     */
    private Integer index;
}
