package com.scanonweb.exception;

import com.scanonweb.dto.UploadResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import java.util.HashMap;
import java.util.Map;

/**
 * 全局异常处理器
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理文件大小超限异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ResponseEntity<UploadResponse> handleMaxUploadSizeExceeded(MaxUploadSizeExceededException e) {
        log.error("文件大小超限", e);
        return ResponseEntity.status(HttpStatus.PAYLOAD_TOO_LARGE)
                .body(UploadResponse.error("文件大小超过限制", 413));
    }

    /**
     * 处理参数验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<UploadResponse> handleValidationException(MethodArgumentNotValidException e) {
        log.error("参数验证失败", e);
        
        StringBuilder message = new StringBuilder("参数验证失败: ");
        for (FieldError error : e.getBindingResult().getFieldErrors()) {
            message.append(error.getField()).append(" ").append(error.getDefaultMessage()).append("; ");
        }
        
        return ResponseEntity.badRequest()
                .body(UploadResponse.error(message.toString()));
    }

    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<UploadResponse> handleBindException(BindException e) {
        log.error("数据绑定失败", e);
        
        StringBuilder message = new StringBuilder("数据绑定失败: ");
        for (FieldError error : e.getBindingResult().getFieldErrors()) {
            message.append(error.getField()).append(" ").append(error.getDefaultMessage()).append("; ");
        }
        
        return ResponseEntity.badRequest()
                .body(UploadResponse.error(message.toString()));
    }

    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<UploadResponse> handleIllegalArgumentException(IllegalArgumentException e) {
        log.error("非法参数", e);
        return ResponseEntity.badRequest()
                .body(UploadResponse.error("参数错误: " + e.getMessage()));
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<UploadResponse> handleRuntimeException(RuntimeException e) {
        log.error("运行时异常", e);
        return ResponseEntity.internalServerError()
                .body(UploadResponse.error("服务器内部错误: " + e.getMessage(), 500));
    }

    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, Object>> handleGenericException(Exception e) {
        log.error("未处理的异常", e);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("message", "服务器内部错误: " + e.getMessage());
        response.put("code", 500);
        
        return ResponseEntity.internalServerError().body(response);
    }
}
