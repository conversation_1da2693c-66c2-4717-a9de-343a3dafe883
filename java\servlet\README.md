# ScanOnWeb Servlet Server

简单的Servlet后台服务，用于接收Vue3前端页面提交的上传数据。设计简洁，功能完整，完全兼容Go和SpringBoot版本的API。

## 🎯 设计理念

- **简单易懂**: 使用原生Servlet，代码结构清晰
- **功能完整**: 支持PDF、TIFF、JPG格式文件上传
- **API兼容**: 与Go和SpringBoot版本完全兼容
- **轻量级**: 最小化依赖，快速启动

## ✅ 功能特性

- 📤 支持PDF、TIFF、JPG格式文件上传
- 🔄 支持multipart/form-data和JSON两种上传方式
- 🌐 CORS跨域支持
- 📁 文件下载功能
- 💊 健康检查接口
- 🚀 内嵌Tomcat，独立运行

## 🔧 技术栈

- **Java**: 17+
- **Servlet**: Jakarta EE 6.0
- **Tomcat**: 10.1.16 (内嵌)
- **Maven**: 3.6+
- **Jackson**: JSON处理
- **Commons**: 文件处理工具

## 🚀 快速开始

### 环境要求

- Java 17 或更高版本
- Maven 3.6 或更高版本

### 启动方式

#### 方式1: 使用启动脚本（推荐）
```bash
# Windows
start.bat

# Linux/Mac
chmod +x start.sh
./start.sh
```

#### 方式2: 使用Maven命令
```bash
# 编译项目
mvn clean compile

# 启动服务
mvn exec:java -Dexec.mainClass="com.scanonweb.ScanOnWebServer"
```

#### 方式3: 打包部署
```bash
# 打包WAR文件
mvn clean package

# 部署到Tomcat
cp target/scanonweb-servlet.war $TOMCAT_HOME/webapps/
```

### 访问地址

- **服务地址**: http://localhost:8080
- **健康检查**: http://localhost:8080/health
- **测试页面**: http://localhost:8080

## 📋 API接口

### 1. 健康检查
```
GET /health
```

### 2. PDF上传
```
POST /upload
Content-Type: multipart/form-data 或 application/json

# multipart方式（扫描控件）
FormData:
- image: 文件
- id: 文档ID
- desc: 描述

# JSON方式（前端axios）
{
  "id": "文档ID",
  "desc": "描述",
  "imageData": "base64编码的图像数据"
}
```

### 3. TIFF上传
```
POST /upload-tiff
# 参数同PDF上传
```

### 4. JPG上传
```
POST /upload-jpg
# 参数同PDF上传，额外支持index参数
```

### 5. 文件下载
```
GET /uploads/{filename}
```

## 📊 响应格式

### 成功响应
```json
{
  "success": true,
  "message": "PDF文件上传成功",
  "code": 200,
  "fileId": "scan_123456",
  "path": "/uploads/scan_123456_1701234567890.pdf",
  "data": {
    "filename": "scan_123456_1701234567890.pdf",
    "originalName": "document.pdf",
    "size": 1024000,
    "id": "scan_123456",
    "format": "pdf"
  }
}
```

### 错误响应
```json
{
  "success": false,
  "message": "缺少上传文件",
  "code": 400
}
```

## 📁 项目结构

```
java/servlet/
├── src/main/java/com/scanonweb/
│   ├── ScanOnWebServer.java          # 服务器启动器
│   ├── servlet/                      # Servlet类
│   │   ├── BaseUploadServlet.java    # 基础上传Servlet
│   │   ├── UploadServlet.java        # PDF上传
│   │   ├── TiffUploadServlet.java    # TIFF上传
│   │   ├── JpgUploadServlet.java     # JPG上传
│   │   ├── HealthServlet.java        # 健康检查
│   │   └── FileServlet.java          # 文件下载
│   ├── filter/                       # 过滤器
│   │   └── CorsFilter.java           # CORS过滤器
│   └── util/                         # 工具类
│       ├── ResponseUtil.java         # 响应工具
│       └── FileUtil.java             # 文件工具
├── src/main/webapp/
│   ├── WEB-INF/web.xml              # Web配置
│   └── index.html                   # 测试页面
├── pom.xml                          # Maven配置
├── start.bat                        # Windows启动脚本
└── README.md                        # 说明文档
```

## 🔄 版本对比

| 特性 | Servlet版本 | SpringBoot版本 | Go版本 |
|------|------------|---------------|--------|
| 复杂度 | ⭐ 简单 | ⭐⭐⭐ 复杂 | ⭐⭐ 中等 |
| 启动速度 | ⭐⭐⭐ 快 | ⭐⭐ 中等 | ⭐⭐⭐ 快 |
| 内存占用 | ⭐⭐⭐ 低 | ⭐⭐ 中等 | ⭐⭐⭐ 低 |
| 功能完整性 | ⭐⭐⭐ 完整 | ⭐⭐⭐ 完整 | ⭐⭐⭐ 完整 |
| API兼容性 | ✅ 完全兼容 | ✅ 完全兼容 | ✅ 基准版本 |

## 🎯 适用场景

### 选择Servlet版本的理由：
- ✅ 需要简单易懂的代码结构
- ✅ 对启动速度有要求
- ✅ 内存资源有限
- ✅ 不需要复杂的企业级功能
- ✅ 学习Servlet开发

### 选择SpringBoot版本的理由：
- ✅ 需要企业级功能
- ✅ 复杂的业务逻辑
- ✅ 团队熟悉Spring生态
- ✅ 需要更多的扩展性

### 选择Go版本的理由：
- ✅ 极致的性能要求
- ✅ 最小的资源占用
- ✅ 简单的部署需求

## 🛠️ 开发说明

### 添加新的上传格式

1. 继承`BaseUploadServlet`类
2. 实现`getFileFormat()`方法
3. 在`web.xml`中配置Servlet映射

示例：
```java
public class PngUploadServlet extends BaseUploadServlet {
    @Override
    protected String getFileFormat() {
        return "png";
    }
}
```

### 自定义配置

修改`FileUtil.java`中的配置常量：
```java
private static final String UPLOAD_DIR = "./uploads";
```

## 🔧 故障排除

### 常见问题

1. **端口冲突**: 修改`ScanOnWebServer.java`中的端口号
2. **文件上传失败**: 检查`uploads`目录权限
3. **跨域问题**: 检查`CorsFilter`配置
4. **内存不足**: 调整JVM参数 `-Xmx512m`

### 日志查看

服务器日志直接输出到控制台，便于调试。

## 📄 许可证

MIT License

---

**简单、高效、兼容** - ScanOnWeb Servlet版本为您提供最直接的文档上传解决方案！
