package com.scanonweb;

import org.apache.catalina.Context;
import org.apache.catalina.LifecycleException;
import org.apache.catalina.startup.Tomcat;

import java.io.File;

/**
 * ScanOnWeb Servlet服务器启动器
 */
public class ScanOnWebServer {
    
    public static void main(String[] args) {
        try {
            // 创建Tomcat实例
            Tomcat tomcat = new Tomcat();
            tomcat.setPort(8080);
            tomcat.getConnector(); // 触发连接器创建
            
            // 设置工作目录
            String webappDirLocation = "src/main/webapp/";
            Context ctx = tomcat.addWebapp("", new File(webappDirLocation).getAbsolutePath());
            
            // 设置类加载器
            ctx.setParentClassLoader(Thread.currentThread().getContextClassLoader());
            
            System.out.println("================================");
            System.out.println("ScanOnWeb Servlet Server");
            System.out.println("================================");
            System.out.println("正在启动服务器...");
            
            // 启动Tomcat
            tomcat.start();
            
            System.out.println("✅ 服务器启动成功!");
            System.out.println("访问地址: http://localhost:8080");
            System.out.println("健康检查: http://localhost:8080/health");
            System.out.println("按 Ctrl+C 停止服务器");
            System.out.println("================================");
            
            // 等待服务器关闭
            tomcat.getServer().await();
            
        } catch (LifecycleException e) {
            System.err.println("❌ 服务器启动失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
