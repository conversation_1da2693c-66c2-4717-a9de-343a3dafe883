using Microsoft.AspNetCore.Mvc;
using ScanOnWeb.Models;
using ScanOnWeb.Services;

namespace ScanOnWeb.Controllers;

/// <summary>
/// 文件管理控制器
/// </summary>
[ApiController]
[Route("")]
public class FileController : ControllerBase
{
    private readonly IFileService _fileService;
    private readonly ILogger<FileController> _logger;

    public FileController(IFileService fileService, ILogger<FileController> logger)
    {
        _fileService = fileService;
        _logger = logger;
    }

    /// <summary>
    /// 文件下载
    /// </summary>
    [HttpGet("uploads/{filename}")]
    public async Task<IActionResult> DownloadFile(string filename)
    {
        if (string.IsNullOrWhiteSpace(filename))
        {
            return BadRequest("文件名不能为空");
        }

        if (!_fileService.FileExists(filename))
        {
            return NotFound("文件不存在");
        }

        var filePath = _fileService.GetFilePath(filename);
        var fileBytes = await System.IO.File.ReadAllBytesAsync(filePath);

        // 确定Content-Type
        var contentType = GetContentType(filename);

        return File(fileBytes, contentType, filename);
    }

    /// <summary>
    /// 获取文件列表
    /// </summary>
    [HttpGet("files")]
    public async Task<ActionResult<FileListResponse>> GetFileList()
    {
        try
        {
            var files = await _fileService.GetFileListAsync();
            var response = new FileListResponse
            {
                Success = true,
                Files = files,
                Count = files.Count
            };

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取文件列表失败");
            var response = new FileListResponse
            {
                Success = false,
                Files = new List<Models.FileInfo>(),
                Count = 0
            };

            return StatusCode(500, response);
        }
    }

    /// <summary>
    /// 删除文件
    /// </summary>
    [HttpDelete("files/{filename}")]
    public async Task<ActionResult<DeleteResponse>> DeleteFile(string filename)
    {
        if (string.IsNullOrWhiteSpace(filename))
        {
            var response = new DeleteResponse
            {
                Success = false,
                Message = "文件名不能为空"
            };
            return BadRequest(response);
        }

        try
        {
            var deleted = await _fileService.DeleteFileAsync(filename);

            if (deleted)
            {
                var response = new DeleteResponse
                {
                    Success = true,
                    Message = "文件删除成功"
                };
                return Ok(response);
            }
            else
            {
                var response = new DeleteResponse
                {
                    Success = false,
                    Message = "文件不存在"
                };
                return NotFound(response);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "删除文件失败: {Filename}", filename);
            var response = new DeleteResponse
            {
                Success = false,
                Message = $"删除文件失败: {ex.Message}"
            };
            return StatusCode(500, response);
        }
    }

    /// <summary>
    /// 根据文件扩展名获取Content-Type
    /// </summary>
    private static string GetContentType(string filename)
    {
        var extension = Path.GetExtension(filename).ToLowerInvariant();
        
        return extension switch
        {
            ".pdf" => "application/pdf",
            ".tiff" or ".tif" => "image/tiff",
            ".jpg" or ".jpeg" => "image/jpeg",
            ".png" => "image/png",
            ".gif" => "image/gif",
            ".bmp" => "image/bmp",
            _ => "application/octet-stream"
        };
    }
}
