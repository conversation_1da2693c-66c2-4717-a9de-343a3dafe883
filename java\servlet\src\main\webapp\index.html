<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ScanOnWeb Servlet Server</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        h1 {
            color: #409eff;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e4e7ed;
            border-radius: 8px;
        }
        .section h3 {
            color: #303133;
            margin-top: 0;
        }
        button {
            background-color: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #66b1ff;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            border-left: 4px solid #409eff;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-left-color: #67c23a;
            background-color: #f0f9ff;
        }
        .error {
            border-left-color: #f56c6c;
            background-color: #fef0f0;
        }
        input[type="file"] {
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 ScanOnWeb Servlet Server</h1>
        <p style="text-align: center; color: #666;">简单的Servlet后台服务，完全兼容Go和SpringBoot版本API</p>
        
        <!-- 健康检查 -->
        <div class="section">
            <h3>📊 健康检查</h3>
            <button onclick="testHealth()">检查服务状态</button>
            <div id="health-result" class="result">点击按钮检查Servlet服务状态...</div>
        </div>

        <!-- 文件上传测试 -->
        <div class="section">
            <h3>📤 文件上传测试</h3>
            <div>
                <label>选择文件: </label>
                <input type="file" id="fileInput" accept=".pdf,.tiff,.tif,.jpg,.jpeg,.png">
            </div>
            <div>
                <button onclick="uploadFile('pdf')">上传为PDF</button>
                <button onclick="uploadFile('tiff')">上传为TIFF</button>
                <button onclick="uploadFile('jpg')">上传为JPG</button>
            </div>
            <div id="upload-result" class="result">选择文件后点击上传按钮...</div>
        </div>

        <!-- JSON上传测试 -->
        <div class="section">
            <h3>📝 JSON上传测试</h3>
            <button onclick="testJsonUpload('pdf')">测试PDF JSON上传</button>
            <button onclick="testJsonUpload('tiff')">测试TIFF JSON上传</button>
            <button onclick="testJsonUpload('jpg')">测试JPG JSON上传</button>
            <div id="json-result" class="result">点击按钮测试JSON格式上传...</div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080';

        // 健康检查
        async function testHealth() {
            const resultDiv = document.getElementById('health-result');
            resultDiv.textContent = '正在检查服务状态...';
            resultDiv.className = 'result';
            
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Servlet服务运行正常\n\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 服务异常\n状态码: ${response.status}\n响应: ${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 连接失败\n错误: ${error.message}\n\n请确保Servlet服务已启动在端口8080`;
            }
        }

        // 文件上传
        async function uploadFile(format) {
            const fileInput = document.getElementById('fileInput');
            const resultDiv = document.getElementById('upload-result');
            
            if (!fileInput.files[0]) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 请先选择文件';
                return;
            }
            
            const file = fileInput.files[0];
            const formData = new FormData();
            formData.append('image', file);
            formData.append('id', 'test_' + format + '_' + Date.now());
            formData.append('desc', `Servlet ${format.toUpperCase()} 上传测试`);
            
            resultDiv.textContent = `正在上传 ${format.toUpperCase()} 文件...`;
            resultDiv.className = 'result';
            
            try {
                let url = `${API_BASE}/upload`;
                if (format === 'tiff') url = `${API_BASE}/upload-tiff`;
                if (format === 'jpg') url = `${API_BASE}/upload-jpg`;
                
                const response = await fetch(url, {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ ${format.toUpperCase()} 文件上传成功!\n\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ ${format.toUpperCase()} 文件上传失败!\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ ${format.toUpperCase()} 文件上传失败!\n错误: ${error.message}`;
            }
        }

        // JSON上传测试
        async function testJsonUpload(format) {
            const resultDiv = document.getElementById('json-result');
            resultDiv.textContent = `正在测试 ${format.toUpperCase()} JSON上传...`;
            resultDiv.className = 'result';
            
            // 创建一个简单的测试图像数据（1x1像素的PNG）
            const testImageData = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
            
            const requestData = {
                id: `test_json_${format}_${Date.now()}`,
                desc: `Servlet ${format.toUpperCase()} JSON上传测试`,
                imageData: testImageData
            };
            
            if (format === 'jpg') {
                requestData.index = 0;
            }
            
            try {
                let url = `${API_BASE}/upload`;
                if (format === 'tiff') url = `${API_BASE}/upload-tiff`;
                if (format === 'jpg') url = `${API_BASE}/upload-jpg`;
                
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ ${format.toUpperCase()} JSON上传成功!\n\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ ${format.toUpperCase()} JSON上传失败!\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ ${format.toUpperCase()} JSON上传失败!\n错误: ${error.message}`;
            }
        }

        // 页面加载时自动检查服务状态
        window.onload = function() {
            setTimeout(testHealth, 500);
        };
    </script>
</body>
</html>
