import React from 'react';
import './MessageToast.css';
import { Message } from './ScannerApp';

interface MessageToastProps {
  messages: Message[];
}

const MessageToast: React.FC<MessageToastProps> = ({ messages }) => {
  const getIcon = (type: Message['type']) => {
    switch (type) {
      case 'success':
        return 'fas fa-check-circle';
      case 'error':
        return 'fas fa-exclamation-circle';
      case 'warning':
        return 'fas fa-exclamation-triangle';
      case 'info':
      default:
        return 'fas fa-info-circle';
    }
  };

  return (
    <div className="message-container">
      {messages.map((message) => (
        <div
          key={message.id}
          className={`message-toast message-${message.type}`}
        >
          <i className={getIcon(message.type)}></i>
          <span className="message-text">{message.text}</span>
        </div>
      ))}
    </div>
  );
};

export default MessageToast;
