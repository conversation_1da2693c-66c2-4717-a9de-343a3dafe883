[package]
name = "scanonweb-rust"
version = "1.0.0"
edition = "2021"
description = "Rust backend for ScanOnWeb document scanning system"
authors = ["ScanOnWeb Team"]

[dependencies]
# Web框架
axum = "0.7"
tokio = { version = "1.0", features = ["full"] }
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "fs"] }

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 文件上传处理
bytes = "1.5"

# Base64编码
base64 = "0.21"

# 文件系统操作
tokio-util = { version = "0.7", features = ["io"] }

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# UUID生成
uuid = { version = "1.0", features = ["v4"] }

# 错误处理
anyhow = "1.0"
thiserror = "1.0"

# 日志
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# HTTP状态码
http = "1.0"

[dev-dependencies]
# 测试依赖
reqwest = { version = "0.11", features = ["json", "multipart"] }

[[bin]]
name = "scanonweb-server"
path = "src/main.rs"
