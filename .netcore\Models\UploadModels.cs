using System.Text.Json.Serialization;

namespace ScanOnWeb.Models;

/// <summary>
/// 上传请求模型
/// </summary>
public class UploadRequest
{
    /// <summary>
    /// 文件ID
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 描述信息
    /// </summary>
    [JsonPropertyName("desc")]
    public string? Description { get; set; }

    /// <summary>
    /// Base64编码的图像数据
    /// </summary>
    [JsonPropertyName("imageData")]
    public string ImageData { get; set; } = string.Empty;

    /// <summary>
    /// 索引（用于JPG上传）
    /// </summary>
    public int? Index { get; set; }
}

/// <summary>
/// 上传响应模型
/// </summary>
public class UploadResponse
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 响应消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 状态码
    /// </summary>
    public int Code { get; set; }

    /// <summary>
    /// 文件ID
    /// </summary>
    [JsonPropertyName("fileId")]
    public string? FileId { get; set; }

    /// <summary>
    /// 文件路径
    /// </summary>
    public string? Path { get; set; }

    /// <summary>
    /// 附加数据
    /// </summary>
    public object? Data { get; set; }

    /// <summary>
    /// 创建成功响应
    /// </summary>
    public static UploadResponse CreateSuccess(string message, string fileId, string path, object? data = null)
    {
        return new UploadResponse
        {
            Success = true,
            Message = message,
            Code = 200,
            FileId = fileId,
            Path = path,
            Data = data
        };
    }

    /// <summary>
    /// 创建错误响应
    /// </summary>
    public static UploadResponse CreateError(string message, int code = 400)
    {
        return new UploadResponse
        {
            Success = false,
            Message = message,
            Code = code
        };
    }
}

/// <summary>
/// 健康检查响应模型
/// </summary>
public class HealthResponse
{
    /// <summary>
    /// 状态
    /// </summary>
    public string Status { get; set; } = "ok";

    /// <summary>
    /// 时间
    /// </summary>
    public string Time { get; set; } = DateTime.UtcNow.ToString("O");

    /// <summary>
    /// 服务名称
    /// </summary>
    public string Service { get; set; } = "ScanOnWeb .NET Core Server";

    /// <summary>
    /// 版本
    /// </summary>
    public string Version { get; set; } = "1.0.0";
}

/// <summary>
/// 文件信息模型
/// </summary>
public class FileInfo
{
    /// <summary>
    /// 文件名
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 文件大小（字节）
    /// </summary>
    public long Size { get; set; }

    /// <summary>
    /// 修改时间
    /// </summary>
    [JsonPropertyName("modTime")]
    public string ModTime { get; set; } = string.Empty;

    /// <summary>
    /// 文件URL
    /// </summary>
    public string Url { get; set; } = string.Empty;

    /// <summary>
    /// 文件类型
    /// </summary>
    [JsonPropertyName("type")]
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// 格式化的文件大小
    /// </summary>
    [JsonPropertyName("formattedSize")]
    public string FormattedSize { get; set; } = string.Empty;
}

/// <summary>
/// 文件列表响应模型
/// </summary>
public class FileListResponse
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 文件列表
    /// </summary>
    public List<FileInfo> Files { get; set; } = new();

    /// <summary>
    /// 文件数量
    /// </summary>
    public int Count { get; set; }
}

/// <summary>
/// 删除响应模型
/// </summary>
public class DeleteResponse
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 响应消息
    /// </summary>
    public string Message { get; set; } = string.Empty;
}
