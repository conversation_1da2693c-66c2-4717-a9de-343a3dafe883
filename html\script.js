/**
 * 文档扫描系统 - HTML版本
 * 主要逻辑文件
 */

// 全局变量
let scanonweb = null;
let isConnected = false;
let devices = [];
let selectedDevice = -1;
let images = [];
let viewMode = "grid";
let currentPreviewIndex = 0;

// 扫描配置
const config = {
  dpi_x: 300,
  dpi_y: 300,
  colorMode: "RGB",
  showDialog: false,
  autoFeedEnable: true,
  autoFeed: false,
  dupxMode: false,
  autoDeskew: false,
  autoBorderDetection: false,
};

// DOM元素
const elements = {
  connectionAlert: document.getElementById("connection-alert"),
  deviceSelect: document.getElementById("device-select"),
  colorMode: document.getElementById("color-mode"),
  dpiX: document.getElementById("dpi-x"),
  dpiY: document.getElementById("dpi-y"),
  showDialog: document.getElementById("show-dialog"),
  autoFeed: document.getElementById("auto-feed"),
  duplexMode: document.getElementById("duplex-mode"),
  autoDeskew: document.getElementById("auto-deskew"),
  autoBorder: document.getElementById("auto-border"),
  refreshDevices: document.getElementById("refresh-devices"),
  startScan: document.getElementById("start-scan"),
  getImages: document.getElementById("get-images"),
  clearAll: document.getElementById("clear-all"),
  uploadDocs: document.getElementById("upload-docs"),
  saveLocal: document.getElementById("save-local"),
  resultCard: document.getElementById("result-card"),
  resultTitle: document.getElementById("result-title"),
  imageGrid: document.getElementById("image-grid"),
  imageList: document.getElementById("image-list"),
  imageTableBody: document.getElementById("image-table-body"),
  emptyState: document.getElementById("empty-state"),
  emptyScan: document.getElementById("empty-scan"),
  viewGrid: document.getElementById("view-grid"),
  viewList: document.getElementById("view-list"),
  uploadDialog: document.getElementById("upload-dialog"),
  uploadId: document.getElementById("upload-id"),
  uploadDesc: document.getElementById("upload-desc"),
  uploadCancel: document.getElementById("upload-cancel"),
  uploadConfirm: document.getElementById("upload-confirm"),
  previewDialog: document.getElementById("preview-dialog"),
  previewImage: document.getElementById("preview-image"),
  previewInfoText: document.getElementById("preview-info-text"),
  prevImage: document.getElementById("prev-image"),
  nextImage: document.getElementById("next-image"),
  downloadCurrent: document.getElementById("download-current"),
  previewClose: document.getElementById("preview-close"),
  loadingOverlay: document.getElementById("loading-overlay"),
  loadingText: document.getElementById("loading-text"),
};

// 工具函数
const utils = {
  // 显示消息
  showMessage(message, type = "info") {
    // 简单的消息提示实现
    const messageDiv = document.createElement("div");
    messageDiv.className = `message message-${type}`;
    messageDiv.textContent = message;
    messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 4px;
            color: white;
            z-index: 3000;
            animation: slideInRight 0.3s ease-out;
        `;

    switch (type) {
      case "success":
        messageDiv.style.backgroundColor = "#67c23a";
        break;
      case "warning":
        messageDiv.style.backgroundColor = "#e6a23c";
        break;
      case "error":
        messageDiv.style.backgroundColor = "#f56c6c";
        break;
      default:
        messageDiv.style.backgroundColor = "#409eff";
    }

    document.body.appendChild(messageDiv);

    setTimeout(() => {
      messageDiv.remove();
    }, 3000);
  },

  // 显示确认对话框
  showConfirm(message, callback) {
    if (confirm(message)) {
      callback();
    }
  },

  // 生成唯一ID
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  },

  // 下载base64图像
  downloadBase64Image(base64, filename = "image.jpg") {
    const byteCharacters = atob(base64);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: "image/jpeg" });

    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  },

  // 显示加载状态
  showLoading(text = "处理中...") {
    elements.loadingText.textContent = text;
    elements.loadingOverlay.style.display = "flex";
  },

  // 隐藏加载状态
  hideLoading() {
    elements.loadingOverlay.style.display = "none";
  },
};

// 初始化扫描服务
function initScanService() {
  try {
    scanonweb = new ScanOnWeb();

    // 设置事件回调
    scanonweb.onGetDevicesListEvent = (msg) => {
      devices = msg.devices || [];
      updateDeviceSelect();

      // 自动选择第一个设备
      if (devices.length > 0) {
        selectedDevice = msg.currentIndex >= 0 ? msg.currentIndex : 0;
        elements.deviceSelect.value = selectedDevice;
        if (selectedDevice >= 0) {
          setTimeout(() => {
            onDeviceChange(selectedDevice);
          }, 100);
        }
      } else {
        selectedDevice = -1;
      }

      isConnected = true;
      updateConnectionStatus();
      updateButtonStates();
      utils.showMessage(
        `发现 ${devices.length} 个扫描设备${
          selectedDevice >= 0 ? "，已自动选择第一个设备" : ""
        }`,
        "success"
      );
    };

    scanonweb.onScanFinishedEvent = (msg) => {
      utils.showMessage(
        `扫描完成！共扫描 ${msg.imageAfterCount} 张图像`,
        "success"
      );
      getAllImage();
    };

    scanonweb.onGetAllImageEvent = (msg) => {
      if (msg.images && msg.images.length > 0) {
        images = msg.images.map((image, index) => ({
          src: `data:image/jpg;base64,${image}`,
          index: index,
          base64: image,
        }));
        updateImageDisplay();
        utils.showMessage(`获取到 ${images.length} 张图像`, "success");
      } else {
        utils.showMessage("暂无扫描图像", "info");
      }
    };

    scanonweb.onGetImageByIdEvent = (msg) => {
      if (msg.imageBase64) {
        addImage(msg.imageBase64);
      }
    };

    scanonweb.onImageEditedEvent = (msg) => {
      utils.showMessage(`图像 ${msg.imageIndex + 1} 已编辑`, "info");
      if (msg.imageBase64) {
        editImage(msg.imageIndex, msg.imageBase64);
      }
    };

    scanonweb.onUploadEvent = () => {
      utils.showMessage("用户点击了上传按钮", "info");
      showUploadDialog();
    };

    // 设置上传回调
    setupUploadCallbacks();

    // 检查连接状态
    setTimeout(() => {
      if (devices.length === 0) {
        isConnected = false;
        updateConnectionStatus();
        utils.showMessage("扫描服务连接失败，请检查服务是否启动", "warning");
      }
    }, 3000);
  } catch (error) {
    console.error("初始化扫描服务失败:", error);
    isConnected = false;
    updateConnectionStatus();
    utils.showMessage("初始化扫描服务失败", "error");
  }
}

// 设置上传回调
function setupUploadCallbacks() {
  // PDF上传回调
  scanonweb.onUploadAllImageAsPdfToUrlEvent = (msg) => {
    console.log("PDF上传回调:", msg);
    utils.hideLoading();

    try {
      let uploadResult = null;
      if (msg.uploadResult) {
        uploadResult =
          typeof msg.uploadResult === "string"
            ? JSON.parse(msg.uploadResult)
            : msg.uploadResult;
      }

      if (uploadResult && uploadResult.success) {
        utils.showMessage(
          `PDF文档上传成功！共 ${msg.imageCount || 0} 张图像`,
          "success"
        );
        hideUploadDialog();
        console.log("上传详情:", uploadResult);
      } else {
        const errorMsg = uploadResult?.message || msg.message || "未知错误";
        utils.showMessage(`PDF上传失败: ${errorMsg}`, "error");
      }
    } catch (error) {
      console.error("解析上传结果失败:", error, msg);
      utils.showMessage("PDF上传失败: 响应解析错误", "error");
    }
  };

  // TIFF上传回调
  scanonweb.onUploadAllImageAsTiffToUrlEvent = (msg) => {
    console.log("TIFF上传回调:", msg);
    utils.hideLoading();

    try {
      let uploadResult = null;
      if (msg.uploadResult) {
        uploadResult =
          typeof msg.uploadResult === "string"
            ? JSON.parse(msg.uploadResult)
            : msg.uploadResult;
      }

      if (uploadResult && uploadResult.success) {
        utils.showMessage(
          `TIFF文档上传成功！共 ${msg.imageCount || 0} 张图像`,
          "success"
        );
        hideUploadDialog();
        console.log("上传详情:", uploadResult);
      } else {
        const errorMsg = uploadResult?.message || msg.message || "未知错误";
        utils.showMessage(`TIFF上传失败: ${errorMsg}`, "error");
      }
    } catch (error) {
      console.error("解析上传结果失败:", error, msg);
      utils.showMessage("TIFF上传失败: 响应解析错误", "error");
    }
  };

  // JPG上传回调
  scanonweb.onUploadJpgImageByIndexEvent = (msg) => {
    console.log("JPG上传回调:", msg);
    utils.hideLoading();

    try {
      let uploadResult = null;
      if (msg.uploadResult) {
        uploadResult =
          typeof msg.uploadResult === "string"
            ? JSON.parse(msg.uploadResult)
            : msg.uploadResult;
      }

      if (uploadResult && uploadResult.success) {
        utils.showMessage("JPG图像上传成功！", "success");
        hideUploadDialog();
        console.log("上传详情:", uploadResult);
      } else {
        const errorMsg = uploadResult?.message || msg.message || "未知错误";
        utils.showMessage(`JPG上传失败: ${errorMsg}`, "error");
      }
    } catch (error) {
      console.error("解析上传结果失败:", error, msg);
      utils.showMessage("JPG上传失败: 响应解析错误", "error");
    }
  };
}

// 更新设备选择下拉框
function updateDeviceSelect() {
  elements.deviceSelect.innerHTML =
    '<option value="-1">请选择扫描设备</option>';
  devices.forEach((device, index) => {
    const option = document.createElement("option");
    option.value = index;
    option.textContent = device;
    elements.deviceSelect.appendChild(option);
  });
}

// 更新连接状态
function updateConnectionStatus() {
  if (isConnected) {
    elements.connectionAlert.style.display = "none";
  } else {
    elements.connectionAlert.style.display = "block";
  }
}

// 更新按钮状态
function updateButtonStates() {
  const hasDevice = selectedDevice !== -1 && isConnected;
  const hasImages = images.length > 0;

  elements.startScan.disabled = !hasDevice;
  elements.emptyScan.disabled = !hasDevice;
  elements.uploadDocs.disabled = !hasImages;
  elements.saveLocal.disabled = !hasImages;
}

// 更新图像显示
function updateImageDisplay() {
  const hasImages = images.length > 0;

  if (hasImages) {
    elements.resultCard.style.display = "block";
    elements.emptyState.style.display = "none";
    elements.resultTitle.textContent = `扫描结果 (${images.length} 张图像)`;

    if (viewMode === "grid") {
      updateGridView();
    } else {
      updateListView();
    }
  } else {
    elements.resultCard.style.display = "none";
    elements.emptyState.style.display = "block";
  }

  updateButtonStates();
}

// 更新网格视图
function updateGridView() {
  elements.imageGrid.style.display = "grid";
  elements.imageList.style.display = "none";
  elements.viewGrid.className = "btn btn-sm btn-primary";
  elements.viewList.className = "btn btn-sm btn-default";

  elements.imageGrid.innerHTML = "";
  images.forEach((image, index) => {
    const imageItem = createImageGridItem(image, index);
    elements.imageGrid.appendChild(imageItem);
  });
}

// 更新列表视图
function updateListView() {
  elements.imageGrid.style.display = "none";
  elements.imageList.style.display = "block";
  elements.viewGrid.className = "btn btn-sm btn-default";
  elements.viewList.className = "btn btn-sm btn-primary";

  elements.imageTableBody.innerHTML = "";
  images.forEach((image, index) => {
    const row = createImageTableRow(image, index);
    elements.imageTableBody.appendChild(row);
  });
}

// 创建网格视图图像项
function createImageGridItem(image, index) {
  const div = document.createElement("div");
  div.className = "image-item";
  div.innerHTML = `
        <div class="image-wrapper">
            <img src="${image.src}" alt="扫描图像 ${
    index + 1
  }" class="scan-image" onclick="previewImage(${index})">
            <div class="image-overlay">
                <div class="btn-group">
                    <button class="btn btn-primary btn-sm" onclick="previewImage(${index})" title="预览">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-success btn-sm" onclick="downloadImage(${index})" title="下载">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="btn btn-warning btn-sm" onclick="deleteImage(${index})" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="image-info">
            <span class="image-index">图像 ${index + 1}</span>
        </div>
    `;
  return div;
}

// 创建列表视图表格行
function createImageTableRow(image, index) {
  const tr = document.createElement("tr");
  tr.innerHTML = `
        <td>
            <img src="${
              image.src
            }" class="table-thumbnail" onclick="previewImage(${index})" alt="预览">
        </td>
        <td>图像 ${index + 1}</td>
        <td><span class="tag">JPEG</span></td>
        <td>
            <div class="btn-group">
                <button class="btn btn-primary btn-sm" onclick="previewImage(${index})">
                    <i class="fas fa-eye"></i> 预览
                </button>
                <button class="btn btn-success btn-sm" onclick="downloadImage(${index})">
                    <i class="fas fa-download"></i> 下载
                </button>
                <button class="btn btn-warning btn-sm" onclick="deleteImage(${index})">
                    <i class="fas fa-trash"></i> 删除
                </button>
            </div>
        </td>
    `;
  return tr;
}

// 加载设备列表
function loadDevices() {
  if (!scanonweb) {
    utils.showMessage("扫描服务未初始化", "error");
    return;
  }

  try {
    scanonweb.loadDevices();
  } catch (error) {
    utils.showMessage("获取设备列表失败", "error");
  }
}

// 设备选择变化
function onDeviceChange(deviceIndex) {
  if (scanonweb) {
    scanonweb.selectScanDevice(deviceIndex);
    utils.showMessage(`已选择设备: ${devices[deviceIndex]}`, "info");
  }
}

// 开始扫描
function startScan() {
  if (!scanonweb) {
    utils.showMessage("扫描服务未初始化", "error");
    return;
  }

  if (selectedDevice === -1) {
    utils.showMessage("请先选择扫描设备", "warning");
    return;
  }

  try {
    // 更新扫描配置
    updateScanConfig();
    scanonweb.scaner_work_config = {
      ...scanonweb.scaner_work_config,
      ...config,
      deviceIndex: selectedDevice,
    };

    scanonweb.startScan();
    utils.showMessage("开始扫描...", "info");
  } catch (error) {
    utils.showMessage("启动扫描失败", "error");
  }
}

// 获取所有图像
function getAllImage() {
  if (!scanonweb) {
    utils.showMessage("扫描服务未初始化", "error");
    return;
  }

  try {
    scanonweb.getAllImage();
  } catch (error) {
    utils.showMessage("获取图像失败", "error");
  }
}

// 清空所有图像
function clearAll() {
  utils.showConfirm("确定要清空所有扫描结果吗？", () => {
    if (scanonweb) {
      scanonweb.clearAll();
      images = [];
      updateImageDisplay();
      utils.showMessage("已清空所有扫描结果", "success");
    }
  });
}

// 更新扫描配置
function updateScanConfig() {
  config.dpi_x = parseInt(elements.dpiX.value) || 300;
  config.dpi_y = parseInt(elements.dpiY.value) || 300;
  config.colorMode = elements.colorMode.value;
  config.showDialog = elements.showDialog.checked;
  config.autoFeedEnable = elements.autoFeed.checked;
  config.dupxMode = elements.duplexMode.checked;
  config.autoDeskew = elements.autoDeskew.checked;
  config.autoBorderDetection = elements.autoBorder.checked;
}

// 添加图像
function addImage(imageBase64) {
  images.push({
    src: `data:image/jpg;base64,${imageBase64}`,
    index: images.length,
    base64: imageBase64,
  });
  updateImageDisplay();
}

// 编辑图像
function editImage(index, imageBase64) {
  if (index >= 0 && index < images.length) {
    images[index] = {
      src: `data:image/jpg;base64,${imageBase64}`,
      index: index,
      base64: imageBase64,
    };
    updateImageDisplay();
  }
}

// 显示上传对话框
function showUploadDialog() {
  if (images.length === 0) {
    utils.showMessage("没有可上传的图像", "warning");
    return;
  }

  elements.uploadId.value = utils.generateId();
  elements.uploadDesc.value = `扫描文档_${new Date().toLocaleString()}`;
  elements.uploadDialog.style.display = "flex";
}

// 隐藏上传对话框
function hideUploadDialog() {
  elements.uploadDialog.style.display = "none";
}

// 确认上传
function confirmUpload() {
  const id = elements.uploadId.value.trim();
  const description = elements.uploadDesc.value.trim();
  const format = document.querySelector(
    'input[name="upload-format"]:checked'
  ).value;

  if (!id) {
    utils.showMessage("请输入文档ID", "warning");
    return;
  }

  if (!scanonweb) {
    utils.showMessage("扫描服务未初始化", "error");
    return;
  }

  if (images.length === 0) {
    utils.showMessage("没有可上传的图像", "warning");
    return;
  }

  try {
    utils.showLoading("上传中...");

    // 调用控件内置的上传方法
    if (format === "pdf") {
      const uploadUrl = "http://localhost:8080/upload";
      utils.showMessage("开始上传PDF文档...", "info");
      scanonweb.uploadAllImageAsPdfToUrl(uploadUrl, id, description);
    } else if (format === "tiff") {
      const tiffUploadUrl = "http://localhost:8080/upload-tiff";
      utils.showMessage("开始上传TIFF文档...", "info");
      scanonweb.uploadAllImageAsTiffToUrl(tiffUploadUrl, id, description);
    } else if (format === "jpg") {
      const jpgUploadUrl = "http://localhost:8080/upload-jpg";
      utils.showMessage("开始上传JPG图像...", "info");
      // 上传第一张图像作为示例，实际可以循环上传所有图像
      scanonweb.uploadJpgImageByIndex(jpgUploadUrl, id, description, 0);
    }
  } catch (error) {
    utils.hideLoading();
    console.error("上传失败:", error);
    utils.showMessage(`上传失败: ${error.message || "未知错误"}`, "error");
  }
}

// 预览图像
function previewImage(index) {
  if (index >= 0 && index < images.length) {
    currentPreviewIndex = index;
    elements.previewImage.src = images[index].src;
    elements.previewInfoText.textContent = `图像 ${index + 1} / ${
      images.length
    }`;
    updatePreviewButtons();
    elements.previewDialog.style.display = "flex";
  }
}

// 更新预览按钮状态
function updatePreviewButtons() {
  elements.prevImage.disabled = currentPreviewIndex <= 0;
  elements.nextImage.disabled = currentPreviewIndex >= images.length - 1;
}

// 上一张图像
function prevImage() {
  if (currentPreviewIndex > 0) {
    currentPreviewIndex--;
    elements.previewImage.src = images[currentPreviewIndex].src;
    elements.previewInfoText.textContent = `图像 ${currentPreviewIndex + 1} / ${
      images.length
    }`;
    updatePreviewButtons();
  }
}

// 下一张图像
function nextImage() {
  if (currentPreviewIndex < images.length - 1) {
    currentPreviewIndex++;
    elements.previewImage.src = images[currentPreviewIndex].src;
    elements.previewInfoText.textContent = `图像 ${currentPreviewIndex + 1} / ${
      images.length
    }`;
    updatePreviewButtons();
  }
}

// 下载图像
function downloadImage(index) {
  if (index >= 0 && index < images.length) {
    const image = images[index];
    const filename = `scan_image_${index + 1}.jpg`;
    utils.downloadBase64Image(image.base64, filename);
    utils.showMessage(`图像 ${index + 1} 下载成功`, "success");
  }
}

// 下载当前预览图像
function downloadCurrentImage() {
  downloadImage(currentPreviewIndex);
}

// 删除图像
function deleteImage(index) {
  utils.showConfirm(`确定要删除图像 ${index + 1} 吗？`, () => {
    images.splice(index, 1);
    // 重新设置索引
    images.forEach((img, idx) => {
      img.index = idx;
    });
    updateImageDisplay();
    utils.showMessage("图像删除成功", "success");

    // 如果删除的是当前预览的图像，关闭预览对话框
    if (currentPreviewIndex === index) {
      elements.previewDialog.style.display = "none";
    } else if (currentPreviewIndex > index) {
      currentPreviewIndex--;
    }
  });
}

// 本地保存
function saveAs() {
  if (!scanonweb) {
    utils.showMessage("扫描服务未初始化", "error");
    return;
  }

  if (images.length === 0) {
    utils.showMessage("没有可保存的图像", "warning");
    return;
  }

  try {
    const filename = `d:/scan_${Date.now()}.pdf`;
    scanonweb.saveAllImageToLocal(filename);
    utils.showMessage("文件保存成功", "success");
  } catch (error) {
    utils.showMessage("保存失败", "error");
  }
}

// 事件监听器
function setupEventListeners() {
  // 设备选择变化
  elements.deviceSelect.addEventListener("change", (e) => {
    selectedDevice = parseInt(e.target.value);
    if (selectedDevice >= 0) {
      onDeviceChange(selectedDevice);
    }
    updateButtonStates();
  });

  // 按钮点击事件
  elements.refreshDevices.addEventListener("click", loadDevices);
  elements.startScan.addEventListener("click", startScan);
  elements.getImages.addEventListener("click", getAllImage);
  elements.clearAll.addEventListener("click", clearAll);
  elements.uploadDocs.addEventListener("click", showUploadDialog);
  elements.saveLocal.addEventListener("click", saveAs);
  elements.emptyScan.addEventListener("click", startScan);

  // 视图切换
  elements.viewGrid.addEventListener("click", () => {
    viewMode = "grid";
    updateImageDisplay();
  });

  elements.viewList.addEventListener("click", () => {
    viewMode = "list";
    updateImageDisplay();
  });

  // 上传对话框
  elements.uploadCancel.addEventListener("click", hideUploadDialog);
  elements.uploadConfirm.addEventListener("click", confirmUpload);

  // 预览对话框
  elements.prevImage.addEventListener("click", prevImage);
  elements.nextImage.addEventListener("click", nextImage);
  elements.downloadCurrent.addEventListener("click", downloadCurrentImage);
  elements.previewClose.addEventListener("click", () => {
    elements.previewDialog.style.display = "none";
  });

  // 模态框关闭按钮
  document.querySelectorAll(".modal-close").forEach((btn) => {
    btn.addEventListener("click", (e) => {
      const modal = e.target.closest(".modal");
      modal.style.display = "none";
    });
  });

  // 点击模态框背景关闭
  document.querySelectorAll(".modal").forEach((modal) => {
    modal.addEventListener("click", (e) => {
      if (e.target === modal) {
        modal.style.display = "none";
      }
    });
  });

  // 键盘事件
  document.addEventListener("keydown", (e) => {
    // ESC键关闭模态框
    if (e.key === "Escape") {
      document.querySelectorAll(".modal").forEach((modal) => {
        if (modal.style.display === "flex") {
          modal.style.display = "none";
        }
      });
    }

    // 预览对话框中的左右箭头键
    if (elements.previewDialog.style.display === "flex") {
      if (e.key === "ArrowLeft") {
        prevImage();
      } else if (e.key === "ArrowRight") {
        nextImage();
      }
    }
  });
}

// 页面加载完成后初始化
document.addEventListener("DOMContentLoaded", () => {
  // 设置事件监听器
  setupEventListeners();

  // 初始化扫描服务
  initScanService();

  // 自动加载设备列表
  setTimeout(() => {
    loadDevices();
  }, 1000);

  // 初始化界面状态
  updateConnectionStatus();
  updateButtonStates();
  updateImageDisplay();
});
