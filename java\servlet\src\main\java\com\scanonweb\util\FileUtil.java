package com.scanonweb.util;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.IOUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.UUID;

/**
 * 文件工具类
 */
public class FileUtil {
    
    private static final String UPLOAD_DIR = "./uploads";
    
    /**
     * 初始化上传目录
     */
    public static void initUploadDirectory() {
        try {
            Path uploadPath = Paths.get(UPLOAD_DIR);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
                System.out.println("创建上传目录: " + uploadPath.toAbsolutePath());
            }
        } catch (IOException e) {
            throw new RuntimeException("创建上传目录失败", e);
        }
    }
    
    /**
     * 生成文件名
     */
    public static String generateFilename(String id, String extension) {
        if (id == null || id.trim().isEmpty()) {
            id = UUID.randomUUID().toString().replace("-", "");
        }
        
        String timestamp = String.valueOf(System.currentTimeMillis());
        return String.format("%s_%s.%s", id, timestamp, extension);
    }
    
    /**
     * 保存上传的文件
     */
    public static String saveUploadedFile(InputStream inputStream, String id, String format) throws IOException {
        initUploadDirectory();
        
        String filename = generateFilename(id, format);
        File file = new File(UPLOAD_DIR, filename);
        
        try (FileOutputStream fos = new FileOutputStream(file)) {
            IOUtils.copy(inputStream, fos);
        }
        
        System.out.println("文件保存成功: " + filename + " (大小: " + file.length() + " bytes)");
        return filename;
    }
    
    /**
     * 保存Base64编码的文件
     */
    public static String saveBase64File(String base64Data, String id, String format) throws IOException {
        initUploadDirectory();
        
        // 解码Base64数据
        byte[] data = Base64.decodeBase64(base64Data);
        
        String filename = generateFilename(id, format);
        File file = new File(UPLOAD_DIR, filename);
        
        try (FileOutputStream fos = new FileOutputStream(file)) {
            fos.write(data);
        }
        
        System.out.println("Base64文件保存成功: " + filename + " (大小: " + data.length + " bytes)");
        return filename;
    }
    
    /**
     * 获取文件
     */
    public static File getUploadedFile(String filename) {
        return new File(UPLOAD_DIR, filename);
    }
    
    /**
     * 验证文件类型
     */
    public static boolean isValidFileType(String extension) {
        String[] allowedTypes = {"pdf", "tiff", "tif", "jpg", "jpeg", "png"};
        for (String type : allowedTypes) {
            if (type.equalsIgnoreCase(extension)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 获取文件扩展名
     */
    public static String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filename.length() - 1) {
            return filename.substring(lastDotIndex + 1).toLowerCase();
        }
        return "";
    }
}
