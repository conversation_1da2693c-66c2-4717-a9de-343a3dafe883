/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #f5f7fa;
    line-height: 1.6;
}

/* 全局滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 主容器 */
.scan-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f5f7fa;
    min-height: 100vh;
}

/* 页面标题 */
.header-card {
    margin-bottom: 20px;
    text-align: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.page-title {
    margin: 0;
    font-size: 2.5em;
    font-weight: 300;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.page-subtitle {
    margin: 10px 0 0 0;
    font-size: 1.1em;
    opacity: 0.9;
}

/* 连接状态指示器 */
.connection-alert {
    margin-bottom: 20px;
}

.alert {
    padding: 15px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.alert-warning {
    background-color: #fdf6ec;
    border: 1px solid #faecd8;
    color: #e6a23c;
}

.alert-content strong {
    display: block;
    margin-bottom: 5px;
}

.alert-content p {
    margin: 0;
    opacity: 0.8;
}

/* 卡片样式 */
.config-card,
.action-card,
.result-card {
    margin-bottom: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border: none;
    animation: fadeIn 0.5s ease-out;
}

.card-header {
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
    background-color: #fafafa;
    border-radius: 8px 8px 0 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 600;
    color: #303133;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-body {
    padding: 20px;
}

/* 表单样式 */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: -10px;
}

.col-6 {
    flex: 0 0 50%;
    padding: 10px;
}

.form-item {
    margin-bottom: 15px;
}

.form-item label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #606266;
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.form-control:focus {
    outline: none;
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* DPI输入框 */
.dpi-row {
    display: flex;
    align-items: center;
    gap: 10px;
}

.dpi-row .form-control {
    flex: 1;
}

.dpi-separator {
    font-weight: bold;
    color: #909399;
}

/* 高级选项 */
.advanced-options {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.options-row {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.checkbox-label,
.radio-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-weight: normal !important;
    margin-bottom: 0 !important;
}

.checkbox-label input,
.radio-label input {
    margin: 0;
}

/* 按钮样式 */
.button-row {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    padding: 20px;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    background: none;
    flex: 1;
    justify-content: center;
    min-width: 120px;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-primary {
    background-color: #409eff;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: #66b1ff;
}

.btn-success {
    background-color: #67c23a;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background-color: #85ce61;
}

.btn-info {
    background-color: #909399;
    color: white;
}

.btn-info:hover:not(:disabled) {
    background-color: #a6a9ad;
}

.btn-warning {
    background-color: #e6a23c;
    color: white;
}

.btn-warning:hover:not(:disabled) {
    background-color: #ebb563;
}

.btn-secondary {
    background-color: #909399;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background-color: #a6a9ad;
}

.btn-default {
    background-color: #dcdfe6;
    color: #606266;
}

.btn-default:hover:not(:disabled) {
    background-color: #c6e2ff;
    color: #409eff;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    min-width: auto;
}

.btn-group {
    display: flex;
    border-radius: 4px;
    overflow: hidden;
}

.btn-group .btn {
    border-radius: 0;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-group .btn:last-child {
    border-right: none;
}

/* 图像网格 */
.image-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    padding: 10px 0;
}

.image-item {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s, box-shadow 0.2s;
}

.image-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.image-wrapper {
    position: relative;
    overflow: hidden;
}

.scan-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.2s;
}

.scan-image:hover {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s;
}

.image-wrapper:hover .image-overlay {
    opacity: 1;
}

.image-info {
    padding: 15px;
    text-align: center;
}

.image-index {
    font-weight: 600;
    color: #303133;
}

/* 表格样式 */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.table th,
.table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ebeef5;
}

.table th {
    background-color: #fafafa;
    font-weight: 600;
    color: #303133;
}

.table-thumbnail {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
    cursor: pointer;
    transition: transform 0.2s;
}

.table-thumbnail:hover {
    transform: scale(1.1);
}

.tag {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    background-color: #909399;
    color: white;
}

/* 空状态 */
.empty-state {
    margin: 40px 0;
    padding: 40px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.empty-content {
    max-width: 400px;
    margin: 0 auto;
}

.empty-icon {
    font-size: 4em;
    color: #c0c4cc;
    margin-bottom: 20px;
}

.empty-content h3 {
    margin-bottom: 10px;
    color: #303133;
}

.empty-content p {
    color: #909399;
    margin-bottom: 20px;
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
}

.modal-large {
    max-width: 80%;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #ebeef5;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    margin: 0;
    color: #303133;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #909399;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #606266;
}

.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #ebeef5;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* 预览相关 */
.preview-container {
    text-align: center;
    padding: 20px;
}

.preview-image {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.preview-info {
    font-weight: 600;
    color: #606266;
}

.preview-actions {
    display: flex;
    gap: 10px;
}

/* 单选按钮组 */
.radio-group {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

/* 加载覆盖层 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.loading-content {
    background: white;
    padding: 30px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.loading-content i {
    font-size: 2em;
    color: #409eff;
    margin-bottom: 15px;
}

.loading-content span {
    color: #303133;
    font-size: 16px;
}

/* 消息提示样式 */
.message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 4px;
    color: white;
    z-index: 3000;
    animation: slideInRight 0.3s ease-out;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    font-weight: 500;
    max-width: 400px;
    word-wrap: break-word;
}

.message-success {
    background-color: #67c23a;
}

.message-warning {
    background-color: #e6a23c;
}

.message-error {
    background-color: #f56c6c;
}

.message-info {
    background-color: #409eff;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* 加载动画 */
.fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .scan-container {
        padding: 10px;
    }

    .page-title {
        font-size: 2em;
        flex-direction: column;
        gap: 10px;
    }

    .col-6 {
        flex: 0 0 100%;
    }

    .button-row {
        flex-direction: column;
    }

    .btn {
        min-width: auto;
    }

    .image-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 15px;
    }

    .modal-content {
        width: 95%;
        margin: 10px;
    }

    .modal-footer {
        flex-direction: column;
        gap: 15px;
    }

    .preview-actions {
        flex-wrap: wrap;
        justify-content: center;
    }

    .radio-group {
        flex-direction: column;
        gap: 10px;
    }
}
