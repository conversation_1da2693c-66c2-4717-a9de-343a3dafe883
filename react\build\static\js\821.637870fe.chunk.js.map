{"version": 3, "file": "static/js/821.637870fe.chunk.js", "mappings": ";qGASiEA,EAAOC,QAG9D,WAAc,aActB,MAAMC,EACJC,WAAAA,GAEEC,KAAKC,mBAAqB,CACxBC,QAAQ,EAERC,MAAO,IAEPC,MAAO,IAEPC,YAAa,EAEbC,YAAY,EAEZC,gBAAgB,EAEhBC,UAAU,EAEVC,UAAU,EAEVC,YAAY,EAEZC,qBAAqB,EAErBC,UAAW,MAEXC,UAAW,UAEbb,KAAKc,SAAW,KAChBd,KAAKe,WAAa,EAGlBf,KAAKgB,YACP,CAOAC,kBAAAA,CAAmBC,GAEjB,OADAC,QAAQC,IAAI,8FACL,IAAIC,SAAQ,CAACC,EAASC,KAC3B,MAAMC,EAAS,IAAIC,UAAUP,EAAQ,IACrCM,EAAOE,OAAS,KACdJ,EAAQE,IAEVA,EAAOG,QAAUC,IACfL,EAAOK,OAERC,MAAKL,IACNL,QAAQC,IAAI,wDAEZpB,KAAK8B,sBAAsBN,GAC3BL,QAAQC,IAAI,mEAEZpB,KAAK+B,cACEP,KACNI,IAED,GAAIV,EAAQc,OAAS,EACnB,OAAOhC,KAAKiB,mBAAmBC,EAAQe,MAAM,IAE/C,MAAML,IAEV,CAKAZ,UAAAA,GAEE,MAAME,EAAU,CAAC,sBAAuB,sBAAuB,sBAAuB,sBAAuB,uBAC7GlB,KAAKiB,mBAAmBC,EAC1B,CAMAY,qBAAAA,CAAsBN,GACpBxB,KAAKc,SAAWU,EAChBxB,KAAKc,SAASa,QAAU3B,KAAKkC,cAAcC,KAAKnC,MAChDA,KAAKc,SAASsB,UAAYpC,KAAKqC,gBAAgBF,KAAKnC,KACtD,CAMAkC,aAAAA,CAAcI,GACZC,MAAM,iKACNpB,QAAQC,IAAI,oBAAsBkB,EAAME,KAC1C,CAOAC,eAAAA,CAAgBC,GACd,SAAKA,GAAkB,qBAANA,QAA2BC,IAAND,IAGlB,oBAANA,CAChB,CAMAL,eAAAA,CAAgBC,GACd,MAAMM,EAAMC,KAAKC,MAAMR,EAAME,MAE7B,OAAQI,EAAIG,UACV,IAAK,iBAGG/C,KAAKyC,gBAAgBzC,KAAKgD,wBAC5BhD,KAAKgD,sBAAsBJ,GAE7B,MAEJ,IAAK,eAGD5C,KAAKe,WAAa6B,EAAI7B,WAClBf,KAAKyC,gBAAgBzC,KAAKiD,sBAC5BjD,KAAKiD,oBAAoBL,GAE3B,MAEJ,IAAK,mBAGD5C,KAAKC,mBAAmBI,YAAcuC,EAAIM,aAC1ClD,KAAKC,mBAAmBK,WAAasC,EAAItC,WACzCN,KAAKC,mBAAmBM,eAAiBqC,EAAIrC,eAC7CP,KAAKC,mBAAmBO,SAAWoC,EAAIpC,SACvCR,KAAKC,mBAAmBQ,SAAWmC,EAAInC,SACvCT,KAAKC,mBAAmBS,WAAakC,EAAIlC,WACzCV,KAAKC,mBAAmBU,oBAAsBiC,EAAIjC,oBAE9CX,KAAKyC,gBAAgBzC,KAAKmD,0BAC5BnD,KAAKmD,wBAAwBP,GAE/B,MAEJ,IAAK,gBAGD5C,KAAKe,WAAa6B,EAAI7B,WAClBf,KAAKyC,gBAAgBzC,KAAKoD,uBAC5BpD,KAAKoD,qBAAqBR,GAE5B,MAEJ,IAAK,cAGD5C,KAAKe,WAAa6B,EAAI7B,WAClBf,KAAKyC,gBAAgBzC,KAAKqD,qBAC5BrD,KAAKqD,mBAAmBT,GAE1B,MAEJ,IAAK,eAGD5C,KAAKe,WAAa6B,EAAI7B,WAClBf,KAAKyC,gBAAgBzC,KAAKsD,sBAC5BtD,KAAKsD,oBAAoBV,GAE3B,MAEJ,IAAK,mBAGD5C,KAAKe,WAAa6B,EAAI7B,WAClBf,KAAKyC,gBAAgBzC,KAAKuD,0BAC5BvD,KAAKuD,wBAAwBX,GAE/B,MAEJ,IAAK,cAGD5C,KAAKe,WAAa6B,EAAI7B,WAClBf,KAAKyC,gBAAgBzC,KAAKwD,qBAC5BxD,KAAKwD,mBAAmBZ,GAE1B,MAEJ,IAAK,eAGD5C,KAAKe,WAAa6B,EAAI7B,WAClBf,KAAKyC,gBAAgBzC,KAAKyD,sBAC5BzD,KAAKyD,oBAAoBb,GAE3B,MAEJ,IAAK,2BAGG5C,KAAKyC,gBAAgBzC,KAAK0D,kCAC5B1D,KAAK0D,gCAAgCd,GAEvC,MAEJ,IAAK,4BAGG5C,KAAKyC,gBAAgBzC,KAAK2D,mCAC5B3D,KAAK2D,iCAAiCf,GAExC,MAEJ,IAAK,wBAGG5C,KAAKyC,gBAAgBzC,KAAK4D,+BAC5B5D,KAAK4D,6BAA6BhB,GAEpC,MAEJ,IAAK,SAED5C,KAAKe,WAAa6B,EAAI7B,WAElBf,KAAKyC,gBAAgBzC,KAAK6D,gBAC5B7D,KAAK6D,cAAcjB,GAErB,MAEJ,IAAK,cAGG5C,KAAKyC,gBAAgBzC,KAAK8D,qBAC5B9D,KAAK8D,mBAAmBlB,GAE1B,MAEJ,IAAK,YAGG5C,KAAKyC,gBAAgBzC,KAAK+D,mBAC5B/D,KAAK+D,iBAAiBnB,GAKhC,CAMAoB,oBAAAA,CAAqBC,GACnB,IACmC,IAA7BjE,KAAKc,SAASoD,WAChBlE,KAAKc,SAASqD,KAAKtB,KAAKuB,UAAUH,IAElC1B,MAAM,oMAEV,CAAE,MAAO8B,GACP9B,MAAM,yDAAc8B,EACtB,CACF,CASAC,aAAAA,CAAcC,EAAaC,EAAMC,EAAMC,GACrC,MAAMC,EAAS,CACb5B,SAAU,gBACVwB,YAAaA,EACbC,KAAMA,EACNC,KAAMA,EACNG,IAAKF,GAEP1E,KAAKgE,qBAAqBW,EAC5B,CAKA5C,WAAAA,GACE,MAAM4C,EAAS,CACb5B,SAAU,kBAEZ/C,KAAKgE,qBAAqBW,EAC5B,CAMAE,gBAAAA,CAAiBxE,GACf,MAAMsE,EAAS,CACb5B,SAAU,mBACV1C,YAAaA,GAEfL,KAAKgE,qBAAqBW,EAC5B,CAKAG,SAAAA,GACE,MAAMH,EAAS,CACb5B,SAAU,YACVgC,OAAQ/E,KAAKC,oBAEfD,KAAKgE,qBAAqBW,EAC5B,CAKAK,QAAAA,GACE,MAAML,EAAS,CACb5B,SAAU,YAEZ/C,KAAKgE,qBAAqBW,EAC5B,CAKAM,aAAAA,GACE,MAAMN,EAAS,CACb5B,SAAU,iBAEZ/C,KAAKgE,qBAAqBW,EAC5B,CAKAO,WAAAA,GACE,MAAMP,EAAS,CACb5B,SAAU,eAEZ/C,KAAKgE,qBAAqBW,EAC5B,CAMAQ,YAAAA,CAAaC,GACX,MAAMT,EAAS,CACb5B,SAAU,eACVqC,MAAOA,GAETpF,KAAKgE,qBAAqBW,EAC5B,CAMAU,gBAAAA,CAAiBT,GACf,MAAMD,EAAS,CACb5B,SAAU,mBACV6B,IAAKA,GAEP5E,KAAKgE,qBAAqBW,EAC5B,CAOAW,WAAAA,CAAYF,EAAOG,GACjB,MAAMZ,EAAS,CACb5B,SAAU,cACVqC,MAAOA,EACPG,MAAOA,GAETvF,KAAKgE,qBAAqBW,EAC5B,CAMAa,YAAAA,CAAaJ,GACX,MAAMT,EAAS,CACb5B,SAAU,eACVqC,MAAOA,GAETpF,KAAKgE,qBAAqBW,EAC5B,CAMAc,kBAAAA,CAAmBL,GACjB,MAAMT,EAAS,CACb5B,SAAU,qBACVqC,MAAOA,GAETpF,KAAKgE,qBAAqBW,EAC5B,CAQAe,wBAAAA,CAAyBd,EAAKe,EAAIC,GAChC,MAAMjB,EAAS,CACb5B,SAAU,2BACV6B,IAAKA,EACLe,GAAIA,EACJC,KAAMA,GAER5F,KAAKgE,qBAAqBW,EAC5B,CAQAkB,yBAAAA,CAA0BjB,EAAKe,EAAIC,GACjC,MAAMjB,EAAS,CACb5B,SAAU,4BACV6B,IAAKA,EACLe,GAAIA,EACJC,KAAMA,GAER5F,KAAKgE,qBAAqBW,EAC5B,CASAmB,qBAAAA,CAAsBlB,EAAKe,EAAIC,EAAMR,GACnC,MAAMT,EAAS,CACb5B,SAAU,wBACVqC,MAAOA,EACPR,IAAKA,EACLe,GAAIA,EACJC,KAAMA,GAER5F,KAAKgE,qBAAqBW,EAC5B,CAMAoB,mBAAAA,CAAoBC,GAClB,MAAMrB,EAAS,CACb5B,SAAU,sBACViD,SAAUA,GAEZhG,KAAKgE,qBAAqBW,EAC5B,CAKAsB,mBAAAA,GACE,MAAMtB,EAAS,CACb5B,SAAU,uBAEZ/C,KAAKgE,qBAAqBW,EAC5B,CAWAuB,iBAAAA,CAAkBC,EAAUC,EAAMC,EAAUC,EAAUC,EAAYP,GAChE,MAAMrB,EAAS,CACb5B,SAAU,oBACVoD,SAAUA,EACVC,KAAMA,EACNC,SAAUA,EACVC,SAAUA,EACVC,WAAYA,EACZP,SAAUA,GAEZhG,KAAKgE,qBAAqBW,EAC5B,CAMA6B,sBAAAA,CAAuBC,GACrB,MAAM9B,EAAS,CACb5B,SAAU,yBACV0D,QAASA,GAEXzG,KAAKgE,qBAAqBW,EAC5B,CAKA+B,QAAAA,GACE,MAAM/B,EAAS,CACb5B,SAAU,SAEZ/C,KAAKgE,qBAAqBW,EAC5B,CAKAgC,MAAAA,GACE,MAAMhC,EAAS,CACb5B,SAAU,UAEZ/C,KAAKgE,qBAAqBW,EAC5B,CAKAiC,cAAAA,GACE5G,KAAKc,SAAS+F,OAChB,EAaF,OATqCjH,EAAOC,UAC1CD,EAAOC,QAAUC,GAIG,qBAAXgH,SACTA,OAAOhH,UAAYA,GAGdA,CAET,CAhkBkFiH", "sources": ["../node_modules/scanonweb/dist/scanonweb.umd.js"], "sourcesContent": ["/*!\n * scanonweb v1.0.1\n * ScanOnWeb - 扫描控件 JavaScript SDK，用于与本地扫描服务程序通信\n * https://www.brainysoft.cn\n * \n * Copyright (c) 2025 BrainySoft\n * Licensed under the MIT license\n */\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n  typeof define === 'function' && define.amd ? define(factory) :\n  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.ScanOnWeb = factory());\n})(this, (function () { 'use strict';\n\n  /**\n   * ScanOnWeb - 扫描控件 JavaScript SDK\n   * https://www.brainysoft.cn   扫描控件官方网站,如需更多文档帮助请访问官网\n   * @version 1.0.1\n   * <AUTHOR>\n   * @license MIT\n   */\n\n  /**\n   * 扫描控件类 - 用于与本地扫描服务程序通信\n   * @class ScanOnWeb\n   */\n  class ScanOnWeb {\n    constructor() {\n      // 扫描配置参数信息,后续扫描仪开始扫描前会根据这个配置信息来进行扫描设置\n      this.scaner_work_config = {\n        showUI: false,\n        // 是否显示扫描控件工作界面，如果为false则不显示扫描控件工作界面\n        dpi_x: 300,\n        // dpi 分辨率x，一般的图像扫描300dpi就够了\n        dpi_y: 300,\n        // dpi 分辨率y\n        deviceIndex: 0,\n        // 选中的扫描仪硬件设备id索引，如果有多个扫描仪设备，这个值就是用来选择哪个设备进行扫描的\n        showDialog: false,\n        // 是否显示设备内置对话框\n        autoFeedEnable: true,\n        // 是否使用自动进纸器(需要设备支持才能正常工作)\n        autoFeed: false,\n        // 是否自动装填纸张(需要设备支持才能正常工作)\n        dupxMode: false,\n        // 是否使用双面扫描模式(需要设备支持才能正常工作)\n        autoDeskew: false,\n        // 是否使用自动纠偏模式(需要设备支持才能正常工作)\n        autoBorderDetection: false,\n        // 是否使用自动边框检测(需要设备支持才能正常工作)\n        colorMode: \"RGB\",\n        // 色彩模式,RGB为彩色模式,BW是黑白模式 ,GRAY是灰色模式\n        transMode: \"memory\" // 数据传输模式,memory,file,native 这三种配置值,默认为memory模式，大部分设备都是使用这种模式\n      };\n      this.h5socket = null;\n      this.imageCount = 0; // 扫描结果图像总数\n\n      // 尝试连接websocket服务器,注意连接成功哪个是通过回调实现的\n      this.tryConnect();\n    }\n\n    /**\n     * 通过连接多个websocket server端口返回一个可用的websocket连接对象,主要用于防止本地端口被占用的情况\n     * @param {string[]} wssUrls - WebSocket服务器URL数组\n     * @returns {Promise} WebSocket连接Promise\n     */\n    getConnectedServer(wssUrls) {\n      console.log(\"尝试连接托盘扫描服务websocket服务器...\");\n      return new Promise((resolve, reject) => {\n        const server = new WebSocket(wssUrls[0]);\n        server.onopen = () => {\n          resolve(server);\n        };\n        server.onerror = err => {\n          reject(err);\n        };\n      }).then(server => {\n        console.log(\"连接websocket服务器成功!\");\n        // 找到了可用websocket服务器端口\n        this.initWebsocketCallback(server);\n        console.log(\"尝试获取扫描设备列表...\");\n        // 发送一个获取扫描设备列表的命令,询问本地计算机安装了多少个扫描设备的驱动程序\n        this.loadDevices();\n        return server;\n      }, err => {\n        // 如果连接失败,则尝试连接其他端口\n        if (wssUrls.length > 1) {\n          return this.getConnectedServer(wssUrls.slice(1));\n        }\n        throw err;\n      });\n    }\n\n    /**\n     * 尝试检测websocket哪个端口可以成功连接\n     */\n    tryConnect() {\n      // 以下一共定义了5个websocket端口,如果有端口被占用,则会自动尝试连接下一个端口\n      const wssUrls = [\"ws://127.0.0.1:1001\", \"ws://127.0.0.1:2001\", \"ws://127.0.0.1:3001\", \"ws://127.0.0.1:4001\", \"ws://127.0.0.1:5001\"];\n      this.getConnectedServer(wssUrls);\n    }\n\n    /**\n     * 初始化websocket相关的函数绑定\n     * @param {WebSocket} server - WebSocket服务器实例\n     */\n    initWebsocketCallback(server) {\n      this.h5socket = server;\n      this.h5socket.onerror = this.onSocketError.bind(this);\n      this.h5socket.onmessage = this.onSocketMessage.bind(this);\n    }\n\n    /**\n     * WebSocket错误处理\n     * @param {Event} event - 错误事件\n     */\n    onSocketError(event) {\n      alert(\"无法连接扫描服务程序,请检查扫描服务程序是否已经启动！\");\n      console.log(\"WebSocket error: \" + event.data);\n    }\n\n    /**\n     * 判断回调函数是否存在\n     * @param {Function} f - 要检查的函数\n     * @returns {boolean} 函数是否存在且为函数类型\n     */\n    isCallbackExist(f) {\n      if (!f || typeof f === \"undefined\" || f === undefined) {\n        return false;\n      }\n      return typeof f === \"function\";\n    }\n\n    /**\n     * WebSocket消息处理\n     * @param {MessageEvent} event - WebSocket消息事件\n     */\n    onSocketMessage(event) {\n      const msg = JSON.parse(event.data);\n      // console.log(msg);\n      switch (msg.cmd_type) {\n        case \"getDevicesList\":\n          {\n            // 获取设备信息列表返回结果\n            if (this.isCallbackExist(this.onGetDevicesListEvent)) {\n              this.onGetDevicesListEvent(msg);\n            }\n            break;\n          }\n        case \"scanComplete\":\n          {\n            // 扫描完成\n            this.imageCount = msg.imageCount;\n            if (this.isCallbackExist(this.onScanFinishedEvent)) {\n              this.onScanFinishedEvent(msg);\n            }\n            break;\n          }\n        case \"selectScanDevice\":\n          {\n            // 选择扫描设备结果\n            this.scaner_work_config.deviceIndex = msg.currentIndex; // 当前选中的设备索引\n            this.scaner_work_config.showDialog = msg.showDialog; // 是否显示设备内置对话框\n            this.scaner_work_config.autoFeedEnable = msg.autoFeedEnable; // 是否使用自动进纸器(需要设备支持才能正常工作)\n            this.scaner_work_config.autoFeed = msg.autoFeed; // 是否自动装填纸张(需要设备支持才能正常工作)\n            this.scaner_work_config.dupxMode = msg.dupxMode; // 是否使用双面扫描模式(需要设备支持才能正常工作)\n            this.scaner_work_config.autoDeskew = msg.autoDeskew; // 是否使用自动纠偏模式(需要设备支持才能正常工作)\n            this.scaner_work_config.autoBorderDetection = msg.autoBorderDetection; // 是否使用自动边框检测(需要设备支持才能正常工作)\n\n            if (this.isCallbackExist(this.onSelectScanDeviceEvent)) {\n              this.onSelectScanDeviceEvent(msg);\n            }\n            break;\n          }\n        case \"getImageCount\":\n          {\n            // 获取扫描图片数量\n            this.imageCount = msg.imageCount;\n            if (this.isCallbackExist(this.onGetImageCountEvent)) {\n              this.onGetImageCountEvent(msg);\n            }\n            break;\n          }\n        case \"getAllImage\":\n          {\n            // 获取所有图片\n            this.imageCount = msg.imageCount;\n            if (this.isCallbackExist(this.onGetAllImageEvent)) {\n              this.onGetAllImageEvent(msg);\n            }\n            break;\n          }\n        case \"getImageById\":\n          {\n            // 获取某一页图片的base64编码数据\n            this.imageCount = msg.imageCount;\n            if (this.isCallbackExist(this.onGetImageByIdEvent)) {\n              this.onGetImageByIdEvent(msg);\n            }\n            break;\n          }\n        case \"loadImageFromUrl\":\n          {\n            // 从URL加载图片\n            this.imageCount = msg.imageCount;\n            if (this.isCallbackExist(this.onLoadImageFromUrlEvent)) {\n              this.onLoadImageFromUrlEvent(msg);\n            }\n            break;\n          }\n        case \"rotateImage\":\n          {\n            // 旋转图片\n            this.imageCount = msg.imageCount;\n            if (this.isCallbackExist(this.onRotateImageEvent)) {\n              this.onRotateImageEvent(msg);\n            }\n            break;\n          }\n        case \"getImageSize\":\n          {\n            // 获取图片尺寸\n            this.imageCount = msg.imageCount;\n            if (this.isCallbackExist(this.onGetImageSizeEvent)) {\n              this.onGetImageSizeEvent(msg);\n            }\n            break;\n          }\n        case \"uploadAllImageAsPdfToUrl\":\n          {\n            // 上传pdf图像到指定的URL的回调\n            if (this.isCallbackExist(this.onUploadAllImageAsPdfToUrlEvent)) {\n              this.onUploadAllImageAsPdfToUrlEvent(msg);\n            }\n            break;\n          }\n        case \"uploadAllImageAsTiffToUrl\":\n          {\n            // 上传tiff图像到指定的URL的回调\n            if (this.isCallbackExist(this.onUploadAllImageAsTiffToUrlEvent)) {\n              this.onUploadAllImageAsTiffToUrlEvent(msg);\n            }\n            break;\n          }\n        case \"uploadJpgImageByIndex\":\n          {\n            // 上传jpg图像到指定的URL的回调\n            if (this.isCallbackExist(this.onUploadJpgImageByIndexEvent)) {\n              this.onUploadJpgImageByIndexEvent(msg);\n            }\n            break;\n          }\n        case \"upload\":\n          {\n            this.imageCount = msg.imageCount;\n            // 用户点击了界面里面的\"开始上传\"按钮的时间回调\n            if (this.isCallbackExist(this.onUploadEvent)) {\n              this.onUploadEvent(msg);\n            }\n            break;\n          }\n        case \"imageEdited\":\n          {\n            // 用户在扫描托盘程序里面对图片进行了编辑操作的回调\n            if (this.isCallbackExist(this.onImageEditedEvent)) {\n              this.onImageEditedEvent(msg);\n            }\n            break;\n          }\n        case \"imageDrap\":\n          {\n            // 用户在扫描托盘程序里面对图片进行了拖拽操作的回调\n            if (this.isCallbackExist(this.onImageDrapEvent)) {\n              this.onImageDrapEvent(msg);\n            }\n            break;\n          }\n      }\n    }\n\n    /**\n     * 通过websocket发送数据给webscoket服务端\n     * @param {Object} commandData - 要发送的命令数据\n     */\n    sendWebSocketCommand(commandData) {\n      try {\n        if (this.h5socket.readyState === 1) {\n          this.h5socket.send(JSON.stringify(commandData));\n        } else {\n          alert(\"发送扫描指令失败！请刷新页面或者检查托盘扫描程序是否已经正常运行!\");\n        }\n      } catch (e) {\n        alert(\"发送扫描指令失败！\" + e);\n      }\n    }\n\n    /**\n     * 设置授权信息\n     * @param {string} licenseMode - 授权模式\n     * @param {string} key1 - 授权密钥1\n     * @param {string} key2 - 授权密钥2\n     * @param {string} licenseServerUrl - 授权服务器URL\n     */\n    setLicenseKey(licenseMode, key1, key2, licenseServerUrl) {\n      const cmdObj = {\n        cmd_type: \"setLicenseKey\",\n        licenseMode: licenseMode,\n        key1: key1,\n        key2: key2,\n        url: licenseServerUrl\n      };\n      this.sendWebSocketCommand(cmdObj);\n    }\n\n    /**\n     * 加载所有可用的扫描设备\n     */\n    loadDevices() {\n      const cmdObj = {\n        cmd_type: \"getDevicesList\"\n      };\n      this.sendWebSocketCommand(cmdObj);\n    }\n\n    /**\n     * 设置当前选中的扫描设备id\n     * @param {number} deviceIndex - 设备索引\n     */\n    selectScanDevice(deviceIndex) {\n      const cmdObj = {\n        cmd_type: \"selectScanDevice\",\n        deviceIndex: deviceIndex\n      };\n      this.sendWebSocketCommand(cmdObj);\n    }\n\n    /**\n     * 开始扫描\n     */\n    startScan() {\n      const cmdObj = {\n        cmd_type: \"startScan\",\n        config: this.scaner_work_config\n      };\n      this.sendWebSocketCommand(cmdObj);\n    }\n\n    /**\n     * 清除全部扫描结果\n     */\n    clearAll() {\n      const cmdObj = {\n        cmd_type: \"clearAll\"\n      };\n      this.sendWebSocketCommand(cmdObj);\n    }\n\n    /**\n     * 获取图像总数\n     */\n    getImageCount() {\n      const cmdObj = {\n        cmd_type: \"getImageCount\"\n      };\n      this.sendWebSocketCommand(cmdObj);\n    }\n\n    /**\n     * 获取所有图像\n     */\n    getAllImage() {\n      const cmdObj = {\n        cmd_type: \"getAllImage\"\n      };\n      this.sendWebSocketCommand(cmdObj);\n    }\n\n    /**\n     * 发送指令获取某一页图像到托盘服务\n     * @param {number} index - 图像索引\n     */\n    getImageById(index) {\n      const cmdObj = {\n        cmd_type: \"getImageById\",\n        index: index\n      };\n      this.sendWebSocketCommand(cmdObj);\n    }\n\n    /**\n     * 发送指令远程加载服务器端的多页图像到托盘服务\n     * @param {string} url - 图像URL\n     */\n    loadImageFromUrl(url) {\n      const cmdObj = {\n        cmd_type: \"loadImageFromUrl\",\n        url: url\n      };\n      this.sendWebSocketCommand(cmdObj);\n    }\n\n    /**\n     * 发送指令旋转某一页图像到托盘服务\n     * @param {number} index - 图像索引\n     * @param {number} angle - 旋转角度\n     */\n    rotateImage(index, angle) {\n      const cmdObj = {\n        cmd_type: \"rotateImage\",\n        index: index,\n        angle: angle\n      };\n      this.sendWebSocketCommand(cmdObj);\n    }\n\n    /**\n     * 发送指令获取某一页图像的宽度到托盘服务\n     * @param {number} index - 图像索引\n     */\n    getImageSize(index) {\n      const cmdObj = {\n        cmd_type: \"getImageSize\",\n        index: index\n      };\n      this.sendWebSocketCommand(cmdObj);\n    }\n\n    /**\n     * 发送指令删除某一页图像到托盘服务\n     * @param {number} index - 图像索引\n     */\n    deleteImageByIndex(index) {\n      const cmdObj = {\n        cmd_type: \"deleteImageByIndex\",\n        index: index\n      };\n      this.sendWebSocketCommand(cmdObj);\n    }\n\n    /**\n     * 以pdf格式上传全部图像到服务器端\n     * @param {string} url - 上传URL\n     * @param {string} id - 标识ID\n     * @param {string} desc - 描述信息\n     */\n    uploadAllImageAsPdfToUrl(url, id, desc) {\n      const cmdObj = {\n        cmd_type: \"uploadAllImageAsPdfToUrl\",\n        url: url,\n        id: id,\n        desc: desc\n      };\n      this.sendWebSocketCommand(cmdObj);\n    }\n\n    /**\n     * 以tiff格式上传全部图像到服务器端\n     * @param {string} url - 上传URL\n     * @param {string} id - 标识ID\n     * @param {string} desc - 描述信息\n     */\n    uploadAllImageAsTiffToUrl(url, id, desc) {\n      const cmdObj = {\n        cmd_type: \"uploadAllImageAsTiffToUrl\",\n        url: url,\n        id: id,\n        desc: desc\n      };\n      this.sendWebSocketCommand(cmdObj);\n    }\n\n    /**\n     * 以jpg格式上传某一页图像到服务器端\n     * @param {string} url - 上传URL\n     * @param {string} id - 标识ID\n     * @param {string} desc - 描述信息\n     * @param {number} index - 图像索引\n     */\n    uploadJpgImageByIndex(url, id, desc, index) {\n      const cmdObj = {\n        cmd_type: \"uploadJpgImageByIndex\",\n        index: index,\n        url: url,\n        id: id,\n        desc: desc\n      };\n      this.sendWebSocketCommand(cmdObj);\n    }\n\n    /**\n     * 全部图像保存到客户端本地文件\n     * @param {string} filename - 文件名\n     */\n    saveAllImageToLocal(filename) {\n      const cmdObj = {\n        cmd_type: \"saveAllImageToLocal\",\n        filename: filename\n      };\n      this.sendWebSocketCommand(cmdObj);\n    }\n\n    /**\n     * 从客户端本地读取图像,通过打开文件对话框选择图像文件\n     */\n    openClientLocalfile() {\n      const cmdObj = {\n        cmd_type: \"openClientLocalfile\"\n      };\n      this.sendWebSocketCommand(cmdObj);\n    }\n\n    /**\n     * ftp上传全部图像文件到服务器端\n     * @param {string} serverIp - 服务器IP\n     * @param {number} port - 端口\n     * @param {string} username - 用户名\n     * @param {string} password - 密码\n     * @param {string} serverPath - 服务器路径\n     * @param {string} filename - 文件名\n     */\n    ftpUploadAllImage(serverIp, port, username, password, serverPath, filename) {\n      const cmdObj = {\n        cmd_type: \"ftpUploadAllImage\",\n        serverIp: serverIp,\n        port: port,\n        username: username,\n        password: password,\n        serverPath: serverPath,\n        filename: filename\n      };\n      this.sendWebSocketCommand(cmdObj);\n    }\n\n    /**\n     * 设置上传按钮是否可见\n     * @param {boolean} visible - 是否可见\n     */\n    setUploadButtonVisible(visible) {\n      const cmdObj = {\n        cmd_type: \"setUploadButtonVisible\",\n        visible: visible\n      };\n      this.sendWebSocketCommand(cmdObj);\n    }\n\n    /**\n     * 设置焦点\n     */\n    setFocus() {\n      const cmdObj = {\n        cmd_type: \"focus\"\n      };\n      this.sendWebSocketCommand(cmdObj);\n    }\n\n    /**\n     * 隐藏窗口\n     */\n    hidden() {\n      const cmdObj = {\n        cmd_type: \"hidden\"\n      };\n      this.sendWebSocketCommand(cmdObj);\n    }\n\n    /**\n     * 关闭websocket连接\n     */\n    closeWebSocket() {\n      this.h5socket.close();\n    }\n  }\n\n  // CommonJS 兼容性导出\n  if (typeof module !== \"undefined\" && module.exports) {\n    module.exports = ScanOnWeb;\n  }\n\n  // UMD 兼容性导出\n  if (typeof window !== \"undefined\") {\n    window.ScanOnWeb = ScanOnWeb;\n  }\n\n  return ScanOnWeb;\n\n}));\n"], "names": ["module", "exports", "ScanOnWeb", "constructor", "this", "scaner_work_config", "showUI", "dpi_x", "dpi_y", "deviceIndex", "showDialog", "autoFeedEnable", "autoFeed", "dupxMode", "autoDeskew", "autoBorderDetection", "colorMode", "transMode", "h5socket", "imageCount", "tryConnect", "getConnectedServer", "wssUrls", "console", "log", "Promise", "resolve", "reject", "server", "WebSocket", "onopen", "onerror", "err", "then", "initWebsocketCallback", "loadDevices", "length", "slice", "onSocketError", "bind", "onmessage", "onSocketMessage", "event", "alert", "data", "isCallbackExist", "f", "undefined", "msg", "JSON", "parse", "cmd_type", "onGetDevicesListEvent", "onScanFinishedEvent", "currentIndex", "onSelectScanDeviceEvent", "onGetImageCountEvent", "onGetAllImageEvent", "onGetImageByIdEvent", "onLoadImageFromUrlEvent", "onRotateImageEvent", "onGetImageSizeEvent", "onUploadAllImageAsPdfToUrlEvent", "onUploadAllImageAsTiffToUrlEvent", "onUploadJpgImageByIndexEvent", "onUploadEvent", "onImageEditedEvent", "onImageDrapEvent", "sendWebSocketCommand", "commandData", "readyState", "send", "stringify", "e", "setLicenseKey", "licenseMode", "key1", "key2", "licenseServerUrl", "cmdObj", "url", "selectScanDevice", "startScan", "config", "clearAll", "getImageCount", "getAllImage", "getImageById", "index", "loadImageFromUrl", "rotateImage", "angle", "getImageSize", "deleteImageByIndex", "uploadAllImageAsPdfToUrl", "id", "desc", "uploadAllImageAsTiffToUrl", "uploadJpgImageByIndex", "saveAllImageToLocal", "filename", "openClientLocalfile", "ftpUploadAllImage", "serverIp", "port", "username", "password", "serverPath", "setUploadButtonVisible", "visible", "setFocus", "hidden", "closeWebSocket", "close", "window", "factory"], "sourceRoot": ""}