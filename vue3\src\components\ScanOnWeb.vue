<!-- eslint-disable vue/no-unused-components, no-unused-vars -->
<template>
  <div class="scan-container">
    <!-- 页面标题 -->
    <el-card class="header-card">
      <h1 class="page-title">
        <el-icon><Camera /></el-icon>
        文档扫描系统
      </h1>
      <p class="page-subtitle">专业的文档扫描与管理解决方案</p>
    </el-card>

    <!-- 连接状态指示器 -->
    <el-alert
      v-if="!isConnected"
      title="扫描服务未连接"
      description="请确保扫描服务程序已启动并正在运行"
      type="warning"
      :closable="false"
      show-icon
      class="connection-alert"
    />

    <!-- 扫描配置区域 -->
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <el-icon><Setting /></el-icon>
          <span>扫描配置</span>
        </div>
      </template>

      <el-row :gutter="20">
        <!-- 设备选择 -->
        <el-col :span="12">
          <el-form-item label="扫描设备">
            <el-select 
              v-model="selectedDevice" 
              @change="onDeviceChange"
              placeholder="请选择扫描设备"
              style="width: 100%"
            >
              <el-option
                v-for="(device, index) in devices"
                :key="index"
                :label="device"
                :value="index"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <!-- 色彩模式 -->
        <el-col :span="12">
          <el-form-item label="色彩模式">
            <el-select v-model="config.colorMode" style="width: 100%">
              <el-option label="彩色" value="RGB" />
              <el-option label="灰度" value="GRAY" />
              <el-option label="黑白" value="BW" />
            </el-select>
          </el-form-item>
        </el-col>

        <!-- 分辨率设置 -->
        <el-col :span="12">
          <el-form-item label="分辨率 (DPI)">
            <el-row :gutter="10">
              <el-col :span="11">
                <el-input-number 
                  v-model="config.dpi_x" 
                  :min="75" 
                  :max="1200" 
                  :step="25"
                  style="width: 100%"
                />
              </el-col>
              <el-col :span="2" class="dpi-separator">×</el-col>
              <el-col :span="11">
                <el-input-number 
                  v-model="config.dpi_y" 
                  :min="75" 
                  :max="1200" 
                  :step="25"
                  style="width: 100%"
                />
              </el-col>
            </el-row>
          </el-form-item>
        </el-col>

        <!-- 高级选项 -->
        <el-col :span="12">
          <el-form-item label="高级选项">
            <div class="advanced-options-compact">
              <div class="options-row">
                <el-checkbox v-model="config.showDialog">设备对话框</el-checkbox>
                <el-checkbox v-model="config.autoFeedEnable">自动进纸</el-checkbox>
                <el-checkbox v-model="config.dupxMode">双面扫描</el-checkbox>
              </div>
              <div class="options-row">
                <el-checkbox v-model="config.autoDeskew">自动纠偏</el-checkbox>
                <el-checkbox v-model="config.autoBorderDetection">边框检测</el-checkbox>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-card>

    <!-- 操作按钮区域 -->
    <el-card class="action-card">
      <el-row :gutter="15">
        <el-col :span="4">
          <el-button
            type="primary"
            @click="loadDevices"
            :loading="loading.devices"
            style="width: 100%"
          >
            <el-icon><Refresh /></el-icon>
            刷新设备
          </el-button>
        </el-col>
        <el-col :span="4">
          <el-button
            type="success"
            @click="startScan"
            :loading="loading.scan"
            :disabled="selectedDevice === -1 || !isConnected"
            style="width: 100%"
          >
            <el-icon><Camera /></el-icon>
            开始扫描
          </el-button>
        </el-col>
        <el-col :span="4">
          <el-button
            type="info"
            @click="getAllImage"
            :loading="loading.images"
            style="width: 100%"
          >
            <el-icon><Picture /></el-icon>
            获取图像
          </el-button>
        </el-col>
        <el-col :span="4">
          <el-button
            type="warning"
            @click="clearAll"
            style="width: 100%"
          >
            🗑️ 清空结果
          </el-button>
        </el-col>
        <el-col :span="4">
          <el-button
            type="primary"
            @click="showUploadDialog"
            :disabled="images.length === 0"
            style="width: 100%"
          >
            📤 上传文档
          </el-button>
        </el-col>
        <el-col :span="4">
          <el-button
            type="default"
            @click="saveAs"
            :disabled="images.length === 0"
            style="width: 100%"
          >
            💾 本地保存
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 扫描结果展示区域 -->
    <el-card class="result-card" v-if="images.length > 0">
      <template #header>
        <div class="card-header">
          <el-icon><Picture /></el-icon>
          <span>扫描结果 ({{ images.length }} 张图像)</span>
          <div class="header-actions">
            <el-button-group>
              <el-button
                :type="viewMode === 'grid' ? 'primary' : 'default'"
                @click="viewMode = 'grid'"
                size="small"
              >
                🔲 网格
              </el-button>
              <el-button
                :type="viewMode === 'list' ? 'primary' : 'default'"
                @click="viewMode = 'list'"
                size="small"
              >
                📋 列表
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="image-grid">
        <div 
          v-for="(image, index) in images" 
          :key="index" 
          class="image-item"
        >
          <div class="image-wrapper">
            <img 
              :src="image.src" 
              :alt="`扫描图像 ${index + 1}`"
              @click="previewImage(index)"
              class="scan-image"
            />
            <div class="image-overlay">
              <el-button-group>
                <el-button
                  type="primary"
                  size="small"
                  @click="previewImage(index)"
                >
                  👁️
                </el-button>
                <el-button
                  type="success"
                  size="small"
                  @click="downloadImage(index)"
                >
                  💾
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="deleteImage(index)"
                >
                  🗑️
                </el-button>
              </el-button-group>
            </div>
          </div>
          <div class="image-info">
            <span class="image-index">图像 {{ index + 1 }}</span>
          </div>
        </div>
      </div>

      <!-- 列表视图 -->
      <el-table v-else :data="images" stripe>
        <el-table-column label="预览" width="120">
          <template #default="{ row, $index }">
            <img 
              :src="row.src" 
              class="table-thumbnail"
              @click="previewImage($index)"
            />
          </template>
        </el-table-column>
        <el-table-column label="图像编号" prop="index" width="100">
          <template #default="{ $index }">
            图像 {{ $index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="格式" width="100">
          <template #default>
            <el-tag type="info">JPEG</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ $index }">
            <el-button-group>
              <el-button
                type="primary"
                size="small"
                @click="previewImage($index)"
              >
                👁️ 预览
              </el-button>
              <el-button
                type="success"
                size="small"
                @click="downloadImage($index)"
              >
                💾 下载
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="deleteImage($index)"
              >
                🗑️ 删除
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 空状态 -->
    <el-empty 
      v-else 
      description="暂无扫描结果"
      class="empty-state"
    >
      <el-button 
        type="primary" 
        @click="startScan"
        :disabled="selectedDevice === -1 || !isConnected"
      >
        开始扫描
      </el-button>
    </el-empty>
  </div>

  <!-- 上传对话框 -->
  <el-dialog
    v-model="uploadDialog.visible"
    title="上传文档"
    width="500px"
    :close-on-click-modal="false"
  >
    <el-form :model="uploadDialog.form" label-width="100px">
      <el-form-item label="文档ID">
        <el-input v-model="uploadDialog.form.id" placeholder="请输入文档ID" />
      </el-form-item>
      <el-form-item label="文档描述">
        <el-input
          v-model="uploadDialog.form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入文档描述"
        />
      </el-form-item>
      <el-form-item label="上传格式">
        <el-radio-group v-model="uploadDialog.form.format">
          <el-radio label="pdf">PDF文档</el-radio>
          <el-radio label="tiff">TIFF图像</el-radio>
          <el-radio label="jpg">JPG图像</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="uploadDialog.visible = false">取消</el-button>
      <el-button
        type="primary"
        @click="confirmUpload"
        :loading="loading.upload"
      >
        确认上传
      </el-button>
    </template>
  </el-dialog>

  <!-- 图像预览对话框 -->
  <el-dialog
    v-model="previewDialog.visible"
    title="图像预览"
    width="80%"
    :close-on-click-modal="false"
  >
    <div class="preview-container" v-if="previewDialog.currentImage">
      <img
        :src="previewDialog.currentImage.src"
        class="preview-image"
        :alt="`预览图像 ${previewDialog.currentIndex + 1}`"
      />
    </div>
    <template #footer>
      <div class="preview-footer">
        <div class="preview-info">
          图像 {{ previewDialog.currentIndex + 1 }} / {{ images.length }}
        </div>
        <div class="preview-actions">
          <el-button
            @click="prevImage"
            :disabled="previewDialog.currentIndex <= 0"
          >
            ⬅️ 上一张
          </el-button>
          <el-button
            @click="nextImage"
            :disabled="previewDialog.currentIndex >= images.length - 1"
          >
            下一张 ➡️
          </el-button>
          <el-button
            type="success"
            @click="downloadCurrentImage"
          >
            💾 下载
          </el-button>
          <el-button @click="previewDialog.visible = false">关闭</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Camera, Setting, Picture, Refresh } from '@element-plus/icons-vue'
import ScanOnWeb from '@/scanonweb.js'
import { utils } from '@/services/api.js'

export default {
  name: 'ScanOnWeb',
  components: {
    Camera,
    Setting,
    Picture,
    Refresh
  },
  setup() {
    // 响应式数据
    const scanonweb = ref(null)
    const isConnected = ref(false)
    const devices = ref([])
    const selectedDevice = ref(-1)
    const images = ref([])
    const viewMode = ref('grid')

    // 扫描配置
    const config = reactive({
      dpi_x: 300,
      dpi_y: 300,
      colorMode: 'RGB',
      showDialog: false,
      autoFeedEnable: true,
      autoFeed: false,
      dupxMode: false,
      autoDeskew: false,
      autoBorderDetection: false
    })

    // 加载状态
    const loading = reactive({
      devices: false,
      scan: false,
      images: false,
      upload: false
    })

    // 上传对话框
    const uploadDialog = reactive({
      visible: false,
      form: {
        id: '',
        description: '',
        format: 'pdf'
      }
    })

    // 预览对话框
    const previewDialog = reactive({
      visible: false,
      currentIndex: 0,
      currentImage: null
    })

    // 初始化扫描服务
    const initScanService = () => {
      try {
        scanonweb.value = new ScanOnWeb()

        // 设置事件回调
        scanonweb.value.onGetDevicesListEvent = (msg) => {
          devices.value = msg.devices || []
          // 自动选择第一个设备，如果没有当前设备则选择第一个
          if (devices.value.length > 0) {
            selectedDevice.value = msg.currentIndex >= 0 ? msg.currentIndex : 0
            // 如果自动选择了设备，调用设备选择事件
            if (selectedDevice.value >= 0) {
              setTimeout(() => {
                onDeviceChange(selectedDevice.value)
              }, 100)
            }
          } else {
            selectedDevice.value = -1
          }
          loading.devices = false
          isConnected.value = true
          ElMessage.success(`发现 ${devices.value.length} 个扫描设备${selectedDevice.value >= 0 ? '，已自动选择第一个设备' : ''}`)
        }

        scanonweb.value.onScanFinishedEvent = (msg) => {
          loading.scan = false
          ElMessage.success(`扫描完成！共扫描 ${msg.imageAfterCount} 张图像`)
          getAllImage()
        }

        scanonweb.value.onGetAllImageEvent = (msg) => {
          loading.images = false
          if (msg.images && msg.images.length > 0) {
            images.value = msg.images.map((image, index) => ({
              src: `data:image/jpg;base64,${image}`,
              index: index,
              base64: image
            }))
            ElMessage.success(`获取到 ${images.value.length} 张图像`)
          } else {
            ElMessage.info('暂无扫描图像')
          }
        }

        scanonweb.value.onGetImageByIdEvent = (msg) => {
          if (msg.imageBase64) {
            addImage(msg.imageBase64)
          }
        }

        scanonweb.value.onImageEditedEvent = (msg) => {
          ElMessage.info(`图像 ${msg.imageIndex + 1} 已编辑`)
          if (msg.imageBase64) {
            editImage(msg.imageIndex, msg.imageBase64)
          }
        }

        scanonweb.value.onUploadEvent = () => {
          ElMessage.info('用户点击了上传按钮')
          showUploadDialog()
        }

        // 检查连接状态
        setTimeout(() => {
          if (devices.value.length === 0) {
            isConnected.value = false
            ElMessage.warning('扫描服务连接失败，请检查服务是否启动')
          }
        }, 3000)

      } catch (error) {
        console.error('初始化扫描服务失败:', error)
        isConnected.value = false
        ElMessage.error('初始化扫描服务失败')
      }
    }

    // 加载设备列表
    const loadDevices = async () => {
      if (!scanonweb.value) {
        ElMessage.error('扫描服务未初始化')
        return
      }

      loading.devices = true
      try {
        scanonweb.value.loadDevices()
      } catch (error) {
        loading.devices = false
        ElMessage.error('获取设备列表失败')
      }
    }

    // 设备选择变化
    const onDeviceChange = (deviceIndex) => {
      if (scanonweb.value) {
        scanonweb.value.selectScanDevice(deviceIndex)
        ElMessage.info(`已选择设备: ${devices.value[deviceIndex]}`)
      }
    }

    // 开始扫描
    const startScan = async () => {
      if (!scanonweb.value) {
        ElMessage.error('扫描服务未初始化')
        return
      }

      if (selectedDevice.value === -1) {
        ElMessage.warning('请先选择扫描设备')
        return
      }

      loading.scan = true
      try {
        // 更新扫描配置
        scanonweb.value.scaner_work_config = {
          ...scanonweb.value.scaner_work_config,
          ...config,
          deviceIndex: selectedDevice.value
        }

        scanonweb.value.startScan()
        ElMessage.info('开始扫描...')
      } catch (error) {
        loading.scan = false
        ElMessage.error('启动扫描失败')
      }
    }

    // 获取所有图像
    const getAllImage = async () => {
      if (!scanonweb.value) {
        ElMessage.error('扫描服务未初始化')
        return
      }

      loading.images = true
      try {
        scanonweb.value.getAllImage()
      } catch (error) {
        loading.images = false
        ElMessage.error('获取图像失败')
      }
    }

    // 清空所有图像
    const clearAll = async () => {
      try {
        await ElMessageBox.confirm('确定要清空所有扫描结果吗？', '确认操作', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        if (scanonweb.value) {
          scanonweb.value.clearAll()
          images.value = []
          ElMessage.success('已清空所有扫描结果')
        }
      } catch {
        // 用户取消操作
      }
    }

    // 添加图像
    const addImage = (imageBase64) => {
      images.value.push({
        src: `data:image/jpg;base64,${imageBase64}`,
        index: images.value.length,
        base64: imageBase64
      })
    }

    // 编辑图像
    const editImage = (index, imageBase64) => {
      if (index >= 0 && index < images.value.length) {
        images.value[index] = {
          src: `data:image/jpg;base64,${imageBase64}`,
          index: index,
          base64: imageBase64
        }
      }
    }

    // 显示上传对话框
    const showUploadDialog = () => {
      if (images.value.length === 0) {
        ElMessage.warning('没有可上传的图像')
        return
      }

      uploadDialog.form.id = utils.generateId()
      uploadDialog.form.description = `扫描文档_${new Date().toLocaleString()}`
      uploadDialog.visible = true
    }

    // 确认上传
    const confirmUpload = async () => {
      if (!uploadDialog.form.id.trim()) {
        ElMessage.warning('请输入文档ID')
        return
      }

      if (!scanonweb.value) {
        ElMessage.error('扫描服务未初始化')
        return
      }

      if (images.value.length === 0) {
        ElMessage.warning('没有可上传的图像')
        return
      }

      loading.upload = true
      try {
        const { id, description, format } = uploadDialog.form
        const uploadUrl = 'http://localhost:8080/upload'

        // 设置上传完成回调
        scanonweb.value.onUploadAllImageAsPdfToUrlEvent = (msg) => {
          console.log('PDF上传回调:', msg)
          loading.upload = false

          try {
            // 解析uploadResult字符串
            let uploadResult = null
            if (msg.uploadResult) {
              uploadResult = typeof msg.uploadResult === 'string'
                ? JSON.parse(msg.uploadResult)
                : msg.uploadResult
            }

            if (uploadResult && uploadResult.success) {
              ElMessage.success(`PDF文档上传成功！共 ${msg.imageCount || 0} 张图像`)
              uploadDialog.visible = false
              console.log('上传详情:', uploadResult)
            } else {
              const errorMsg = uploadResult?.message || msg.message || '未知错误'
              ElMessage.error(`PDF上传失败: ${errorMsg}`)
            }
          } catch (error) {
            console.error('解析上传结果失败:', error, msg)
            ElMessage.error(`PDF上传失败: 响应解析错误`)
          }
        }

        scanonweb.value.onUploadAllImageAsTiffToUrlEvent = (msg) => {
          console.log('TIFF上传回调:', msg)
          loading.upload = false

          try {
            // 解析uploadResult字符串
            let uploadResult = null
            if (msg.uploadResult) {
              uploadResult = typeof msg.uploadResult === 'string'
                ? JSON.parse(msg.uploadResult)
                : msg.uploadResult
            }

            if (uploadResult && uploadResult.success) {
              ElMessage.success(`TIFF文档上传成功！共 ${msg.imageCount || 0} 张图像`)
              uploadDialog.visible = false
              console.log('上传详情:', uploadResult)
            } else {
              const errorMsg = uploadResult?.message || msg.message || '未知错误'
              ElMessage.error(`TIFF上传失败: ${errorMsg}`)
            }
          } catch (error) {
            console.error('解析上传结果失败:', error, msg)
            ElMessage.error(`TIFF上传失败: 响应解析错误`)
          }
        }

        scanonweb.value.onUploadJpgImageByIndexEvent = (msg) => {
          console.log('JPG上传回调:', msg)
          loading.upload = false

          try {
            // 解析uploadResult字符串
            let uploadResult = null
            if (msg.uploadResult) {
              uploadResult = typeof msg.uploadResult === 'string'
                ? JSON.parse(msg.uploadResult)
                : msg.uploadResult
            }

            if (uploadResult && uploadResult.success) {
              ElMessage.success(`JPG图像上传成功！`)
              uploadDialog.visible = false
              console.log('上传详情:', uploadResult)
            } else {
              const errorMsg = uploadResult?.message || msg.message || '未知错误'
              ElMessage.error(`JPG上传失败: ${errorMsg}`)
            }
          } catch (error) {
            console.error('解析上传结果失败:', error, msg)
            ElMessage.error(`JPG上传失败: 响应解析错误`)
          }
        }

        // 调用控件内置的上传方法
        if (format === 'pdf') {
          ElMessage.info('开始上传PDF文档...')
          scanonweb.value.uploadAllImageAsPdfToUrl(uploadUrl, id, description)
        } else if (format === 'tiff') {
          ElMessage.info('开始上传TIFF文档...')
          const tiffUploadUrl = 'http://localhost:8080/upload-tiff'
          scanonweb.value.uploadAllImageAsTiffToUrl(tiffUploadUrl, id, description)
        } else if (format === 'jpg') {
          ElMessage.info('开始上传JPG图像...')
          const jpgUploadUrl = 'http://localhost:8080/upload-jpg'
          // 上传第一张图像作为示例，实际可以循环上传所有图像
          scanonweb.value.uploadJpgImageByIndex(jpgUploadUrl, id, description, 0)
        }

      } catch (error) {
        loading.upload = false
        console.error('上传失败:', error)
        ElMessage.error(`上传失败: ${error.message || '未知错误'}`)
      }
    }

    // 预览图像
    const previewImage = (index) => {
      if (index >= 0 && index < images.value.length) {
        previewDialog.currentIndex = index
        previewDialog.currentImage = images.value[index]
        previewDialog.visible = true
      }
    }

    // 上一张图像
    const prevImage = () => {
      if (previewDialog.currentIndex > 0) {
        previewDialog.currentIndex--
        previewDialog.currentImage = images.value[previewDialog.currentIndex]
      }
    }

    // 下一张图像
    const nextImage = () => {
      if (previewDialog.currentIndex < images.value.length - 1) {
        previewDialog.currentIndex++
        previewDialog.currentImage = images.value[previewDialog.currentIndex]
      }
    }

    // 下载图像
    const downloadImage = (index) => {
      if (index >= 0 && index < images.value.length) {
        const image = images.value[index]
        const filename = `scan_image_${index + 1}.jpg`
        utils.downloadBase64Image(image.base64, filename)
        ElMessage.success(`图像 ${index + 1} 下载成功`)
      }
    }

    // 下载当前预览图像
    const downloadCurrentImage = () => {
      downloadImage(previewDialog.currentIndex)
    }

    // 删除图像
    const deleteImage = async (index) => {
      try {
        await ElMessageBox.confirm(`确定要删除图像 ${index + 1} 吗？`, '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        images.value.splice(index, 1)
        // 重新设置索引
        images.value.forEach((img, idx) => {
          img.index = idx
        })

        ElMessage.success('图像删除成功')
      } catch {
        // 用户取消操作
      }
    }

    // 本地保存
    const saveAs = () => {
      if (!scanonweb.value) {
        ElMessage.error('扫描服务未初始化')
        return
      }

      if (images.value.length === 0) {
        ElMessage.warning('没有可保存的图像')
        return
      }

      try {
        const filename = `d:/scan_${Date.now()}.pdf`
        scanonweb.value.saveAllImageToLocal(filename)
        ElMessage.success('文件保存成功')
      } catch (error) {
        ElMessage.error('保存失败')
      }
    }

    // 生命周期钩子
    onMounted(() => {
      initScanService()
      // 自动加载设备列表
      setTimeout(() => {
        loadDevices()
      }, 1000)
    })

    onUnmounted(() => {
      // 清理资源
      if (scanonweb.value && scanonweb.value.h5socket) {
        scanonweb.value.h5socket.close()
      }
    })

    // 返回模板需要的数据和方法
    return {
      // 响应式数据
      isConnected,
      devices,
      selectedDevice,
      images,
      viewMode,
      config,
      loading,
      uploadDialog,
      previewDialog,

      // 方法
      loadDevices,
      onDeviceChange,
      startScan,
      getAllImage,
      clearAll,
      showUploadDialog,
      confirmUpload,
      previewImage,
      prevImage,
      nextImage,
      downloadImage,
      downloadCurrentImage,
      deleteImage,
      saveAs
    }
  }
}
</script>

<style scoped>
.scan-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.header-card {
  margin-bottom: 20px;
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.page-title {
  margin: 0;
  font-size: 2.5em;
  font-weight: 300;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.page-subtitle {
  margin: 10px 0 0 0;
  font-size: 1.1em;
  opacity: 0.9;
}

.connection-alert {
  margin-bottom: 20px;
}

.config-card,
.action-card,
.result-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: none;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  margin-left: auto;
}

.dpi-separator {
  text-align: center;
  line-height: 32px;
  font-weight: bold;
  color: #909399;
}

.advanced-options-compact {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.options-row {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  padding: 10px 0;
}

.image-item {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.image-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.image-wrapper {
  position: relative;
  overflow: hidden;
}

.scan-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.2s;
}

.scan-image:hover {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s;
}

.image-wrapper:hover .image-overlay {
  opacity: 1;
}

.image-info {
  padding: 15px;
  text-align: center;
}

.image-index {
  font-weight: 600;
  color: #303133;
}

.table-thumbnail {
  width: 80px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.2s;
}

.table-thumbnail:hover {
  transform: scale(1.1);
}

.empty-state {
  margin: 40px 0;
  padding: 40px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-container {
  text-align: center;
  padding: 20px;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.preview-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.preview-info {
  font-weight: 600;
  color: #606266;
}

.preview-actions {
  display: flex;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .scan-container {
    padding: 10px;
  }

  .page-title {
    font-size: 2em;
  }

  .image-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
  }

  .preview-footer {
    flex-direction: column;
    gap: 15px;
  }

  .preview-actions {
    flex-wrap: wrap;
    justify-content: center;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.config-card,
.action-card,
.result-card {
  animation: fadeIn 0.5s ease-out;
}

/* Element Plus 组件样式覆盖 */
.el-card__header {
  background-color: #fafafa;
  border-bottom: 1px solid #ebeef5;
}

.el-button {
  transition: all 0.2s;
}

.el-button:hover {
  transform: translateY(-1px);
}

.el-form-item__label {
  font-weight: 600;
  color: #606266;
}

.el-alert {
  border-radius: 8px;
}
</style>
