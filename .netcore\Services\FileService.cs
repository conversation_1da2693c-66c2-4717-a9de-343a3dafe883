using ScanOnWeb.Models;
using System.Text;

namespace ScanOnWeb.Services;

/// <summary>
/// 文件服务实现
/// </summary>
public class FileService : IFileService
{
    private const string UploadDirectory = "uploads";
    private readonly ILogger<FileService> _logger;

    public FileService(ILogger<FileService> logger)
    {
        _logger = logger;
        EnsureUploadDirectoryExists();
    }

    /// <summary>
    /// 确保上传目录存在
    /// </summary>
    private void EnsureUploadDirectoryExists()
    {
        var uploadPath = Path.Combine(Directory.GetCurrentDirectory(), UploadDirectory);
        if (!Directory.Exists(uploadPath))
        {
            Directory.CreateDirectory(uploadPath);
            _logger.LogInformation("创建上传目录: {UploadPath}", uploadPath);
        }
    }

    /// <summary>
    /// 生成文件名
    /// </summary>
    private static string GenerateFilename(string id, string format)
    {
        if (string.IsNullOrWhiteSpace(id))
        {
            id = Guid.NewGuid().ToString("N");
        }

        var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        return $"{id}_{timestamp}.{format}";
    }

    /// <summary>
    /// 保存上传的文件
    /// </summary>
    public async Task<string> SaveUploadedFileAsync(IFormFile file, string id, string format)
    {
        var filename = GenerateFilename(id, format);
        var filePath = GetFilePath(filename);

        using var stream = new FileStream(filePath, FileMode.Create);
        await file.CopyToAsync(stream);

        _logger.LogInformation("文件保存成功: {Filename} (原文件名: {OriginalFilename}, 大小: {Size} bytes)",
            filename, file.FileName, file.Length);

        return filename;
    }

    /// <summary>
    /// 保存Base64编码的文件
    /// </summary>
    public async Task<string> SaveBase64FileAsync(string base64Data, string id, string format)
    {
        try
        {
            var data = Convert.FromBase64String(base64Data);
            var filename = GenerateFilename(id, format);
            var filePath = GetFilePath(filename);

            await File.WriteAllBytesAsync(filePath, data);

            _logger.LogInformation("Base64文件保存成功: {Filename} (大小: {Size} bytes)",
                filename, data.Length);

            return filename;
        }
        catch (FormatException ex)
        {
            _logger.LogError(ex, "Base64数据格式错误");
            throw new ArgumentException("Base64数据格式错误", nameof(base64Data));
        }
    }

    /// <summary>
    /// 获取文件列表
    /// </summary>
    public async Task<List<Models.FileInfo>> GetFileListAsync()
    {
        var files = new List<Models.FileInfo>();
        var uploadPath = Path.Combine(Directory.GetCurrentDirectory(), UploadDirectory);

        if (!Directory.Exists(uploadPath))
        {
            return files;
        }

        var fileInfos = new DirectoryInfo(uploadPath).GetFiles();

        foreach (var fileInfo in fileInfos)
        {
            var file = new Models.FileInfo
            {
                Name = fileInfo.Name,
                Size = fileInfo.Length,
                ModTime = fileInfo.LastWriteTime.ToString("yyyy-MM-dd HH:mm:ss"),
                Url = $"/uploads/{fileInfo.Name}",
                Type = Path.GetExtension(fileInfo.Name).TrimStart('.').ToLower(),
                FormattedSize = FormatFileSize(fileInfo.Length)
            };

            files.Add(file);
        }

        // 按修改时间排序（最新的在前）
        files.Sort((a, b) => string.Compare(b.ModTime, a.ModTime, StringComparison.Ordinal));

        return await Task.FromResult(files);
    }

    /// <summary>
    /// 删除文件
    /// </summary>
    public async Task<bool> DeleteFileAsync(string filename)
    {
        var filePath = GetFilePath(filename);

        if (File.Exists(filePath))
        {
            File.Delete(filePath);
            _logger.LogInformation("文件删除成功: {Filename}", filename);
            return await Task.FromResult(true);
        }

        _logger.LogWarning("文件不存在: {Filename}", filename);
        return await Task.FromResult(false);
    }

    /// <summary>
    /// 获取文件路径
    /// </summary>
    public string GetFilePath(string filename)
    {
        return Path.Combine(Directory.GetCurrentDirectory(), UploadDirectory, filename);
    }

    /// <summary>
    /// 检查文件是否存在
    /// </summary>
    public bool FileExists(string filename)
    {
        return File.Exists(GetFilePath(filename));
    }

    /// <summary>
    /// 格式化文件大小
    /// </summary>
    private static string FormatFileSize(long bytes)
    {
        string[] units = { "Bytes", "KB", "MB", "GB", "TB" };
        
        if (bytes == 0)
            return "0 Bytes";

        double size = bytes;
        int unitIndex = 0;

        while (size >= 1024 && unitIndex < units.Length - 1)
        {
            size /= 1024;
            unitIndex++;
        }

        return $"{size:F2} {units[unitIndex]}";
    }
}
