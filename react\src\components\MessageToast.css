/* 消息提示容器 */
.message-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 3000;
  display: flex;
  flex-direction: column;
  gap: 10px;
  pointer-events: none;
}

/* 消息提示样式 */
.message-toast {
  padding: 12px 20px;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  gap: 10px;
  min-width: 300px;
  max-width: 400px;
  animation: slideInRight 0.3s ease-out;
  backdrop-filter: blur(10px);
  pointer-events: auto;
}

.message-toast i {
  font-size: 1.1em;
  flex-shrink: 0;
}

.message-text {
  flex: 1;
  word-wrap: break-word;
}

/* 消息类型样式 */
.message-success {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.message-error {
  background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
}

.message-warning {
  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.message-info {
  background: linear-gradient(135deg, #17a2b8 0%, #007bff 100%);
}

/* 动画效果 */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .message-container {
    top: 10px;
    right: 10px;
    left: 10px;
  }
  
  .message-toast {
    min-width: auto;
    max-width: none;
    padding: 10px 15px;
    font-size: 14px;
  }
  
  .message-toast i {
    font-size: 1em;
  }
}
