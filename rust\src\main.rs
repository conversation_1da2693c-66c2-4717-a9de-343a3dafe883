use axum::{
    routing::{get, post},
    Router,
};
use std::net::SocketAddr;
use tower_http::cors::{Any, CorsLayer};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

mod handlers;
mod models;
mod utils;

use handlers::{
    file_handler,
    health_handler,
    upload_handler,
};

#[tokio::main]
async fn main() {
    // 初始化日志
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "scanonweb_rust=debug,tower_http=debug".into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    // 创建上传目录
    utils::file_utils::init_upload_directory().await;

    // 配置CORS
    let cors = CorsLayer::new()
        .allow_origin(Any)
        .allow_methods(Any)
        .allow_headers(Any);

    // 构建路由
    let app = Router::new()
        // 健康检查
        .route("/health", get(health_handler::health_check))
        
        // PDF上传
        .route("/upload", post(upload_handler::upload_pdf))
        
        // TIFF上传
        .route("/upload-tiff", post(upload_handler::upload_tiff))
        
        // JPG上传
        .route("/upload-jpg", post(upload_handler::upload_jpg))
        
        // 文件下载
        .route("/uploads/*path", get(file_handler::download_file))
        
        // 文件列表
        .route("/files", get(file_handler::list_files))
        
        // 删除文件
        .route("/files/*path", axum::routing::delete(file_handler::delete_file))
        
        // 添加CORS中间件
        .layer(cors);

    // 启动服务器
    let addr = SocketAddr::from(([0, 0, 0, 0], 8080));
    
    println!("================================");
    println!("ScanOnWeb Rust Server");
    println!("================================");
    println!("🚀 服务器启动成功!");
    println!("📍 访问地址: http://localhost:8080");
    println!("💊 健康检查: http://localhost:8080/health");
    println!("🛑 按 Ctrl+C 停止服务器");
    println!("================================");

    let listener = tokio::net::TcpListener::bind(addr).await.unwrap();
    axum::serve(listener, app).await.unwrap();
}
