#!/bin/bash

echo "================================"
echo "ScanOnWeb .NET Core Server"
echo "================================"

# Check .NET environment
if ! command -v dotnet &> /dev/null; then
    echo "Error: .NET not found, please install .NET 8.0 or higher"
    echo "Visit: https://dotnet.microsoft.com/download"
    exit 1
fi

echo "Building project..."
dotnet build

if [ $? -ne 0 ]; then
    echo "Build failed, please check error messages"
    exit 1
fi

echo "Starting .NET Core server..."
echo "Access URL: http://localhost:8080"
echo "API Documentation: http://localhost:8080/swagger"
echo "Press Ctrl+C to stop server"

dotnet run
