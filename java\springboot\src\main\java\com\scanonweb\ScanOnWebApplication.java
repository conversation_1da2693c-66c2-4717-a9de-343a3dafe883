package com.scanonweb;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

/**
 * ScanOnWeb 文档扫描系统主应用类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableConfigurationProperties
public class ScanOnWebApplication {

    public static void main(String[] args) {
        SpringApplication.run(ScanOnWebApplication.class, args);
        System.out.println("=================================");
        System.out.println("ScanOnWeb Server 启动成功!");
        System.out.println("访问地址: http://localhost:8080");
        System.out.println("健康检查: http://localhost:8080/health");
        System.out.println("=================================");
    }
}
