use axum::{
    extract::Path,
    http::{HeaderMap, StatusCode},
    response::{<PERSON><PERSON>, Response},
};
use tokio::fs;
use tokio_util::io::ReaderStream;

use crate::models::{FileListResponse, DeleteResponse};
use crate::utils::file_utils;

/// 文件下载处理器
pub async fn download_file(
    Path(filename): Path<String>,
) -> Result<Response, StatusCode> {
    let file_path = file_utils::get_upload_path(&filename);
    
    // 检查文件是否存在
    if !file_path.exists() {
        return Err(StatusCode::NOT_FOUND);
    }
    
    // 打开文件
    let file = match fs::File::open(&file_path).await {
        Ok(file) => file,
        Err(_) => return Err(StatusCode::INTERNAL_SERVER_ERROR),
    };
    
    // 获取文件元数据
    let metadata = match file.metadata().await {
        Ok(metadata) => metadata,
        Err(_) => return Err(StatusCode::INTERNAL_SERVER_ERROR),
    };
    
    // 确定Content-Type
    let content_type = match file_utils::get_file_extension(&filename) {
        Some("pdf") => "application/pdf",
        Some("tiff") | Some("tif") => "image/tiff",
        Some("jpg") | Some("jpeg") => "image/jpeg",
        Some("png") => "image/png",
        _ => "application/octet-stream",
    };
    
    // 创建文件流
    let stream = ReaderStream::new(file);
    let body = axum::body::Body::from_stream(stream);
    
    // 构建响应
    let mut response = Response::builder()
        .status(StatusCode::OK)
        .header("Content-Type", content_type)
        .header("Content-Length", metadata.len().to_string())
        .header(
            "Content-Disposition",
            format!("attachment; filename=\"{}\"", filename),
        );
    
    match response.body(body) {
        Ok(response) => Ok(response),
        Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
    }
}

/// 文件列表处理器
pub async fn list_files() -> Result<Json<FileListResponse>, (StatusCode, Json<FileListResponse>)> {
    match file_utils::list_upload_files().await {
        Ok(files) => {
            let count = files.len();
            let response = FileListResponse {
                success: true,
                files,
                count,
            };
            Ok(Json(response))
        }
        Err(e) => {
            tracing::error!("获取文件列表失败: {}", e);
            let response = FileListResponse {
                success: false,
                files: vec![],
                count: 0,
            };
            Err((StatusCode::INTERNAL_SERVER_ERROR, Json(response)))
        }
    }
}

/// 删除文件处理器
pub async fn delete_file(
    Path(filename): Path<String>,
) -> Result<Json<DeleteResponse>, (StatusCode, Json<DeleteResponse>)> {
    match file_utils::delete_file(&filename).await {
        Ok(deleted) => {
            if deleted {
                let response = DeleteResponse {
                    success: true,
                    message: "文件删除成功".to_string(),
                };
                Ok(Json(response))
            } else {
                let response = DeleteResponse {
                    success: false,
                    message: "文件不存在".to_string(),
                };
                Err((StatusCode::NOT_FOUND, Json(response)))
            }
        }
        Err(e) => {
            tracing::error!("删除文件失败: {}", e);
            let response = DeleteResponse {
                success: false,
                message: format!("删除文件失败: {}", e),
            };
            Err((StatusCode::INTERNAL_SERVER_ERROR, Json(response)))
        }
    }
}
