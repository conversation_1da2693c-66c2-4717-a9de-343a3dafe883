using ScanOnWeb.Services;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

// 配置Serilog日志
Log.Logger = new LoggerConfiguration()
    .WriteTo.Console()
    .WriteTo.File("logs/scanonweb-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();

builder.Host.UseSerilog();

// 添加服务
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// 注册自定义服务
builder.Services.AddScoped<IFileService, FileService>();

// 配置CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// 配置文件上传大小限制
builder.Services.Configure<IISServerOptions>(options =>
{
    options.MaxRequestBodySize = 100 * 1024 * 1024; // 100MB
});

builder.WebHost.ConfigureKestrel(options =>
{
    options.Limits.MaxRequestBodySize = 100 * 1024 * 1024; // 100MB
});

var app = builder.Build();

// 配置HTTP请求管道
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseCors();
app.UseRouting();
app.MapControllers();

// 静态文件服务（用于文件下载）
app.UseStaticFiles();

// 创建上传目录
var uploadsPath = Path.Combine(Directory.GetCurrentDirectory(), "uploads");
if (!Directory.Exists(uploadsPath))
{
    Directory.CreateDirectory(uploadsPath);
    Log.Information("创建上传目录: {UploadsPath}", uploadsPath);
}

// 启动信息
Log.Information("================================");
Log.Information("ScanOnWeb .NET Core Server");
Log.Information("================================");
Log.Information("🚀 服务器启动成功!");
Log.Information("📍 访问地址: http://localhost:8080");
Log.Information("💊 健康检查: http://localhost:8080/health");
Log.Information("📚 API文档: http://localhost:8080/swagger");
Log.Information("🛑 按 Ctrl+C 停止服务器");
Log.Information("================================");

app.Run("http://localhost:8080");
