package com.scanonweb.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 上传响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UploadResponse {

    /**
     * 操作是否成功
     */
    private boolean success;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 状态码
     */
    private int code;

    /**
     * 文件ID
     */
    @JsonProperty("fileId")
    private String fileId;

    /**
     * 文件访问路径
     */
    private String path;

    /**
     * 详细数据
     */
    private Map<String, Object> data;

    /**
     * 创建成功响应
     */
    public static UploadResponse success(String message, String fileId, String path, Map<String, Object> data) {
        return UploadResponse.builder()
                .success(true)
                .message(message)
                .code(200)
                .fileId(fileId)
                .path(path)
                .data(data)
                .build();
    }

    /**
     * 创建失败响应
     */
    public static UploadResponse error(String message, int code) {
        return UploadResponse.builder()
                .success(false)
                .message(message)
                .code(code)
                .build();
    }

    /**
     * 创建失败响应（默认400状态码）
     */
    public static UploadResponse error(String message) {
        return error(message, 400);
    }
}
