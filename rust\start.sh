#!/bin/bash

echo "================================"
echo "ScanOnWeb Rust Server"
echo "================================"

# Check Rust environment
if ! command -v rustc &> /dev/null; then
    echo "Error: Rust not found, please install Rust"
    echo "Visit: https://rustup.rs/"
    exit 1
fi

# Check Cargo
if ! command -v cargo &> /dev/null; then
    echo "Error: Cargo not found, please install Rust with Cargo"
    exit 1
fi

echo "Building project..."
cargo build --release

if [ $? -ne 0 ]; then
    echo "Build failed, please check error messages"
    exit 1
fi

echo "Starting Rust server..."
echo "Access URL: http://localhost:8080"
echo "Press Ctrl+C to stop server"

cargo run --release
