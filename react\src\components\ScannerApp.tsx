import React, { useState, useEffect, useRef } from 'react';
import './ScannerApp.css';
import ControlPanel from './ControlPanel';
import ImageGallery from './ImageGallery';
import UploadModal from './UploadModal';
import PreviewModal from './PreviewModal';
import MessageToast from './MessageToast';

// 声明全局ScanOnWeb类型
declare global {
  interface Window {
    ScanOnWeb: any;
  }
}

export interface ScanImage {
  src: string;
  index: number;
  base64: string;
}

export interface ScanConfig {
  dpi_x: number;
  dpi_y: number;
  colorMode: string;
  showDialog: boolean;
  autoFeedEnable: boolean;
  autoFeed: boolean;
  dupxMode: boolean;
  autoDeskew: boolean;
  autoBorderDetection: boolean;
}

export interface Message {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  text: string;
}

const ScannerApp: React.FC = () => {
  // 状态管理
  const [isConnected, setIsConnected] = useState(false);
  const [devices, setDevices] = useState<string[]>([]);
  const [selectedDevice, setSelectedDevice] = useState(-1);
  const [images, setImages] = useState<ScanImage[]>([]);
  const [loading, setLoading] = useState({
    devices: false,
    scan: false,
    images: false,
    upload: false
  });
  const [messages, setMessages] = useState<Message[]>([]);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [currentPreviewIndex, setCurrentPreviewIndex] = useState(0);

  // 扫描配置
  const [config, setConfig] = useState<ScanConfig>({
    dpi_x: 300,
    dpi_y: 300,
    colorMode: 'RGB',
    showDialog: false,
    autoFeedEnable: true,
    autoFeed: false,
    dupxMode: false,
    autoDeskew: false,
    autoBorderDetection: false
  });

  const scanonwebRef = useRef<any>(null);
  const loadTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 消息提示函数
  const showMessage = (type: Message['type'], text: string) => {
    const id = Date.now().toString();
    const message: Message = { id, type, text };
    setMessages(prev => [...prev, message]);
    
    // 3秒后自动移除消息
    setTimeout(() => {
      setMessages(prev => prev.filter(m => m.id !== id));
    }, 3000);
  };

  // 初始化扫描服务
  const initScanService = () => {
    console.log('开始初始化扫描服务...');
    try {
      // 动态加载scanonweb
      import('scanonweb').then((ScanOnWebModule) => {
        console.log('scanonweb模块加载成功:', ScanOnWebModule);
        const ScanOnWeb = ScanOnWebModule.default || ScanOnWebModule;
        console.log('ScanOnWeb构造函数:', ScanOnWeb);
        scanonwebRef.current = new ScanOnWeb();
        console.log('ScanOnWeb实例创建成功:', scanonwebRef.current);

        // 设置事件回调
        scanonwebRef.current.onGetDevicesListEvent = (msg: any) => {
          console.log('设备列表回调:', msg);
          const deviceList = msg.devices || [];

          // 清除加载超时
          if (loadTimeoutRef.current) {
            clearTimeout(loadTimeoutRef.current);
            loadTimeoutRef.current = null;
          }

          // 更新设备列表
          setDevices(deviceList);
          setLoading(prev => ({ ...prev, devices: false }));

          // 设置连接状态为已连接（能收到设备列表说明服务已连接）
          setIsConnected(true);
          console.log('WebSocket连接已建立，设备列表加载完成');

          // 自动选择第一个设备
          if (deviceList.length > 0) {
            const deviceIndex = msg.currentIndex >= 0 ? msg.currentIndex : 0;
            console.log('自动选择设备索引:', deviceIndex, '设备名称:', deviceList[deviceIndex]);

            setSelectedDevice(deviceIndex);

            // 立即调用设备选择（此时WebSocket已连接）
            if (scanonwebRef.current && deviceIndex >= 0) {
              scanonwebRef.current.selectScanDevice(deviceIndex);
              showMessage('info', `已选择设备: ${deviceList[deviceIndex]}`);
            }

            showMessage('success', `发现 ${deviceList.length} 个扫描设备，已自动选择第一个设备`);
          } else {
            setSelectedDevice(-1);
            showMessage('warning', '未发现可用的扫描设备');
          }
        };

        scanonwebRef.current.onScanFinishedEvent = (msg: any) => {
          setLoading(prev => ({ ...prev, scan: false }));
          showMessage('success', `扫描完成！共扫描 ${msg.imageAfterCount} 张图像`);
          getAllImage();
        };

        scanonwebRef.current.onGetAllImageEvent = (msg: any) => {
          setLoading(prev => ({ ...prev, images: false }));
          if (msg.images && msg.images.length > 0) {
            const imageList: ScanImage[] = msg.images.map((image: string, index: number) => ({
              src: `data:image/jpg;base64,${image}`,
              index: index,
              base64: image
            }));
            setImages(imageList);
            showMessage('success', `获取到 ${imageList.length} 张图像`);
          } else {
            showMessage('info', '暂无扫描图像');
          }
        };

        scanonwebRef.current.onUploadEvent = () => {
          showMessage('info', '用户点击了上传按钮');
          setShowUploadModal(true);
        };

        // 设置上传回调
        setupUploadCallbacks();

        console.log('scanonweb初始化完成，等待WebSocket连接...');

      }).catch((error) => {
        console.error('加载scanonweb失败:', error);
        setIsConnected(false);
        showMessage('error', '加载扫描服务失败');
      });

    } catch (error) {
      console.error('初始化扫描服务失败:', error);
      setIsConnected(false);
      showMessage('error', '初始化扫描服务失败');
    }
  };

  // 设置上传回调
  const setupUploadCallbacks = () => {
    if (!scanonwebRef.current) return;

    // PDF上传回调
    scanonwebRef.current.onUploadAllImageAsPdfToUrlEvent = (msg: any) => {
      console.log('PDF上传回调:', msg);
      setLoading(prev => ({ ...prev, upload: false }));

      try {
        let uploadResult = null;
        if (msg.uploadResult) {
          uploadResult = typeof msg.uploadResult === 'string'
            ? JSON.parse(msg.uploadResult)
            : msg.uploadResult;
        }

        if (uploadResult && uploadResult.success) {
          showMessage('success', `PDF文档上传成功！共 ${msg.imageCount || 0} 张图像`);
          setShowUploadModal(false);
        } else {
          const errorMsg = uploadResult?.message || msg.message || '未知错误';
          showMessage('error', `PDF上传失败: ${errorMsg}`);
        }
      } catch (error) {
        console.error('解析上传结果失败:', error, msg);
        showMessage('error', 'PDF上传失败: 响应解析错误');
      }
    };

    // TIFF上传回调
    scanonwebRef.current.onUploadAllImageAsTiffToUrlEvent = (msg: any) => {
      console.log('TIFF上传回调:', msg);
      setLoading(prev => ({ ...prev, upload: false }));

      try {
        let uploadResult = null;
        if (msg.uploadResult) {
          uploadResult = typeof msg.uploadResult === 'string'
            ? JSON.parse(msg.uploadResult)
            : msg.uploadResult;
        }

        if (uploadResult && uploadResult.success) {
          showMessage('success', `TIFF文档上传成功！共 ${msg.imageCount || 0} 张图像`);
          setShowUploadModal(false);
        } else {
          const errorMsg = uploadResult?.message || msg.message || '未知错误';
          showMessage('error', `TIFF上传失败: ${errorMsg}`);
        }
      } catch (error) {
        console.error('解析上传结果失败:', error, msg);
        showMessage('error', 'TIFF上传失败: 响应解析错误');
      }
    };

    // JPG上传回调
    scanonwebRef.current.onUploadJpgImageByIndexEvent = (msg: any) => {
      console.log('JPG上传回调:', msg);
      setLoading(prev => ({ ...prev, upload: false }));

      try {
        let uploadResult = null;
        if (msg.uploadResult) {
          uploadResult = typeof msg.uploadResult === 'string'
            ? JSON.parse(msg.uploadResult)
            : msg.uploadResult;
        }

        if (uploadResult && uploadResult.success) {
          showMessage('success', 'JPG图像上传成功！');
          setShowUploadModal(false);
        } else {
          const errorMsg = uploadResult?.message || msg.message || '未知错误';
          showMessage('error', `JPG上传失败: ${errorMsg}`);
        }
      } catch (error) {
        console.error('解析上传结果失败:', error, msg);
        showMessage('error', 'JPG上传失败: 响应解析错误');
      }
    };
  };

  // 加载设备列表（手动刷新）
  const loadDevices = () => {
    console.log('手动刷新设备列表...');
    if (!scanonwebRef.current) {
      console.log('扫描服务未初始化');
      showMessage('error', '扫描服务未初始化');
      return;
    }

    // 检查WebSocket连接状态
    if (!scanonwebRef.current.h5socket || scanonwebRef.current.h5socket.readyState !== 1) {
      showMessage('warning', 'WebSocket未连接，请等待连接建立');
      return;
    }

    console.log('调用loadDevices方法...');
    setLoading(prev => ({ ...prev, devices: true }));

    try {
      scanonwebRef.current.loadDevices();
      console.log('loadDevices方法调用成功');
    } catch (error) {
      console.log('loadDevices方法调用失败:', error);
      setLoading(prev => ({ ...prev, devices: false }));
      showMessage('error', '获取设备列表失败');
    }
  };

  // 设备选择变化
  const onDeviceChange = (deviceIndex: number) => {
    console.log('设备选择变化:', deviceIndex, '设备列表长度:', devices.length, '设备列表:', devices);
    if (scanonwebRef.current && deviceIndex >= 0) {
      // 检查WebSocket连接状态
      if (!scanonwebRef.current.h5socket || scanonwebRef.current.h5socket.readyState !== 1) {
        showMessage('warning', 'WebSocket未连接，无法选择设备');
        return;
      }

      // 确保设备索引有效
      if (deviceIndex < devices.length && devices[deviceIndex]) {
        scanonwebRef.current.selectScanDevice(deviceIndex);
        showMessage('info', `已选择设备: ${devices[deviceIndex]}`);
      } else {
        console.log('设备索引超出范围或设备名称为空');
      }
    } else {
      console.log('设备选择失败，条件不满足');
    }
  };

  // 开始扫描
  const startScan = () => {
    if (!scanonwebRef.current) {
      showMessage('error', '扫描服务未初始化');
      return;
    }

    // 检查WebSocket连接状态
    if (!scanonwebRef.current.h5socket || scanonwebRef.current.h5socket.readyState !== 1) {
      showMessage('warning', 'WebSocket未连接，无法开始扫描');
      return;
    }

    if (selectedDevice === -1) {
      showMessage('warning', '请先选择扫描设备');
      return;
    }

    setLoading(prev => ({ ...prev, scan: true }));
    try {
      // 更新扫描配置
      scanonwebRef.current.scaner_work_config = {
        ...scanonwebRef.current.scaner_work_config,
        ...config,
        deviceIndex: selectedDevice
      };

      scanonwebRef.current.startScan();
      showMessage('info', '开始扫描...');
    } catch (error) {
      setLoading(prev => ({ ...prev, scan: false }));
      showMessage('error', '启动扫描失败');
    }
  };

  // 获取所有图像
  const getAllImage = () => {
    if (!scanonwebRef.current) {
      showMessage('error', '扫描服务未初始化');
      return;
    }

    // 检查WebSocket连接状态
    if (!scanonwebRef.current.h5socket || scanonwebRef.current.h5socket.readyState !== 1) {
      showMessage('warning', 'WebSocket未连接，无法获取图像');
      return;
    }

    setLoading(prev => ({ ...prev, images: true }));
    try {
      scanonwebRef.current.getAllImage();
    } catch (error) {
      setLoading(prev => ({ ...prev, images: false }));
      showMessage('error', '获取图像失败');
    }
  };

  // 清空所有图像
  const clearAll = () => {
    if (window.confirm('确定要清空所有扫描结果吗？')) {
      if (scanonwebRef.current) {
        // 检查WebSocket连接状态
        if (!scanonwebRef.current.h5socket || scanonwebRef.current.h5socket.readyState !== 1) {
          showMessage('warning', 'WebSocket未连接，无法清空结果');
          return;
        }

        scanonwebRef.current.clearAll();
        setImages([]);
        showMessage('success', '已清空所有扫描结果');
      }
    }
  };

  // 上传确认
  const confirmUpload = (uploadData: { id: string; description: string; format: string }) => {
    const { id, description, format } = uploadData;

    if (!scanonwebRef.current) {
      showMessage('error', '扫描服务未初始化');
      return;
    }

    if (images.length === 0) {
      showMessage('warning', '没有可上传的图像');
      return;
    }

    try {
      setLoading(prev => ({ ...prev, upload: true }));

      // 调用控件内置的上传方法
      if (format === 'pdf') {
        const uploadUrl = 'http://localhost:8080/upload';
        showMessage('info', '开始上传PDF文档...');
        scanonwebRef.current.uploadAllImageAsPdfToUrl(uploadUrl, id, description);
      } else if (format === 'tiff') {
        const tiffUploadUrl = 'http://localhost:8080/upload-tiff';
        showMessage('info', '开始上传TIFF文档...');
        scanonwebRef.current.uploadAllImageAsTiffToUrl(tiffUploadUrl, id, description);
      } else if (format === 'jpg') {
        const jpgUploadUrl = 'http://localhost:8080/upload-jpg';
        showMessage('info', '开始上传JPG图像...');
        scanonwebRef.current.uploadJpgImageByIndex(jpgUploadUrl, id, description, 0);
      }

    } catch (error) {
      setLoading(prev => ({ ...prev, upload: false }));
      console.error('上传失败:', error);
      showMessage('error', `上传失败: ${(error as Error).message || '未知错误'}`);
    }
  };

  // 预览图像
  const previewImage = (index: number) => {
    setCurrentPreviewIndex(index);
    setShowPreviewModal(true);
  };

  // 下载图像
  const downloadImage = (index: number) => {
    if (index >= 0 && index < images.length) {
      const image = images[index];
      const filename = `scan_image_${index + 1}.jpg`;
      
      // 下载base64图像
      const byteCharacters = atob(image.base64);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: 'image/jpeg' });
      
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      showMessage('success', `图像 ${index + 1} 下载成功`);
    }
  };

  // 删除图像
  const deleteImage = (index: number) => {
    if (window.confirm(`确定要删除图像 ${index + 1} 吗？`)) {
      const newImages = images.filter((_, i) => i !== index);
      // 重新设置索引
      const updatedImages = newImages.map((img, idx) => ({
        ...img,
        index: idx
      }));
      setImages(updatedImages);
      showMessage('success', '图像删除成功');
    }
  };

  // 本地保存
  const saveAs = () => {
    if (!scanonwebRef.current) {
      showMessage('error', '扫描服务未初始化');
      return;
    }

    if (images.length === 0) {
      showMessage('warning', '没有可保存的图像');
      return;
    }

    try {
      const filename = `d:/scan_${Date.now()}.pdf`;
      scanonwebRef.current.saveAllImageToLocal(filename);
      showMessage('success', '文件保存成功');
    } catch (error) {
      showMessage('error', '保存失败');
    }
  };

  // 组件挂载时初始化
  useEffect(() => {
    initScanService();

    // 注意：不需要手动调用loadDevices()
    // scanonweb会在WebSocket连接成功后自动调用loadDevices()
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className="scanner-app">
      {/* 页面标题 */}
      <div className="app-header">
        <h1 className="app-title">
          <i className="fas fa-camera"></i>
          文档扫描系统 - React版本
        </h1>
        <p className="app-subtitle">左右布局的专业扫描解决方案</p>
      </div>

      {/* 连接状态指示器 */}
      {!isConnected && (
        <div className="connection-alert">
          <div className="alert alert-warning">
            <i className="fas fa-exclamation-triangle"></i>
            <div className="alert-content">
              <strong>扫描服务未连接</strong>
              <p>请确保扫描服务程序已启动并正在运行</p>
            </div>
          </div>
        </div>
      )}

      {/* 主要内容区域 - 左右布局 */}
      <div className="main-content">
        {/* 左侧控制面板 */}
        <div className="left-panel">
          <ControlPanel
            isConnected={isConnected}
            devices={devices}
            selectedDevice={selectedDevice}
            config={config}
            loading={loading}
            images={images}
            onDeviceChange={(index) => {
              setSelectedDevice(index);
              onDeviceChange(index);
            }}
            onConfigChange={setConfig}
            onLoadDevices={loadDevices}
            onStartScan={startScan}
            onGetImages={getAllImage}
            onClearAll={clearAll}
            onUpload={() => setShowUploadModal(true)}
            onSaveAs={saveAs}
          />
        </div>

        {/* 右侧图像展示区域 */}
        <div className="right-panel">
          <ImageGallery
            images={images}
            onPreview={previewImage}
            onDownload={downloadImage}
            onDelete={deleteImage}
          />
        </div>
      </div>

      {/* 上传对话框 */}
      <UploadModal
        visible={showUploadModal}
        loading={loading.upload}
        onConfirm={confirmUpload}
        onCancel={() => setShowUploadModal(false)}
      />

      {/* 预览对话框 */}
      <PreviewModal
        visible={showPreviewModal}
        images={images}
        currentIndex={currentPreviewIndex}
        onClose={() => setShowPreviewModal(false)}
        onPrev={() => setCurrentPreviewIndex(Math.max(0, currentPreviewIndex - 1))}
        onNext={() => setCurrentPreviewIndex(Math.min(images.length - 1, currentPreviewIndex + 1))}
        onDownload={() => downloadImage(currentPreviewIndex)}
      />

      {/* 消息提示 */}
      <MessageToast messages={messages} />
    </div>
  );
};

export default ScannerApp;
