{"version": 3, "file": "static/css/main.173cf5f9.css", "mappings": "AAAA,EAGE,qBAAsB,CAFtB,QAAS,CACT,SAEF,CAEA,KAEE,kCAAmC,CACnC,iCAAkC,CAClC,wBAAyB,CAHzB,uGAAyH,CAIzH,eACF,CAEA,KACE,uEAEF,CAGA,oBAEE,UAAW,CADX,SAEF,CAEA,0BACE,kBAAmB,CACnB,iBACF,CAEA,0BACE,kBAAmB,CACnB,iBACF,CAEA,gCACE,kBACF,CCrCA,KAEE,wBAAyB,CADzB,gBAEF,CCFA,aAEE,kDAA6D,CAD7D,gBAAiB,CAEjB,YACF,CAGA,YAOE,kCAA2B,CAA3B,0BAA2B,CAH3B,oBAAqC,CACrC,kBAAmB,CACnB,+BAAyC,CAJzC,kBAAmB,CACnB,YAAa,CAFb,iBAOF,CAEA,WAME,kBAAmB,CAHnB,aAAc,CAEd,YAAa,CAJb,eAAgB,CAChB,eAAgB,CAMhB,QAAS,CADT,sBAAuB,CAHvB,QAKF,CAEA,aACE,aACF,CAEA,cAEE,aAAc,CADd,eAAgB,CAGhB,eAAgB,CADhB,eAEF,CAGA,kBACE,kBACF,CAEA,OAIE,kBAAmB,CAGnB,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAqC,CAJrC,iBAAkB,CAMlB,+BAAyC,CALzC,YAAa,CAEb,QAAS,CAJT,iBAQF,CAEA,eACE,6BAA8B,CAC9B,aACF,CAEA,sBACE,aAAc,CAEd,eAAgB,CADhB,iBAEF,CAEA,iBACE,QAAS,CACT,UACF,CAGA,cACE,YAAa,CACb,QAAS,CACT,8BACF,CAGA,YACE,cAMF,CAGA,yBALE,kCAA2B,CAA3B,0BAA2B,CAH3B,oBAAqC,CACrC,kBAAmB,CACnB,+BAAyC,CAEzC,eAWF,CAPA,aACE,QAMF,CAGA,0BACE,cACE,qBACF,CAEA,YACE,SACF,CAEA,WAEE,qBAAsB,CADtB,aAAc,CAEd,QACF,CACF,CAEA,yBACE,aACE,YACF,CAEA,YACE,YACF,CAEA,WACE,eACF,CAEA,cACE,aACF,CAEA,cACE,QACF,CACF,CCvIA,eACE,WAAY,CACZ,eAAgB,CAChB,SACF,CAGA,eACE,+BACF,CAEA,0BACE,kBACF,CAGA,gBAKE,kBAAmB,CAHnB,kDAA6D,CAC7D,UAAY,CACZ,YAAa,CAIb,eAAgB,CADhB,eAAgB,CADhB,QAAS,CALT,YAQF,CAEA,kBACE,eACF,CAGA,iBACE,YACF,CAoBA,cAOE,eACF,CAQA,uBACE,wBAAyB,CACzB,aACF,CAGA,YAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,0BACE,QACF,CAEA,eAEE,aAAc,CACd,eAAgB,CAFhB,eAGF,CAGA,eAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,6BAEF,CAEA,eAEE,kBAAmB,CAInB,iBAAkB,CAFlB,cAAe,CAHf,YAAa,CAOb,cAAgB,CALhB,OAAQ,CAER,WAAY,CAEZ,oCAEF,CAEA,qBACE,wBACF,CAEA,oCAGE,oBAAqB,CADrB,WAAY,CADZ,UAGF,CAEA,oBACE,aAAc,CACd,eACF,CAGA,aAEE,aAAS,CADT,YAAa,CACb,QACF,CAGA,KAGE,iBAAkB,CAOlB,sBAAuB,CAKvB,eAAgB,CAdhB,iBAAkB,CAalB,iBAEF,CAEA,0BACE,0BAEF,CAEA,2BACE,uBACF,CAcA,aACE,kDAA6D,CAC7D,UACF,CAEA,UACE,kDAA6D,CAC7D,UACF,CAEA,aACE,kDAA6D,CAC7D,UACF,CAEA,eACE,kDAEF,CAGA,aACE,kBAAmB,CACnB,iBAAkB,CAClB,YACF,CAEA,aAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,iBACF,CAEA,wBACE,eACF,CAEA,cAEE,aAAc,CACd,cAAgB,CAFhB,eAGF,CAEA,cAEE,cAAgB,CADhB,eAEF,CAEA,wBACE,aACF,CAEA,2BACE,aACF,CAGA,yBACE,eACE,yBACF,CAEA,iBACE,YACF,CAEA,gBAEE,aAAc,CADd,YAEF,CAEA,KAEE,cAAe,CADf,iBAEF,CACF,CCvPA,eAEE,YAAa,CACb,qBAAsB,CAFtB,WAGF,CAGA,gBAEE,kDAA6D,CAC7D,UAAY,CAEZ,6BAA8B,CAJ9B,YAMF,CAEA,6BAHE,kBAAmB,CAFnB,YASF,CAJA,aAGE,QACF,CAEA,eAEE,eAAgB,CADhB,eAEF,CAEA,eACE,eACF,CAGA,aAEE,gBAAoC,CACpC,iBAAkB,CAFlB,YAAa,CAGb,eACF,CAEA,YAGE,gBAAuB,CADvB,WAAY,CAEZ,UAAY,CACZ,cAAe,CAEf,cAAe,CANf,gBAAiB,CAKjB,uBAEF,CAEA,kBACE,oBACF,CAEA,mBACE,oBACF,CAGA,iBACE,QAAO,CACP,eAAgB,CAChB,YACF,CAGA,aAGE,kBAAmB,CADnB,YAAa,CADb,WAAY,CAGZ,sBACF,CAEA,eAEE,aAAc,CADd,iBAEF,CAEA,YAGE,aAAc,CAFd,aAAc,CACd,kBAEF,CAEA,kBAEE,aAAc,CADd,kBAEF,CAEA,iBAEE,cAAgB,CADhB,QAEF,CAGA,YAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,yDAEF,CAEA,YACE,eAAiB,CACjB,kBAAmB,CAEnB,+BAAyC,CADzC,eAAgB,CAEhB,uBACF,CAEA,kBAEE,+BAA0C,CAD1C,0BAEF,CAEA,eAEE,eAAgB,CADhB,iBAEF,CAEA,YAIE,cAAe,CAFf,YAAa,CACb,gBAAiB,CAEjB,6BAA+B,CAJ/B,UAKF,CAEA,kBACE,qBACF,CAGA,eAQE,kBAAmB,CAFnB,oBAA8B,CAD9B,QAAS,CAET,YAAa,CAEb,sBAAuB,CANvB,MAAO,CAOP,SAAU,CATV,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CASN,2BACF,CAEA,oCACE,SACF,CAEA,iBACE,YAAa,CACb,QACF,CAEA,YAIE,iBAAkB,CAClB,UAAY,CAMZ,cAAe,CATf,WAAY,CAQZ,sBAAuB,CATvB,UAWF,CAEA,kBACE,oBACF,CAEA,aACE,kBACF,CAMA,YACE,kBACF,CAGA,YAIE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAF9B,YAIF,CAEA,cAEE,aAAc,CADd,eAEF,CAEA,cACE,kBAAmB,CAGnB,iBAAkB,CAFlB,aAAc,CAGd,cAAgB,CAChB,eAAgB,CAHhB,eAIF,CAGA,YACE,eAAiB,CACjB,kBAAmB,CAEnB,+BAAyC,CADzC,eAEF,CAEA,aAGE,aAAS,CAET,kBAAmB,CAGnB,+BAAgC,CADhC,aAAc,CANd,YAAa,CAKb,eAAgB,CAHhB,QAAS,CADT,2CAA4C,CAE5C,iBAKF,CAEA,WACE,8BAA+B,CAC/B,eACF,CAEA,WAGE,aAAS,CAGT,kBAAmB,CADnB,+BAAgC,CAJhC,YAAa,CAEb,QAAS,CADT,2CAA4C,CAE5C,iBAAkB,CAGlB,oCACF,CAEA,iBACE,wBACF,CAEA,sBACE,kBACF,CAEA,WAIE,iBAAkB,CAClB,cAAe,CAHf,WAAY,CACZ,gBAAiB,CAGjB,6BAA+B,CAL/B,UAMF,CAEA,iBACE,oBACF,CAEA,YACE,kBAAmB,CAGnB,iBAAkB,CAFlB,UAAY,CAGZ,cAAgB,CAChB,eAAgB,CAHhB,eAIF,CAEA,gBACE,YAAa,CACb,OACF,CAEA,iBASE,kBAAmB,CANnB,WAAY,CACZ,iBAAkB,CAClB,UAAY,CACZ,cAAe,CAEf,YAAa,CAGb,cAAe,CATf,WAAY,CAQZ,sBAAuB,CAHvB,uBAAyB,CANzB,UAWF,CAEA,uBACE,oBACF,CAGA,yBACE,YAEE,QAAS,CADT,yDAEF,CAEA,iBACE,YACF,CAEA,gBAEE,qBAAsB,CACtB,QAAS,CAFT,YAGF,CAEA,wBAGE,QAAS,CADT,yCAA0C,CAE1C,iBACF,CAEA,WAEE,WAAY,CADZ,UAEF,CAEA,gBACE,qBAAsB,CACtB,OACF,CAEA,iBAGE,cAAe,CADf,WAAY,CADZ,UAGF,CACF,CAEA,yBAKE,oCAHE,yBAOF,CAJA,wBAGE,QACF,CAEA,gDAKE,kBAAmB,CADnB,YAAa,CAEb,6BACF,CAEA,oBACE,aAAc,CACd,eACF,CAEA,iBACE,aAAc,CACd,eACF,CAEA,mBACE,aAAc,CACd,eACF,CAEA,oBACE,aAAc,CACd,eACF,CAEA,gBACE,kBACF,CACF,CCzXA,eAQE,kBAAmB,CAGnB,iCAA0B,CAA1B,yBAA0B,CAL1B,oBAA8B,CAC9B,YAAa,CAFb,WAAY,CAIZ,sBAAuB,CANvB,MAAO,CAFP,cAAe,CACf,KAAM,CAEN,UAAW,CAMX,YAEF,CAEA,iBAQE,mCAAqC,CAPrC,eAAiB,CACjB,kBAAmB,CAKnB,gCAA0C,CAF1C,eAAgB,CADhB,eAAgB,CAEhB,eAAgB,CAHhB,SAMF,CAGA,cAEE,kDAA6D,CAC7D,UAAY,CAEZ,6BAA8B,CAJ9B,YAMF,CAEA,2BAHE,kBAAmB,CAFnB,YAYF,CAPA,aAEE,eAAgB,CAChB,eAAgB,CAGhB,QAAS,CALT,QAMF,CAEA,aACE,eAAgB,CAChB,WAAY,CAKZ,iBAAkB,CAJlB,UAAY,CAEZ,cAAe,CADf,eAAgB,CAEhB,WAAY,CAEZ,oCACF,CAEA,mBACE,gBACF,CAGA,YAEE,eAAgB,CAChB,eAAgB,CAFhB,YAGF,CAEA,YACE,kBACF,CAEA,uBACE,eACF,CAEA,kBAIE,aAAc,CAHd,aAAc,CAId,cAAgB,CAFhB,eAAgB,CADhB,iBAIF,CAEA,cAGE,wBAAyB,CACzB,iBAAkB,CAGlB,mBAAoB,CAFpB,cAAe,CAHf,iBAAkB,CAIlB,uBAAyB,CALzB,UAOF,CAEA,oBAEE,oBAAqB,CACrB,8BAA8C,CAF9C,YAGF,CAEA,sBAEE,eAAgB,CADhB,eAEF,CAGA,aACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,YAEE,sBAAuB,CAOvB,kBAAmB,CAJnB,wBAAyB,CACzB,iBAAkB,CAClB,cAAe,CANf,YAAa,CAEb,QAAS,CACT,YAAa,CAIb,uBAEF,CAEA,kBAEE,kBAAmB,CADnB,oBAEF,CAEA,8BAEE,oBAAqB,CADrB,cAEF,CAEA,mDACE,aAAc,CACd,eACF,CAEA,2CAEE,kBAAmB,CADnB,oBAEF,CAEA,aAEE,kBAAmB,CAGnB,aAAc,CAJd,YAAa,CAKb,QAAO,CAFP,eAAgB,CADhB,OAIF,CAEA,eACE,eACF,CAEA,YAEE,aAAc,CADd,cAAgB,CAEhB,cACF,CAGA,aACE,kBAAmB,CACnB,wBAAyB,CACzB,iBAAkB,CAElB,eAAgB,CADhB,YAEF,CAEA,WAEE,kBAAmB,CAInB,aAAc,CALd,YAAa,CAIb,cAAgB,CAFhB,QAAS,CACT,iBAGF,CAEA,sBACE,eACF,CAEA,aACE,aACF,CAGA,cAME,kBAAmB,CAJnB,4BAA6B,CAC7B,YAAa,CAEb,QAAS,CADT,wBAAyB,CAHzB,YAMF,CAEA,KASE,kBAAmB,CAGnB,eAAgB,CAVhB,WAAY,CACZ,iBAAkB,CAGlB,cAAe,CAEf,YAAa,CAJb,cAAe,CACf,eAAgB,CAKhB,OAAQ,CATR,iBAAkB,CAUlB,oBAAqB,CAJrB,uBAMF,CAEA,0BAEE,+BAA0C,CAD1C,0BAEF,CAEA,cAEE,kBAAmB,CADnB,UAAY,CAEZ,cACF,CAEA,aACE,kDAA6D,CAC7D,UACF,CAEA,eACE,kBAAmB,CACnB,UACF,CAGA,wBACE,GACE,SAAU,CACV,qCACF,CACA,GACE,SAAU,CACV,gCACF,CACF,CAEA,SACE,iCACF,CAEA,gBACE,GACE,sBACF,CACA,GACE,uBACF,CACF,CAGA,yBACE,iBAEE,WAAY,CADZ,SAEF,CAEA,wCAGE,YACF,CAEA,aACE,eACF,CAEA,YACE,YACF,CAEA,cACE,qBACF,CAEA,KAEE,sBAAuB,CADvB,UAEF,CACF,CC/RA,iBAQE,kBAAmB,CAGnB,iCAA0B,CAA1B,yBAA0B,CAL1B,oBAA8B,CAC9B,YAAa,CAFb,WAAY,CAIZ,sBAAuB,CANvB,MAAO,CAFP,cAAe,CACf,KAAM,CAEN,UAAW,CAMX,YAEF,CAEA,mBAWE,qCAAuC,CANvC,eAAiB,CACjB,kBAAmB,CAInB,gCAA0C,CAF1C,YAAa,CACb,qBAAsB,CAPtB,UAAW,CAEX,gBAAiB,CADjB,gBAAiB,CAIjB,eAAgB,CANhB,SAWF,CAGA,gBAEE,kDAA6D,CAC7D,UAAY,CAIZ,aAAc,CAFd,6BAA8B,CAJ9B,iBAOF,CAEA,+BAJE,kBAAmB,CAFnB,YAYF,CANA,eACE,eAAgB,CAChB,eAAgB,CAGhB,QACF,CAEA,cAEE,gBAAoC,CAEpC,kBAAmB,CAHnB,cAAgB,CAEhB,gBAEF,CAEA,eACE,eAAgB,CAChB,WAAY,CAKZ,iBAAkB,CAJlB,UAAY,CAEZ,cAAe,CADf,eAAgB,CAEhB,WAAY,CAEZ,oCACF,CAEA,qBACE,gBACF,CAGA,iBAME,kBAAmB,CALnB,QAAO,CAMP,eAAgB,CAFhB,iBAGF,CAGA,0BARE,kBAAmB,CADnB,YAAa,CAEb,sBAwBF,CAjBA,SAME,oBAA8B,CAC9B,WAAY,CACZ,iBAAkB,CAClB,UAAY,CAEZ,cAAe,CADf,eAAgB,CALhB,WAAY,CAJZ,iBAAkB,CAClB,OAAQ,CACR,0BAA2B,CAS3B,uBAAyB,CARzB,UAAW,CASX,UAIF,CAEA,8BACE,oBAA8B,CAC9B,qCACF,CAEA,kBAEE,kBAAmB,CADnB,UAEF,CAEA,UACE,SACF,CAEA,UACE,UACF,CAGA,iBAGE,kBAAmB,CADnB,YAAa,CADb,QAAO,CAKP,WAAY,CAFZ,sBAAuB,CACvB,YAEF,CAEA,eAIE,iBAAkB,CAClB,2BAAyC,CAHzC,eAAgB,CADhB,cAAe,CAEf,kBAAmB,CAGnB,6BACF,CAEA,qBACE,qBACF,CAGA,gBAME,kBAAmB,CAJnB,eAAiB,CACjB,4BAA6B,CAC7B,YAAa,CAGb,aAAc,CAFd,6BAA8B,CAJ9B,iBAOF,CAEA,eACE,YAAa,CACb,QACF,CAEA,aAEE,kBAAmB,CAGnB,aAAc,CAJd,YAAa,CAGb,cAAgB,CADhB,OAGF,CAEA,eACE,aACF,CAEA,iBACE,YAAa,CACb,QACF,CAEA,YASE,kBAAmB,CAPnB,WAAY,CACZ,iBAAkB,CAGlB,cAAe,CAEf,YAAa,CAJb,cAAe,CACf,eAAgB,CAKhB,OAAQ,CATR,gBAAiB,CAMjB,uBAIF,CAEA,iCAEE,+BAA0C,CAD1C,0BAEF,CAEA,qBAEE,kBAAmB,CADnB,UAAY,CAEZ,cACF,CAEA,oBAEE,kBAAmB,CACnB,UACF,CAEA,cACE,kBAAmB,CACnB,UACF,CAEA,WACE,kBAAmB,CACnB,UACF,CAGA,eACE,kBAAmB,CACnB,4BAA6B,CAE7B,aAAc,CADd,YAEF,CAEA,gBACE,YAAa,CACb,QAAS,CAGT,sBAAuB,CAFvB,eAAgB,CAChB,aAEF,CAEA,gBAME,sBAA6B,CAH7B,iBAAkB,CADlB,cAAe,CAKf,aAAc,CAHd,eAAgB,CAHhB,iBAAkB,CAIlB,uBAGF,CAEA,sBACE,qBACF,CAEA,uBACE,oBAAqB,CACrB,8BACF,CAEA,iBAIE,aAAc,CAFd,WAAY,CACZ,gBAAiB,CAFjB,UAIF,CAEA,kBAIE,oBAA8B,CAI9B,iBAAkB,CANlB,UAAW,CAGX,UAAY,CACZ,cAAgB,CAChB,eAAgB,CANhB,iBAAkB,CAElB,SAMF,CAGA,0BACE,GACE,SAAU,CACV,mBACF,CACA,GACE,SAAU,CACV,kBACF,CACF,CAGA,yBACE,mBAGE,eAAgB,CADhB,WAAY,CADZ,UAGF,CAEA,gBACE,iBACF,CAMA,wBAHE,aAOF,CAJA,SAEE,WAAY,CADZ,UAGF,CAEA,UACE,SACF,CAEA,UACE,UACF,CAEA,iBACE,YACF,CAEA,gBAEE,qBAAsB,CACtB,QAAS,CAFT,iBAGF,CAEA,iBAGE,cAAe,CADf,sBAAuB,CADvB,UAGF,CAEA,YACE,QAAO,CAEP,sBAAuB,CADvB,cAEF,CAEA,eACE,YACF,CAEA,iBAEE,WAAY,CADZ,UAEF,CACF,CAEA,yBACE,eAGE,kBAAmB,CAFnB,qBAAsB,CACtB,OAEF,CAEA,iBAEE,OAAQ,CADR,6BAEF,CAEA,YACE,cAAe,CACf,gBACF,CACF,CCrWA,mBAKE,YAAa,CACb,qBAAsB,CACtB,QAAS,CACT,mBAAoB,CAPpB,cAAe,CAEf,UAAW,CADX,QAAS,CAET,YAKF,CAGA,eAOE,kBAAmB,CAInB,mCAAqC,CACrC,kCAA2B,CAA3B,0BAA2B,CAV3B,iBAAkB,CAGlB,+BAA0C,CAF1C,UAAY,CAGZ,YAAa,CAFb,eAAgB,CAIhB,QAAS,CAET,eAAgB,CADhB,eAAgB,CARhB,iBAAkB,CAYlB,mBACF,CAEA,iBAEE,aAAc,CADd,eAEF,CAEA,cAEE,oBAAqB,CADrB,QAEF,CAGA,iBACE,kDACF,CAEA,eACE,kDACF,CAEA,iBACE,kDACF,CAEA,cACE,kDACF,CAGA,wBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAGA,yBACE,mBAGE,SAAU,CADV,UAAW,CADX,QAGF,CAEA,eAIE,cAAe,CAFf,cAAe,CADf,cAAe,CAEf,iBAEF,CAEA,iBACE,aACF,CACF", "sources": ["index.css", "App.css", "components/ScannerApp.css", "components/ControlPanel.css", "components/ImageGallery.css", "components/UploadModal.css", "components/PreviewModal.css", "components/MessageToast.css"], "sourcesContent": ["* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nbody {\n  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: #f0f2f5;\n  line-height: 1.6;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n\n/* 全局滚动条样式 */\n::-webkit-scrollbar {\n  width: 8px;\n  height: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n", ".App {\n  min-height: 100vh;\n  background-color: #f0f2f5;\n}\n", "/* 扫描应用主容器 */\n.scanner-app {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  padding: 20px;\n}\n\n/* 页面标题 */\n.app-header {\n  text-align: center;\n  margin-bottom: 20px;\n  padding: 30px;\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 12px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(10px);\n}\n\n.app-title {\n  font-size: 2.5em;\n  font-weight: 300;\n  color: #2c3e50;\n  margin: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 15px;\n}\n\n.app-title i {\n  color: #667eea;\n}\n\n.app-subtitle {\n  font-size: 1.1em;\n  color: #7f8c8d;\n  margin: 10px 0 0 0;\n  font-weight: 400;\n}\n\n/* 连接状态指示器 */\n.connection-alert {\n  margin-bottom: 20px;\n}\n\n.alert {\n  padding: 15px 20px;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(10px);\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n}\n\n.alert-warning {\n  border-left: 4px solid #f39c12;\n  color: #e67e22;\n}\n\n.alert-content strong {\n  display: block;\n  margin-bottom: 5px;\n  font-weight: 600;\n}\n\n.alert-content p {\n  margin: 0;\n  opacity: 0.8;\n}\n\n/* 主要内容区域 - 左右布局 */\n.main-content {\n  display: flex;\n  gap: 20px;\n  min-height: calc(100vh - 200px);\n}\n\n/* 左侧控制面板 */\n.left-panel {\n  flex: 0 0 400px;\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 12px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(10px);\n  overflow: hidden;\n}\n\n/* 右侧图像展示区域 */\n.right-panel {\n  flex: 1;\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 12px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\n  backdrop-filter: blur(10px);\n  overflow: hidden;\n}\n\n/* 响应式设计 */\n@media (max-width: 1024px) {\n  .main-content {\n    flex-direction: column;\n  }\n  \n  .left-panel {\n    flex: none;\n  }\n  \n  .app-title {\n    font-size: 2em;\n    flex-direction: column;\n    gap: 10px;\n  }\n}\n\n@media (max-width: 768px) {\n  .scanner-app {\n    padding: 10px;\n  }\n  \n  .app-header {\n    padding: 20px;\n  }\n  \n  .app-title {\n    font-size: 1.8em;\n  }\n  \n  .app-subtitle {\n    font-size: 1em;\n  }\n  \n  .main-content {\n    gap: 15px;\n  }\n}\n", "/* 控制面板样式 */\n.control-panel {\n  height: 100%;\n  overflow-y: auto;\n  padding: 0;\n}\n\n/* 面板区域 */\n.panel-section {\n  border-bottom: 1px solid #e9ecef;\n}\n\n.panel-section:last-child {\n  border-bottom: none;\n}\n\n/* 区域标题 */\n.section-header {\n  padding: 20px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  font-weight: 600;\n  font-size: 1.1em;\n}\n\n.section-header i {\n  font-size: 1.2em;\n}\n\n/* 区域内容 */\n.section-content {\n  padding: 20px;\n}\n\n/* 表单组 */\n.form-group {\n  margin-bottom: 20px;\n}\n\n.form-group:last-child {\n  margin-bottom: 0;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 8px;\n  font-weight: 600;\n  color: #495057;\n  font-size: 0.9em;\n}\n\n/* 表单控件 */\n.form-control {\n  width: 100%;\n  padding: 10px 12px;\n  border: 2px solid #e9ecef;\n  border-radius: 8px;\n  font-size: 14px;\n  transition: all 0.3s ease;\n  background: white;\n}\n\n.form-control:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n\n.form-control:disabled {\n  background-color: #f8f9fa;\n  color: #6c757d;\n}\n\n/* DPI输入框 */\n.dpi-inputs {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.dpi-inputs .form-control {\n  flex: 1;\n}\n\n.dpi-separator {\n  font-weight: bold;\n  color: #6c757d;\n  font-size: 1.2em;\n}\n\n/* 复选框网格 */\n.checkbox-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 12px;\n}\n\n.checkbox-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  cursor: pointer;\n  padding: 8px;\n  border-radius: 6px;\n  transition: background-color 0.2s ease;\n  font-size: 0.9em;\n}\n\n.checkbox-item:hover {\n  background-color: #f8f9fa;\n}\n\n.checkbox-item input[type=\"checkbox\"] {\n  width: 16px;\n  height: 16px;\n  accent-color: #667eea;\n}\n\n.checkbox-item span {\n  color: #495057;\n  font-weight: 500;\n}\n\n/* 按钮网格 */\n.button-grid {\n  display: grid;\n  gap: 12px;\n}\n\n/* 按钮样式 */\n.btn {\n  padding: 12px 16px;\n  border: none;\n  border-radius: 8px;\n  font-size: 14px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 8px;\n  text-decoration: none;\n  background: none;\n  position: relative;\n  overflow: hidden;\n}\n\n.btn:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.btn:active:not(:disabled) {\n  transform: translateY(0);\n}\n\n.btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  transform: none;\n}\n\n/* 按钮颜色变体 */\n.btn-primary {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n}\n\n.btn-success {\n  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);\n  color: white;\n}\n\n.btn-info {\n  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);\n  color: white;\n}\n\n.btn-warning {\n  background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);\n  color: white;\n}\n\n.btn-secondary {\n  background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);\n  color: white;\n}\n\n/* 状态信息 */\n.status-info {\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 16px;\n}\n\n.status-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.status-item:last-child {\n  margin-bottom: 0;\n}\n\n.status-label {\n  font-weight: 600;\n  color: #495057;\n  font-size: 0.9em;\n}\n\n.status-value {\n  font-weight: 500;\n  font-size: 0.9em;\n}\n\n.status-value.connected {\n  color: #28a745;\n}\n\n.status-value.disconnected {\n  color: #dc3545;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .checkbox-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .section-content {\n    padding: 15px;\n  }\n  \n  .section-header {\n    padding: 15px;\n    font-size: 1em;\n  }\n  \n  .btn {\n    padding: 10px 14px;\n    font-size: 13px;\n  }\n}\n", "/* 图像画廊样式 */\n.image-gallery {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n/* 画廊标题栏 */\n.gallery-header {\n  padding: 20px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.header-left {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.gallery-title {\n  font-weight: 600;\n  font-size: 1.1em;\n}\n\n.header-left i {\n  font-size: 1.2em;\n}\n\n/* 视图切换按钮 */\n.view-toggle {\n  display: flex;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 6px;\n  overflow: hidden;\n}\n\n.toggle-btn {\n  padding: 8px 12px;\n  border: none;\n  background: transparent;\n  color: white;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 14px;\n}\n\n.toggle-btn:hover {\n  background: rgba(255, 255, 255, 0.1);\n}\n\n.toggle-btn.active {\n  background: rgba(255, 255, 255, 0.3);\n}\n\n/* 画廊内容区域 */\n.gallery-content {\n  flex: 1;\n  overflow-y: auto;\n  padding: 20px;\n}\n\n/* 空状态 */\n.empty-state {\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.empty-content {\n  text-align: center;\n  color: #6c757d;\n}\n\n.empty-icon {\n  font-size: 4em;\n  margin-bottom: 20px;\n  color: #dee2e6;\n}\n\n.empty-content h3 {\n  margin-bottom: 10px;\n  color: #495057;\n}\n\n.empty-content p {\n  margin: 0;\n  font-size: 0.9em;\n}\n\n/* 网格视图 */\n.image-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n  gap: 20px;\n}\n\n.image-card {\n  background: white;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n}\n\n.image-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);\n}\n\n.image-wrapper {\n  position: relative;\n  overflow: hidden;\n}\n\n.scan-image {\n  width: 100%;\n  height: 200px;\n  object-fit: cover;\n  cursor: pointer;\n  transition: transform 0.3s ease;\n}\n\n.scan-image:hover {\n  transform: scale(1.05);\n}\n\n/* 图像覆盖层 */\n.image-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.image-wrapper:hover .image-overlay {\n  opacity: 1;\n}\n\n.overlay-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.action-btn {\n  width: 40px;\n  height: 40px;\n  border: none;\n  border-radius: 50%;\n  color: white;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n}\n\n.action-btn:hover {\n  transform: scale(1.1);\n}\n\n.preview-btn {\n  background: #667eea;\n}\n\n.download-btn {\n  background: #28a745;\n}\n\n.delete-btn {\n  background: #dc3545;\n}\n\n/* 图像信息 */\n.image-info {\n  padding: 15px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.image-number {\n  font-weight: 600;\n  color: #495057;\n}\n\n.image-format {\n  background: #e9ecef;\n  color: #6c757d;\n  padding: 2px 8px;\n  border-radius: 4px;\n  font-size: 0.8em;\n  font-weight: 500;\n}\n\n/* 列表视图 */\n.image-list {\n  background: white;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.list-header {\n  display: grid;\n  grid-template-columns: 100px 1fr 100px 150px;\n  gap: 20px;\n  padding: 15px 20px;\n  background: #f8f9fa;\n  font-weight: 600;\n  color: #495057;\n  border-bottom: 1px solid #dee2e6;\n}\n\n.list-body {\n  max-height: calc(100vh - 300px);\n  overflow-y: auto;\n}\n\n.list-item {\n  display: grid;\n  grid-template-columns: 100px 1fr 100px 150px;\n  gap: 20px;\n  padding: 15px 20px;\n  border-bottom: 1px solid #f1f3f4;\n  align-items: center;\n  transition: background-color 0.2s ease;\n}\n\n.list-item:hover {\n  background-color: #f8f9fa;\n}\n\n.list-item:last-child {\n  border-bottom: none;\n}\n\n.thumbnail {\n  width: 80px;\n  height: 60px;\n  object-fit: cover;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: transform 0.2s ease;\n}\n\n.thumbnail:hover {\n  transform: scale(1.1);\n}\n\n.format-tag {\n  background: #667eea;\n  color: white;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 0.8em;\n  font-weight: 500;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 8px;\n}\n\n.list-action-btn {\n  width: 32px;\n  height: 32px;\n  border: none;\n  border-radius: 6px;\n  color: white;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n}\n\n.list-action-btn:hover {\n  transform: scale(1.1);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .image-grid {\n    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n    gap: 15px;\n  }\n  \n  .gallery-content {\n    padding: 15px;\n  }\n  \n  .gallery-header {\n    padding: 15px;\n    flex-direction: column;\n    gap: 15px;\n  }\n  \n  .list-header,\n  .list-item {\n    grid-template-columns: 80px 1fr 80px 120px;\n    gap: 15px;\n    padding: 12px 15px;\n  }\n  \n  .thumbnail {\n    width: 60px;\n    height: 45px;\n  }\n  \n  .action-buttons {\n    flex-direction: column;\n    gap: 4px;\n  }\n  \n  .list-action-btn {\n    width: 28px;\n    height: 28px;\n    font-size: 12px;\n  }\n}\n\n@media (max-width: 480px) {\n  .image-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .list-header,\n  .list-item {\n    grid-template-columns: 1fr;\n    gap: 10px;\n  }\n  \n  .col-preview,\n  .col-name,\n  .col-format,\n  .col-actions {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n  }\n  \n  .col-preview::before {\n    content: \"预览:\";\n    font-weight: 600;\n  }\n  \n  .col-name::before {\n    content: \"名称:\";\n    font-weight: 600;\n  }\n  \n  .col-format::before {\n    content: \"格式:\";\n    font-weight: 600;\n  }\n  \n  .col-actions::before {\n    content: \"操作:\";\n    font-weight: 600;\n  }\n  \n  .action-buttons {\n    flex-direction: row;\n  }\n}\n", "/* 上传模态框样式 */\n.modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  backdrop-filter: blur(4px);\n}\n\n.modal-container {\n  background: white;\n  border-radius: 12px;\n  width: 90%;\n  max-width: 500px;\n  max-height: 90vh;\n  overflow: hidden;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n  animation: modalSlideIn 0.3s ease-out;\n}\n\n/* 模态框标题 */\n.modal-header {\n  padding: 20px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.modal-title {\n  margin: 0;\n  font-size: 1.2em;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.modal-close {\n  background: none;\n  border: none;\n  color: white;\n  font-size: 1.2em;\n  cursor: pointer;\n  padding: 5px;\n  border-radius: 4px;\n  transition: background-color 0.2s ease;\n}\n\n.modal-close:hover {\n  background: rgba(255, 255, 255, 0.2);\n}\n\n/* 模态框内容 */\n.modal-body {\n  padding: 20px;\n  max-height: 60vh;\n  overflow-y: auto;\n}\n\n.form-group {\n  margin-bottom: 20px;\n}\n\n.form-group:last-child {\n  margin-bottom: 0;\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 8px;\n  font-weight: 600;\n  color: #495057;\n  font-size: 0.9em;\n}\n\n.form-control {\n  width: 100%;\n  padding: 10px 12px;\n  border: 2px solid #e9ecef;\n  border-radius: 8px;\n  font-size: 14px;\n  transition: all 0.3s ease;\n  font-family: inherit;\n}\n\n.form-control:focus {\n  outline: none;\n  border-color: #667eea;\n  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n}\n\ntextarea.form-control {\n  resize: vertical;\n  min-height: 80px;\n}\n\n/* 单选按钮组 */\n.radio-group {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.radio-item {\n  display: flex;\n  align-items: flex-start;\n  gap: 12px;\n  padding: 15px;\n  border: 2px solid #e9ecef;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  background: #f8f9fa;\n}\n\n.radio-item:hover {\n  border-color: #667eea;\n  background: #f0f4ff;\n}\n\n.radio-item input[type=\"radio\"] {\n  margin-top: 2px;\n  accent-color: #667eea;\n}\n\n.radio-item input[type=\"radio\"]:checked + .radio-label {\n  color: #667eea;\n  font-weight: 600;\n}\n\n.radio-item:has(input[type=\"radio\"]:checked) {\n  border-color: #667eea;\n  background: #f0f4ff;\n}\n\n.radio-label {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: 500;\n  color: #495057;\n  flex: 1;\n}\n\n.radio-label i {\n  font-size: 1.1em;\n}\n\n.radio-desc {\n  font-size: 0.8em;\n  color: #6c757d;\n  margin-top: 4px;\n}\n\n/* 上传信息 */\n.upload-info {\n  background: #e3f2fd;\n  border: 1px solid #bbdefb;\n  border-radius: 8px;\n  padding: 15px;\n  margin-top: 20px;\n}\n\n.info-item {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  margin-bottom: 8px;\n  font-size: 0.9em;\n  color: #1976d2;\n}\n\n.info-item:last-child {\n  margin-bottom: 0;\n}\n\n.info-item i {\n  font-size: 1em;\n}\n\n/* 模态框底部 */\n.modal-footer {\n  padding: 20px;\n  border-top: 1px solid #e9ecef;\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n  background: #f8f9fa;\n}\n\n.btn {\n  padding: 10px 20px;\n  border: none;\n  border-radius: 6px;\n  font-size: 14px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  text-decoration: none;\n  background: none;\n}\n\n.btn:hover:not(:disabled) {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.btn:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  transform: none;\n}\n\n.btn-primary {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n}\n\n.btn-secondary {\n  background: #6c757d;\n  color: white;\n}\n\n/* 动画效果 */\n@keyframes modalSlideIn {\n  from {\n    opacity: 0;\n    transform: scale(0.9) translateY(-20px);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1) translateY(0);\n  }\n}\n\n.fa-spin {\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .modal-container {\n    width: 95%;\n    margin: 10px;\n  }\n  \n  .modal-header,\n  .modal-body,\n  .modal-footer {\n    padding: 15px;\n  }\n  \n  .modal-title {\n    font-size: 1.1em;\n  }\n  \n  .radio-item {\n    padding: 12px;\n  }\n  \n  .modal-footer {\n    flex-direction: column;\n  }\n  \n  .btn {\n    width: 100%;\n    justify-content: center;\n  }\n}\n", "/* 预览模态框样式 */\n.preview-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.9);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 2000;\n  backdrop-filter: blur(4px);\n}\n\n.preview-container {\n  width: 95%;\n  height: 95%;\n  max-width: 1200px;\n  max-height: 900px;\n  background: white;\n  border-radius: 12px;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);\n  animation: previewSlideIn 0.3s ease-out;\n}\n\n/* 预览标题栏 */\n.preview-header {\n  padding: 15px 20px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-shrink: 0;\n}\n\n.preview-title {\n  font-size: 1.1em;\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.preview-info {\n  font-size: 0.9em;\n  background: rgba(255, 255, 255, 0.2);\n  padding: 4px 12px;\n  border-radius: 20px;\n}\n\n.preview-close {\n  background: none;\n  border: none;\n  color: white;\n  font-size: 1.2em;\n  cursor: pointer;\n  padding: 8px;\n  border-radius: 4px;\n  transition: background-color 0.2s ease;\n}\n\n.preview-close:hover {\n  background: rgba(255, 255, 255, 0.2);\n}\n\n/* 预览内容区域 */\n.preview-content {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  background: #f8f9fa;\n  overflow: hidden;\n}\n\n/* 导航按钮 */\n.nav-btn {\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 50px;\n  height: 50px;\n  background: rgba(0, 0, 0, 0.7);\n  border: none;\n  border-radius: 50%;\n  color: white;\n  font-size: 1.2em;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  z-index: 10;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.nav-btn:hover:not(.disabled) {\n  background: rgba(0, 0, 0, 0.9);\n  transform: translateY(-50%) scale(1.1);\n}\n\n.nav-btn.disabled {\n  opacity: 0.3;\n  cursor: not-allowed;\n}\n\n.nav-prev {\n  left: 20px;\n}\n\n.nav-next {\n  right: 20px;\n}\n\n/* 图像容器 */\n.image-container {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n  height: 100%;\n}\n\n.preview-image {\n  max-width: 100%;\n  max-height: 100%;\n  object-fit: contain;\n  border-radius: 8px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);\n  transition: transform 0.3s ease;\n}\n\n.preview-image:hover {\n  transform: scale(1.02);\n}\n\n/* 预览底部操作栏 */\n.preview-footer {\n  padding: 15px 20px;\n  background: white;\n  border-top: 1px solid #e9ecef;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-shrink: 0;\n}\n\n.image-details {\n  display: flex;\n  gap: 20px;\n}\n\n.detail-item {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  font-size: 0.9em;\n  color: #6c757d;\n}\n\n.detail-item i {\n  color: #667eea;\n}\n\n.preview-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.action-btn {\n  padding: 8px 16px;\n  border: none;\n  border-radius: 6px;\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 6px;\n}\n\n.action-btn:hover:not(:disabled) {\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.action-btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  transform: none;\n}\n\n.prev-btn,\n.next-btn {\n  background: #6c757d;\n  color: white;\n}\n\n.download-btn {\n  background: #28a745;\n  color: white;\n}\n\n.close-btn {\n  background: #dc3545;\n  color: white;\n}\n\n/* 缩略图导航 */\n.thumbnail-nav {\n  background: #f8f9fa;\n  border-top: 1px solid #e9ecef;\n  padding: 15px;\n  flex-shrink: 0;\n}\n\n.thumbnail-list {\n  display: flex;\n  gap: 10px;\n  overflow-x: auto;\n  padding: 5px 0;\n  justify-content: center;\n}\n\n.thumbnail-item {\n  position: relative;\n  cursor: pointer;\n  border-radius: 6px;\n  overflow: hidden;\n  transition: all 0.3s ease;\n  border: 2px solid transparent;\n  flex-shrink: 0;\n}\n\n.thumbnail-item:hover {\n  transform: scale(1.05);\n}\n\n.thumbnail-item.active {\n  border-color: #667eea;\n  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.3);\n}\n\n.thumbnail-image {\n  width: 60px;\n  height: 45px;\n  object-fit: cover;\n  display: block;\n}\n\n.thumbnail-number {\n  position: absolute;\n  bottom: 2px;\n  right: 2px;\n  background: rgba(0, 0, 0, 0.7);\n  color: white;\n  font-size: 0.7em;\n  padding: 1px 4px;\n  border-radius: 2px;\n}\n\n/* 动画效果 */\n@keyframes previewSlideIn {\n  from {\n    opacity: 0;\n    transform: scale(0.9);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1);\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .preview-container {\n    width: 100%;\n    height: 100%;\n    border-radius: 0;\n  }\n  \n  .preview-header {\n    padding: 12px 15px;\n  }\n  \n  .preview-title {\n    font-size: 1em;\n  }\n  \n  .nav-btn {\n    width: 40px;\n    height: 40px;\n    font-size: 1em;\n  }\n  \n  .nav-prev {\n    left: 10px;\n  }\n  \n  .nav-next {\n    right: 10px;\n  }\n  \n  .image-container {\n    padding: 10px;\n  }\n  \n  .preview-footer {\n    padding: 12px 15px;\n    flex-direction: column;\n    gap: 15px;\n  }\n  \n  .preview-actions {\n    width: 100%;\n    justify-content: center;\n    flex-wrap: wrap;\n  }\n  \n  .action-btn {\n    flex: 1;\n    min-width: 80px;\n    justify-content: center;\n  }\n  \n  .thumbnail-nav {\n    padding: 10px;\n  }\n  \n  .thumbnail-image {\n    width: 50px;\n    height: 38px;\n  }\n}\n\n@media (max-width: 480px) {\n  .image-details {\n    flex-direction: column;\n    gap: 8px;\n    align-items: center;\n  }\n  \n  .preview-actions {\n    grid-template-columns: 1fr 1fr;\n    gap: 8px;\n  }\n  \n  .action-btn {\n    font-size: 12px;\n    padding: 6px 12px;\n  }\n}\n", "/* 消息提示容器 */\n.message-container {\n  position: fixed;\n  top: 20px;\n  right: 20px;\n  z-index: 3000;\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n  pointer-events: none;\n}\n\n/* 消息提示样式 */\n.message-toast {\n  padding: 12px 20px;\n  border-radius: 8px;\n  color: white;\n  font-weight: 500;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  min-width: 300px;\n  max-width: 400px;\n  animation: slideInRight 0.3s ease-out;\n  backdrop-filter: blur(10px);\n  pointer-events: auto;\n}\n\n.message-toast i {\n  font-size: 1.1em;\n  flex-shrink: 0;\n}\n\n.message-text {\n  flex: 1;\n  word-wrap: break-word;\n}\n\n/* 消息类型样式 */\n.message-success {\n  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\n}\n\n.message-error {\n  background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);\n}\n\n.message-warning {\n  background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);\n}\n\n.message-info {\n  background: linear-gradient(135deg, #17a2b8 0%, #007bff 100%);\n}\n\n/* 动画效果 */\n@keyframes slideInRight {\n  from {\n    opacity: 0;\n    transform: translateX(100%);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .message-container {\n    top: 10px;\n    right: 10px;\n    left: 10px;\n  }\n  \n  .message-toast {\n    min-width: auto;\n    max-width: none;\n    padding: 10px 15px;\n    font-size: 14px;\n  }\n  \n  .message-toast i {\n    font-size: 1em;\n  }\n}\n"], "names": [], "sourceRoot": ""}