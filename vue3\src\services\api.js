// API服务模块
import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:8080',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    console.log('发送请求:', config.method?.toUpperCase(), config.url)
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    console.log('收到响应:', response.status, response.config.url)
    return response
  },
  error => {
    console.error('响应错误:', error.response?.status, error.message)
    if (error.response?.status === 404) {
      console.error('API接口不存在')
    } else if (error.response?.status >= 500) {
      console.error('服务器内部错误')
    } else if (error.code === 'ECONNREFUSED') {
      console.error('无法连接到服务器，请确保后台服务已启动')
    }
    return Promise.reject(error)
  }
)

// API接口定义
export const scanAPI = {
  // 健康检查
  healthCheck() {
    return api.get('/health')
  },

  // 上传PDF格式的所有图像
  uploadAllImageAsPdf(imageData, id = 'scan', description = '扫描文档') {
    return api.post('/upload', {
      id,
      desc: description,
      format: 'pdf',
      imageData
    })
  },

  // 上传TIFF格式的所有图像
  uploadAllImageAsTiff(imageData, id = 'scan', description = '扫描文档') {
    return api.post('/upload/tiff', {
      id,
      desc: description,
      format: 'tiff',
      imageData
    })
  },

  // 上传单个JPG图像
  uploadJpgImageByIndex(imageData, index, id = 'scan', description = '扫描图像') {
    return api.post('/upload/jpg', {
      id,
      desc: description,
      index,
      imageData
    })
  },

  // 批量上传多个JPG图像
  uploadMultipleJpgImages(images, id = 'scan', description = '扫描图像') {
    const uploadPromises = images.map((imageData, index) => 
      this.uploadJpgImageByIndex(imageData, index, id, description)
    )
    return Promise.all(uploadPromises)
  },

  // 获取已上传文件列表
  getUploadedFiles() {
    return api.get('/files')
  },

  // 删除文件
  deleteFile(filename) {
    return api.delete(`/files/${filename}`)
  }
}

// 工具函数
export const utils = {
  // 将base64字符串转换为Blob
  base64ToBlob(base64, mimeType = 'image/jpeg') {
    const byteCharacters = atob(base64)
    const byteNumbers = new Array(byteCharacters.length)
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i)
    }
    const byteArray = new Uint8Array(byteNumbers)
    return new Blob([byteArray], { type: mimeType })
  },

  // 下载base64图像
  downloadBase64Image(base64, filename = 'image.jpg') {
    const blob = this.base64ToBlob(base64)
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  },

  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  // 生成唯一ID
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }
}

export default api
