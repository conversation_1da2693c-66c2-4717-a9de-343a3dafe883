using Microsoft.AspNetCore.Mvc;
using ScanOnWeb.Models;
using ScanOnWeb.Services;
using System.Text.Json;

namespace ScanOnWeb.Controllers;

/// <summary>
/// 文件上传控制器
/// </summary>
[ApiController]
[Route("")]
public class UploadController : ControllerBase
{
    private readonly IFileService _fileService;
    private readonly ILogger<UploadController> _logger;

    public UploadController(IFileService fileService, ILogger<UploadController> logger)
    {
        _fileService = fileService;
        _logger = logger;
    }

    /// <summary>
    /// PDF文件上传
    /// </summary>
    [HttpPost("upload")]
    public async Task<ActionResult<UploadResponse>> UploadPdf()
    {
        return await HandleUpload("pdf");
    }

    /// <summary>
    /// TIFF文件上传
    /// </summary>
    [HttpPost("upload-tiff")]
    public async Task<ActionResult<UploadResponse>> UploadTiff()
    {
        return await HandleUpload("tiff");
    }

    /// <summary>
    /// JPG文件上传
    /// </summary>
    [HttpPost("upload-jpg")]
    public async Task<ActionResult<UploadResponse>> UploadJpg()
    {
        return await HandleUpload("jpg");
    }

    /// <summary>
    /// 通用上传处理
    /// </summary>
    private async Task<ActionResult<UploadResponse>> HandleUpload(string format)
    {
        try
        {
            var contentType = Request.ContentType ?? "";
            _logger.LogInformation("收到{Format}上传请求, Content-Type: {ContentType}", 
                format.ToUpper(), contentType);

            if (contentType.StartsWith("multipart/form-data"))
            {
                return await HandleMultipartUpload(format);
            }
            else if (contentType.StartsWith("application/json"))
            {
                return await HandleJsonUpload(format);
            }
            else
            {
                var response = UploadResponse.CreateError($"不支持的Content-Type: {contentType}");
                return BadRequest(response);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "{Format}上传失败", format.ToUpper());
            var response = UploadResponse.CreateError($"{format.ToUpper()}上传失败: {ex.Message}", 500);
            return StatusCode(500, response);
        }
    }

    /// <summary>
    /// 处理multipart/form-data上传
    /// </summary>
    private async Task<ActionResult<UploadResponse>> HandleMultipartUpload(string format)
    {
        // 获取文件
        var file = Request.Form.Files.GetFile("image");
        if (file == null || file.Length == 0)
        {
            var response = UploadResponse.CreateError("缺少上传文件");
            return BadRequest(response);
        }

        // 获取参数
        var id = Request.Form["id"].FirstOrDefault();
        var description = Request.Form["desc"].FirstOrDefault();
        var indexStr = Request.Form["index"].FirstOrDefault();

        if (string.IsNullOrWhiteSpace(id))
        {
            id = $"scan_{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}";
        }

        description ??= "扫描文档";

        // 处理JPG的index参数
        if (!string.IsNullOrWhiteSpace(indexStr) && int.TryParse(indexStr, out var index))
        {
            id = $"{id}_{index}";
        }

        // 保存文件
        var filename = await _fileService.SaveUploadedFileAsync(file, id, format);

        // 构建响应数据
        var data = new
        {
            filename,
            originalName = file.FileName,
            size = file.Length,
            id,
            format,
            index = !string.IsNullOrWhiteSpace(indexStr) && int.TryParse(indexStr, out var idx) ? idx : (int?)null
        };

        var message = $"{format.ToUpper()}文件上传成功";
        var path = $"/uploads/{filename}";

        var successResponse = UploadResponse.CreateSuccess(message, id, path, data);
        return Ok(successResponse);
    }

    /// <summary>
    /// 处理JSON上传
    /// </summary>
    private async Task<ActionResult<UploadResponse>> HandleJsonUpload(string format)
    {
        // 读取JSON数据
        using var reader = new StreamReader(Request.Body);
        var jsonString = await reader.ReadToEndAsync();

        if (string.IsNullOrWhiteSpace(jsonString))
        {
            var response = UploadResponse.CreateError("缺少JSON数据");
            return BadRequest(response);
        }

        // 解析JSON
        UploadRequest? uploadRequest;
        try
        {
            uploadRequest = JsonSerializer.Deserialize<UploadRequest>(jsonString, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "JSON解析失败");
            var response = UploadResponse.CreateError("JSON格式错误");
            return BadRequest(response);
        }

        if (uploadRequest == null || string.IsNullOrWhiteSpace(uploadRequest.ImageData))
        {
            var response = UploadResponse.CreateError("缺少图像数据");
            return BadRequest(response);
        }

        // 处理文件ID
        var id = uploadRequest.Id;
        if (string.IsNullOrWhiteSpace(id))
        {
            id = $"scan_{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}";
        }

        if (uploadRequest.Index.HasValue)
        {
            id = $"{id}_{uploadRequest.Index.Value}";
        }

        // 保存Base64文件
        var filename = await _fileService.SaveBase64FileAsync(uploadRequest.ImageData, id, format);

        // 构建响应数据
        var data = new
        {
            filename,
            id,
            format,
            index = uploadRequest.Index
        };

        var message = $"{format.ToUpper()}文件上传成功";
        var path = $"/uploads/{filename}";

        var successResponse = UploadResponse.CreateSuccess(message, id, path, data);
        return Ok(successResponse);
    }
}
