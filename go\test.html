<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>扫描系统测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        h1 {
            color: #409eff;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .status.success {
            background-color: #f0f9ff;
            border-color: #67c23a;
            color: #67c23a;
        }
        .status.error {
            background-color: #fef0f0;
            border-color: #f56c6c;
            color: #f56c6c;
        }
        .status.warning {
            background-color: #fdf6ec;
            border-color: #e6a23c;
            color: #e6a23c;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #dcdfe6;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #303133;
        }
        button {
            background-color: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #66b1ff;
        }
        button:disabled {
            background-color: #c0c4cc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 文档扫描系统测试页面</h1>
        
        <div class="test-section">
            <h3>📡 后台服务连接测试</h3>
            <p>测试Go后台服务是否正常运行</p>
            <button onclick="testBackendHealth()">测试后台连接</button>
            <div id="backend-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🖥️ 扫描服务连接测试</h3>
            <p>测试WebSocket扫描服务连接</p>
            <button onclick="testScanService()">测试扫描服务</button>
            <div id="scan-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📤 文件上传测试</h3>
            <p>测试文件上传功能</p>
            <button onclick="testFileUpload()">测试上传</button>
            <div id="upload-result" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📋 系统状态</h3>
            <div id="system-status">
                <div class="status warning">
                    <strong>⏳ 正在检测系统状态...</strong>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🚀 快速启动指南</h3>
            <ol>
                <li><strong>启动Go后台服务：</strong> 在 <code>go</code> 目录下运行 <code>go run main.go</code></li>
                <li><strong>启动Vue3前端：</strong> 在 <code>vue3</code> 目录下运行 <code>npm run serve</code></li>
                <li><strong>启动扫描服务：</strong> 运行ScanOnWeb托盘程序</li>
                <li><strong>访问应用：</strong> 打开浏览器访问 <code>http://localhost:8081</code></li>
            </ol>
        </div>
    </div>

    <script>
        // 测试后台服务健康状态
        async function testBackendHealth() {
            const resultDiv = document.getElementById('backend-result');
            resultDiv.textContent = '正在测试后台连接...';
            
            try {
                const response = await fetch('http://localhost:8080/health');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.textContent = `✅ 后台服务正常运行\n状态: ${data.status}\n时间: ${data.time}`;
                    updateSystemStatus('backend', true);
                } else {
                    resultDiv.textContent = `❌ 后台服务响应异常\n状态码: ${response.status}`;
                    updateSystemStatus('backend', false);
                }
            } catch (error) {
                resultDiv.textContent = `❌ 无法连接到后台服务\n错误: ${error.message}\n请确保Go服务已启动在端口8080`;
                updateSystemStatus('backend', false);
            }
        }

        // 测试扫描服务连接
        function testScanService() {
            const resultDiv = document.getElementById('scan-result');
            resultDiv.textContent = '正在测试扫描服务连接...';
            
            const ports = [1001, 2001, 3001, 4001, 5001];
            let connectedPort = null;
            let testCount = 0;
            
            ports.forEach(port => {
                const ws = new WebSocket(`ws://127.0.0.1:${port}`);
                
                ws.onopen = function() {
                    connectedPort = port;
                    resultDiv.textContent = `✅ 扫描服务连接成功\n端口: ${port}\n状态: 已连接`;
                    updateSystemStatus('scan', true);
                    ws.close();
                };
                
                ws.onerror = function() {
                    testCount++;
                    if (testCount === ports.length && !connectedPort) {
                        resultDiv.textContent = `❌ 无法连接到扫描服务\n已测试端口: ${ports.join(', ')}\n请确保ScanOnWeb托盘程序已启动`;
                        updateSystemStatus('scan', false);
                    }
                };
            });
        }

        // 测试文件上传
        async function testFileUpload() {
            const resultDiv = document.getElementById('upload-result');
            resultDiv.textContent = '正在测试文件上传...';
            
            // 创建一个测试用的base64图像数据（1x1像素的透明PNG）
            const testImageData = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
            
            const uploadData = {
                id: 'test_' + Date.now(),
                desc: '测试上传',
                imageData: testImageData
            };
            
            try {
                const response = await fetch('http://localhost:8080/upload/jpg', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(uploadData)
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    resultDiv.textContent = `✅ 文件上传成功\n文件ID: ${result.fileId}\n路径: ${result.path}`;
                    updateSystemStatus('upload', true);
                } else {
                    resultDiv.textContent = `❌ 文件上传失败\n错误: ${result.message}`;
                    updateSystemStatus('upload', false);
                }
            } catch (error) {
                resultDiv.textContent = `❌ 上传请求失败\n错误: ${error.message}`;
                updateSystemStatus('upload', false);
            }
        }

        // 更新系统状态
        function updateSystemStatus(service, isOk) {
            const statusDiv = document.getElementById('system-status');
            const services = {
                backend: '后台服务',
                scan: '扫描服务', 
                upload: '上传功能'
            };
            
            let existingStatus = statusDiv.querySelector(`[data-service="${service}"]`);
            if (!existingStatus) {
                existingStatus = document.createElement('div');
                existingStatus.className = 'status';
                existingStatus.setAttribute('data-service', service);
                statusDiv.appendChild(existingStatus);
            }
            
            existingStatus.className = `status ${isOk ? 'success' : 'error'}`;
            existingStatus.innerHTML = `<strong>${isOk ? '✅' : '❌'} ${services[service]}: ${isOk ? '正常' : '异常'}</strong>`;
        }

        // 页面加载时自动测试
        window.onload = function() {
            setTimeout(() => {
                testBackendHealth();
                setTimeout(() => {
                    testScanService();
                }, 1000);
            }, 500);
        };
    </script>
</body>
</html>
