#!/bin/bash

echo "================================"
echo "ScanOnWeb SpringBoot Server"
echo "================================"

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境，请确保已安装Java 17或更高版本"
    exit 1
fi

# 检查Maven环境
if ! command -v mvn &> /dev/null; then
    echo "错误: 未找到Maven环境，请确保已安装Maven"
    exit 1
fi

echo "正在编译项目..."
mvn clean compile

if [ $? -ne 0 ]; then
    echo "编译失败，请检查错误信息"
    exit 1
fi

echo "正在启动SpringBoot服务器..."
echo "访问地址: http://localhost:8090/api"
echo "按 Ctrl+C 停止服务器"

mvn spring-boot:run
