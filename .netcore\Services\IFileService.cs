using ScanOnWeb.Models;

namespace ScanOnWeb.Services;

/// <summary>
/// 文件服务接口
/// </summary>
public interface IFileService
{
    /// <summary>
    /// 保存上传的文件
    /// </summary>
    /// <param name="file">上传的文件</param>
    /// <param name="id">文件ID</param>
    /// <param name="format">文件格式</param>
    /// <returns>保存的文件名</returns>
    Task<string> SaveUploadedFileAsync(IFormFile file, string id, string format);

    /// <summary>
    /// 保存Base64编码的文件
    /// </summary>
    /// <param name="base64Data">Base64数据</param>
    /// <param name="id">文件ID</param>
    /// <param name="format">文件格式</param>
    /// <returns>保存的文件名</returns>
    Task<string> SaveBase64FileAsync(string base64Data, string id, string format);

    /// <summary>
    /// 获取文件列表
    /// </summary>
    /// <returns>文件列表</returns>
    Task<List<Models.FileInfo>> GetFileListAsync();

    /// <summary>
    /// 删除文件
    /// </summary>
    /// <param name="filename">文件名</param>
    /// <returns>是否删除成功</returns>
    Task<bool> DeleteFileAsync(string filename);

    /// <summary>
    /// 获取文件路径
    /// </summary>
    /// <param name="filename">文件名</param>
    /// <returns>文件路径</returns>
    string GetFilePath(string filename);

    /// <summary>
    /// 检查文件是否存在
    /// </summary>
    /// <param name="filename">文件名</param>
    /// <returns>是否存在</returns>
    bool FileExists(string filename);
}
