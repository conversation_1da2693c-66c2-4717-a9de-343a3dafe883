package com.scanonweb.servlet;

import com.scanonweb.util.ResponseUtil;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查Servlet
 */
public class HealthServlet extends HttpServlet {
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        Map<String, Object> healthData = new HashMap<>();
        healthData.put("status", "ok");
        healthData.put("time", LocalDateTime.now().toString());
        healthData.put("service", "ScanOnWeb Servlet Server");
        healthData.put("version", "1.0.0");
        
        ResponseUtil.sendJsonResponse(response, healthData);
    }
}
