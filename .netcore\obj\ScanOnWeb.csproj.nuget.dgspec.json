{"format": 1, "restore": {"D:\\peihexian\\scanonwebh5_demo\\.netcore\\ScanOnWeb.csproj": {}}, "projects": {"D:\\peihexian\\scanonwebh5_demo\\.netcore\\ScanOnWeb.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\peihexian\\scanonwebh5_demo\\.netcore\\ScanOnWeb.csproj", "projectName": "ScanOnWeb", "projectPath": "D:\\peihexian\\scanonwebh5_demo\\.netcore\\ScanOnWeb.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\peihexian\\scanonwebh5_demo\\.netcore\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net7.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "dependencies": {"Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[7.0.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[7.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[4.1.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[5.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.4.0, )"}, "System.Text.Json": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\7.0.203\\RuntimeIdentifierGraph.json"}}}}}