package com.scanonweb.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 文件信息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileInfo {

    /**
     * 文件名
     */
    private String name;

    /**
     * 文件大小（字节）
     */
    private long size;

    /**
     * 修改时间
     */
    private LocalDateTime modTime;

    /**
     * 访问URL
     */
    private String url;

    /**
     * 文件类型
     */
    private String type;

    /**
     * 格式化文件大小
     */
    public String getFormattedSize() {
        if (size == 0) return "0 Bytes";
        
        String[] units = {"Bytes", "KB", "MB", "GB"};
        int unitIndex = 0;
        double fileSize = size;
        
        while (fileSize >= 1024 && unitIndex < units.length - 1) {
            fileSize /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", fileSize, units[unitIndex]);
    }
}
