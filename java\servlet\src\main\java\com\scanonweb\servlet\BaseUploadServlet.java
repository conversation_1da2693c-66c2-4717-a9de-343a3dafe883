package com.scanonweb.servlet;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scanonweb.util.FileUtil;
import com.scanonweb.util.ResponseUtil;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.Part;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 基础上传Servlet
 */
public abstract class BaseUploadServlet extends HttpServlet {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 获取文件格式
     */
    protected abstract String getFileFormat();
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String contentType = request.getContentType();
        
        try {
            if (contentType != null && contentType.startsWith("multipart/form-data")) {
                // 处理multipart/form-data上传（扫描控件）
                handleMultipartUpload(request, response);
            } else if (contentType != null && contentType.startsWith("application/json")) {
                // 处理JSON上传（前端axios）
                handleJsonUpload(request, response);
            } else {
                ResponseUtil.sendError(response, "不支持的Content-Type: " + contentType);
            }
        } catch (Exception e) {
            e.printStackTrace();
            ResponseUtil.sendError(response, "上传失败: " + e.getMessage(), 
                    HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }
    
    /**
     * 处理multipart/form-data上传
     */
    private void handleMultipartUpload(HttpServletRequest request, HttpServletResponse response) 
            throws IOException, ServletException {
        
        // 获取文件部分
        Part filePart = request.getPart("image");
        if (filePart == null || filePart.getSize() == 0) {
            ResponseUtil.sendError(response, "缺少上传文件");
            return;
        }
        
        // 获取参数
        String id = request.getParameter("id");
        String description = request.getParameter("desc");
        String index = request.getParameter("index");
        
        if (id == null || id.trim().isEmpty()) {
            id = "scan_" + System.currentTimeMillis();
        }
        if (description == null) {
            description = "扫描文档";
        }
        
        // 处理JPG的index参数
        if (index != null && !index.trim().isEmpty()) {
            id = id + "_" + index;
        }
        
        // 保存文件
        String filename = FileUtil.saveUploadedFile(filePart.getInputStream(), id, getFileFormat());
        
        // 构建响应数据
        Map<String, Object> data = new HashMap<>();
        data.put("filename", filename);
        data.put("originalName", filePart.getSubmittedFileName());
        data.put("size", filePart.getSize());
        data.put("id", id);
        data.put("format", getFileFormat());
        if (index != null) {
            data.put("index", Integer.parseInt(index));
        }
        
        String message = getFileFormat().toUpperCase() + "文件上传成功";
        String path = "/uploads/" + filename;
        
        ResponseUtil.sendSuccess(response, message, id, path, data);
    }
    
    /**
     * 处理JSON上传
     */
    private void handleJsonUpload(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        
        // 读取JSON数据
        StringBuilder jsonBuilder = new StringBuilder();
        try (BufferedReader reader = request.getReader()) {
            String line;
            while ((line = reader.readLine()) != null) {
                jsonBuilder.append(line);
            }
        }
        
        String jsonString = jsonBuilder.toString();
        if (jsonString.trim().isEmpty()) {
            ResponseUtil.sendError(response, "缺少JSON数据");
            return;
        }
        
        // 解析JSON
        JsonNode jsonNode = objectMapper.readTree(jsonString);
        
        String id = jsonNode.has("id") ? jsonNode.get("id").asText() : null;
        String description = jsonNode.has("desc") ? jsonNode.get("desc").asText() : "扫描文档";
        String imageData = jsonNode.has("imageData") ? jsonNode.get("imageData").asText() : null;
        Integer index = jsonNode.has("index") ? jsonNode.get("index").asInt() : null;
        
        if (imageData == null || imageData.trim().isEmpty()) {
            ResponseUtil.sendError(response, "缺少图像数据");
            return;
        }
        
        if (id == null || id.trim().isEmpty()) {
            id = "scan_" + System.currentTimeMillis();
        }
        
        // 处理JPG的index参数
        if (index != null) {
            id = id + "_" + index;
        }
        
        // 保存文件
        String filename = FileUtil.saveBase64File(imageData, id, getFileFormat());
        
        // 构建响应数据
        Map<String, Object> data = new HashMap<>();
        data.put("filename", filename);
        data.put("id", id);
        data.put("format", getFileFormat());
        if (index != null) {
            data.put("index", index);
        }
        
        String message = getFileFormat().toUpperCase() + "文件上传成功";
        String path = "/uploads/" + filename;
        
        ResponseUtil.sendSuccess(response, message, id, path, data);
    }
}
