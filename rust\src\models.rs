use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 上传请求结构
#[derive(Debug, Deserialize)]
pub struct UploadRequest {
    pub id: String,
    #[serde(rename = "desc")]
    pub description: Option<String>,
    #[serde(rename = "imageData")]
    pub image_data: String,
    pub index: Option<u32>,
}

/// 上传响应结构
#[derive(Debug, Serialize)]
pub struct UploadResponse {
    pub success: bool,
    pub message: String,
    pub code: u16,
    #[serde(rename = "fileId")]
    pub file_id: Option<String>,
    pub path: Option<String>,
    pub data: Option<HashMap<String, serde_json::Value>>,
}

impl UploadResponse {
    /// 创建成功响应
    pub fn success(
        message: String,
        file_id: String,
        path: String,
        data: HashMap<String, serde_json::Value>,
    ) -> Self {
        Self {
            success: true,
            message,
            code: 200,
            file_id: Some(file_id),
            path: Some(path),
            data: Some(data),
        }
    }

    /// 创建错误响应
    pub fn error(message: String, code: u16) -> Self {
        Self {
            success: false,
            message,
            code,
            file_id: None,
            path: None,
            data: None,
        }
    }
}

/// 健康检查响应
#[derive(Debug, Serialize)]
pub struct HealthResponse {
    pub status: String,
    pub time: String,
    pub service: String,
    pub version: String,
}

/// 文件信息
#[derive(Debug, Serialize)]
pub struct FileInfo {
    pub name: String,
    pub size: u64,
    #[serde(rename = "modTime")]
    pub mod_time: String,
    pub url: String,
    #[serde(rename = "type")]
    pub file_type: String,
    #[serde(rename = "formattedSize")]
    pub formatted_size: String,
}

impl FileInfo {
    pub fn new(name: String, size: u64, mod_time: String, file_type: String) -> Self {
        let formatted_size = format_file_size(size);
        let url = format!("/uploads/{}", name);
        
        Self {
            name,
            size,
            mod_time,
            url,
            file_type,
            formatted_size,
        }
    }
}

/// 文件列表响应
#[derive(Debug, Serialize)]
pub struct FileListResponse {
    pub success: bool,
    pub files: Vec<FileInfo>,
    pub count: usize,
}

/// 删除文件响应
#[derive(Debug, Serialize)]
pub struct DeleteResponse {
    pub success: bool,
    pub message: String,
}

/// 格式化文件大小
fn format_file_size(bytes: u64) -> String {
    const UNITS: &[&str] = &["Bytes", "KB", "MB", "GB"];
    
    if bytes == 0 {
        return "0 Bytes".to_string();
    }
    
    let mut size = bytes as f64;
    let mut unit_index = 0;
    
    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }
    
    format!("{:.2} {}", size, UNITS[unit_index])
}
