// scanonweb.js

class ScanOnWeb {
    constructor() {
        this.scaner_work_config = {
            showUI: false,
            dpi_x: 300,
            dpi_y: 300,
            deviceIndex: 0,
            showDialog: false,
            autoFeedEnable: true,
            autoFeed: false,
            dupxMode: false,
            autoDeskew: false,
            autoBorderDetection: false,
            colorMode: "RGB",
            transMode: "memory"
        };
        this.h5socket = null;
        this.imageCount = 0;
        this.tryConnect();
    }

    getConnectedServer(wssUrls) {
        console.log("尝试连接托盘扫描服务websocket服务器...");
        return new Promise((resolve, reject) => {
            const server = new WebSocket(wssUrls[0]);
            server.onopen = () => resolve(server);
            server.onerror = (err) => reject(err);
        }).then((server) => {
            console.log("连接websocket服务器成功!");
            this.initWebsocketCallback(server);
            console.log("尝试获取扫描设备列表...");
            this.loadDevices();
            return server;
        }).catch((err) => {
            console.error("连接websocket服务器失败: " + err);
            if (wssUrls.length > 1) {
                return this.getConnectedServer(wssUrls.slice(1));
            } else {
                console.error("无法连接到任何 WebSocket 服务器");
                return null;
            }
        });
    }

    tryConnect() {
        const wssUrls = ["ws://127.0.0.1:1001", "ws://127.0.0.1:2001", "ws://127.0.0.1:3001", "ws://127.0.0.1:4001", "ws://127.0.0.1:5001"];
        this.getConnectedServer(wssUrls).then(server => {
            if (server) {
                this.h5socket = server;
            }
        });
    }

    initWebsocketCallback(server) {
        this.h5socket = server;
        this.h5socket.onerror = this.onSocketError;
        this.h5socket.onmessage = this.onSocketMessage.bind(this);
    }

    onSocketError(event) {
        alert("无法连接扫描服务程序,请检查扫描服务程序是否已经启动！");
        console.log("WebSocket error: " + event.data);
    }

    onSocketMessage(event) {
        const msg = JSON.parse(event.data);
        switch (msg.cmd_type) {
            case "getDevicesList":
                if (this.isCallbackExist(this.onGetDevicesListEvent)) {
                    this.onGetDevicesListEvent(msg);
                }
                break;
            case "scanComplete":
                this.imageCount = msg.imageCount;
                if (this.isCallbackExist(this.onScanFinishedEvent)) {
                    this.onScanFinishedEvent(msg);
                }
                break;
            case "selectScanDevice":
                this.scaner_work_config.deviceIndex = msg.currentIndex;
                this.scaner_work_config.showDialog = msg.showDialog;
                this.scaner_work_config.autoFeedEnable = msg.autoFeedEnable;
                this.scaner_work_config.autoFeed = msg.autoFeed;
                this.scaner_work_config.dupxMode = msg.dupxMode;
                this.scaner_work_config.autoDeskew = msg.autoDeskew;
                this.scaner_work_config.autoBorderDetection = msg.autoBorderDetection;

                if (this.isCallbackExist(this.onSelectScanDeviceEvent)) {
                    this.onSelectScanDeviceEvent(msg);
                }
                break;
            case "getImageCount":
                this.imageCount = msg.imageCount;
                if (this.isCallbackExist(this.onGetImageCountEvent)) {
                    this.onGetImageCountEvent(msg);
                }
                break;
            case "getAllImage":
                this.imageCount = msg.imageCount;
                if (this.isCallbackExist(this.onGetAllImageEvent)) {
                    this.onGetAllImageEvent(msg);
                }
                break;
            case "getImageById":
                this.imageCount = msg.imageCount;
                if (this.isCallbackExist(this.onGetImageByIdEvent)) {
                    this.onGetImageByIdEvent(msg);
                }
                break;
            case "loadImageFromUrl":
                this.imageCount = msg.imageCount;
                if (this.isCallbackExist(this.onLoadImageFromUrlEvent)) {
                    this.onLoadImageFromUrlEvent(msg);
                }
                break;
            case "rotateImage":
                this.imageCount = msg.imageCount;
                if (this.isCallbackExist(this.onRotateImageEvent)) {
                    this.onRotateImageEvent(msg);
                }
                break;
            case "getImageSize":
                this.imageCount = msg.imageCount;
                if (this.isCallbackExist(this.onGetImageSizeEvent)) {
                    this.onGetImageSizeEvent(msg);
                }
                break;
            case "uploadAllImageAsPdfToUrl":
                if (this.isCallbackExist(this.onUploadAllImageAsPdfToUrlEvent)) {
                    this.onUploadAllImageAsPdfToUrlEvent(msg);
                }
                break;
            case "uploadAllImageAsTiffToUrl": 
                if (this.isCallbackExist(this.onUploadAllImageAsTiffToUrlEvent)) {
                    this.onUploadAllImageAsTiffToUrlEvent(msg);
                }
                break;
            case "uploadJpgImageByIndex":
                if (this.isCallbackExist(this.onUploadJpgImageByIndexEvent)) {
                    this.onUploadJpgImageByIndexEvent(msg);
                }
                break;
            case "upload":
                this.imageCount = msg.imageCount;
                if (this.isCallbackExist(this.onUploadEvent)) {
                    this.onUploadEvent(msg);
                }
                break;
            case "imageEdited":
                if (this.isCallbackExist(this.onImageEditedEvent)) {
                    this.onImageEditedEvent(msg);
                }
                break;
        }
    }

    isCallbackExist(f) {
        return typeof f === 'function';
    }

    sendWebSocketCommand(commandData) {
        if (this.h5socket && this.h5socket.readyState === 1) {
            this.h5socket.send(JSON.stringify(commandData));
        } else {
            console.log("WebSocket 连接未建立或已断开！发送扫描指令失败！请刷新页面或者检查托盘扫描程序是否已经正常运行!");
            //alert("发送扫描指令失败！请刷新页面或者检查托盘扫描程序是否已经正常运行!");
        }
    }

    setLicenseKey(licenseMode, key1, key2, licenseServerUrl) {
        const cmdObj = {
            cmd_type: "setLicenseKey",
            licenseMode,
            key1,
            key2,
            url: licenseServerUrl
        };
        this.sendWebSocketCommand(cmdObj);
    }

    loadDevices() {
        const cmdObj = { cmd_type: "getDevicesList" };
        this.sendWebSocketCommand(cmdObj);
    }

    selectScanDevice(deviceIndex) {
        const cmdObj = {
            cmd_type: "selectScanDevice",
            deviceIndex
        };
        this.sendWebSocketCommand(cmdObj);
    }

    startScan() {
        const cmdObj = {
            cmd_type: "startScan",
            config: this.scaner_work_config
        };
        this.sendWebSocketCommand(cmdObj);
    }

    clearAll() {
        const cmdObj = { cmd_type: "clearAll" };
        this.sendWebSocketCommand(cmdObj);
    }

    getImageCount() {
        const cmdObj = { cmd_type: "getImageCount" };
        this.sendWebSocketCommand(cmdObj);
    }

    getAllImage() {
        const cmdObj = { cmd_type: "getAllImage" };
        this.sendWebSocketCommand(cmdObj);
    }

    getImageById(index) {
        const cmdObj = {
            cmd_type: "getImageById",
            index
        };
        this.sendWebSocketCommand(cmdObj);
    }

    loadImageFromUrl(url) {
        const cmdObj = {
            cmd_type: "loadImageFromUrl",
            url
        };
        this.sendWebSocketCommand(cmdObj);
    }

    rotateImage(index, angle) {
        const cmdObj = {
            cmd_type: "rotateImage",
            index,
            angle
        };
        this.sendWebSocketCommand(cmdObj);
    }

    getImageSize(index) {
        const cmdObj = {
            cmd_type: "getImageSize",
            index
        };
        this.sendWebSocketCommand(cmdObj);
    }

    deleteImageByIndex(index) {
        const cmdObj = {
            cmd_type: "deleteImageByIndex",
            index
        };
        this.sendWebSocketCommand(cmdObj);
    }

    uploadAllImageAsPdfToUrl(url, id, desc) {
        const cmdObj = {
            cmd_type: "uploadAllImageAsPdfToUrl",
            url,
            id,
            desc
        };
        this.sendWebSocketCommand(cmdObj);
    }

    uploadAllImageAsTiffToUrl(url, id, desc) {
        const cmdObj = {
            cmd_type: "uploadAllImageAsTiffToUrl",
            url,
            id,
            desc
        };
        this.sendWebSocketCommand(cmdObj);
    }

    uploadJpgImageByIndex(url, id, desc, index) {
        const cmdObj = {
            cmd_type: "uploadJpgImageByIndex",
            index,
            url,
            id,
            desc
        };
        this.sendWebSocketCommand(cmdObj);
    }

    saveAllImageToLocal(filename) {
        const cmdObj = {
            cmd_type: "saveAllImageToLocal",
            filename
        };
        this.sendWebSocketCommand(cmdObj);
    }

    openClientLocalfile() {
        const cmdObj = { cmd_type: "openClientLocalfile" };
        this.sendWebSocketCommand(cmdObj);
    }

    ftpUploadAllImage(serverIp, port, username, password, serverPath, filename) {
        const cmdObj = {
            cmd_type: "ftpUploadAllImage",
            serverIp,
            port,
            username,
            password,
            serverPath,
            filename
        };
        this.sendWebSocketCommand(cmdObj);
    }

    setUploadButtonVisible(visible) {
        const cmdObj = {
            cmd_type: "setUploadButtonVisible",
            visible
        };
        this.sendWebSocketCommand(cmdObj);
    }

    setFocus() {
        const cmdObj = { cmd_type: "focus" };
        this.sendWebSocketCommand(cmdObj);
    }

    hidden() {
        const cmdObj = { cmd_type: "hidden" };
        this.sendWebSocketCommand(cmdObj);
    }

    closeWebSocket() {
        if (this.h5socket) {
            this.h5socket.close();
        }
    }
}

export default ScanOnWeb;
