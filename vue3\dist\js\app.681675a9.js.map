{"version": 3, "file": "js/app.681675a9.js", "mappings": "oHACOA,GAAG,O,wEAARC,EAAAA,EAAAA,IAEM,MAFNC,EAEM,EADJC,EAAAA,EAAAA,IAAaC,I,8ECAVC,MAAM,kB,GAGHA,MAAM,c,UAIVC,EAAAA,EAAAA,IAA2C,KAAxCD,MAAM,iBAAgB,kBAAc,K,GAiBhCA,MAAM,e,UAETC,EAAAA,EAAAA,IAAiB,YAAX,QAAI,K,GAiEHD,MAAM,4B,GACJA,MAAM,e,GASNA,MAAM,e,GAgFZA,MAAM,e,GAGJA,MAAM,kB,GA1LrBE,IAAA,EAgNsCF,MAAM,c,GAE7BA,MAAM,iB,EAlNrB,wB,GAyNiBA,MAAM,iB,GA0BRA,MAAM,c,GACHA,MAAM,e,EApPxB,kB,GAAAE,IAAA,EAqWSF,MAAM,qB,EArWf,c,GA6WWA,MAAM,kB,GACJA,MAAM,gB,GAGNA,MAAM,mB,woBAjXnBJ,EAAAA,EAAAA,IAAAO,EAAAA,GAAA,OAEEF,EAAAA,EAAAA,IAkTM,MAlTNJ,EAkTM,EAhTJC,EAAAA,EAAAA,IAMUM,EAAA,CANDJ,MAAM,eAAa,CAJhCK,SAAAC,EAAAA,EAAAA,KAKM,IAGK,EAHLL,EAAAA,EAAAA,IAGK,KAHLM,EAGK,EAFHT,EAAAA,EAAAA,IAA6BU,EAAA,MANrCH,SAAAC,EAAAA,EAAAA,KAMiB,IAAU,EAAVR,EAAAA,EAAAA,IAAUW,MAN3BC,EAAA,KAAAC,EAAAA,EAAAA,IAMqC,cAG/BC,KATNF,EAAA,IAcaG,EAAAC,aAdbC,EAAAA,EAAAA,IAAA,SAcwB,WADpBC,EAAAA,EAAAA,IAQEC,EAAA,CArBNf,IAAA,EAeMgB,MAAM,UACNC,YAAY,oBACZC,KAAK,UACJC,UAAU,EACX,eACArB,MAAM,uBAIRF,EAAAA,EAAAA,IAyFUM,EAAA,CAzFDJ,MAAM,eAAa,CACfsB,QAAMhB,EAAAA,EAAAA,KACf,IAGM,EAHNL,EAAAA,EAAAA,IAGM,MAHNsB,EAGM,EAFJzB,EAAAA,EAAAA,IAA8BU,EAAA,MA3BxCH,SAAAC,EAAAA,EAAAA,KA2BmB,IAAW,EAAXR,EAAAA,EAAAA,IAAW0B,MA3B9Bd,EAAA,IA4BUe,OA5BVpB,SAAAC,EAAAA,EAAAA,KAgCM,IAgFS,EAhFTR,EAAAA,EAAAA,IAgFS4B,EAAA,CAhFAC,OAAQ,IAAE,CAhCzBtB,SAAAC,EAAAA,EAAAA,KAkCQ,IAgBS,EAhBTR,EAAAA,EAAAA,IAgBS8B,EAAA,CAhBAC,KAAM,IAAE,CAlCzBxB,SAAAC,EAAAA,EAAAA,KAmCU,IAce,EAdfR,EAAAA,EAAAA,IAcegC,EAAA,CAdDC,MAAM,QAAM,CAnCpC1B,SAAAC,EAAAA,EAAAA,KAoCY,IAYY,EAZZR,EAAAA,EAAAA,IAYYkC,EAAA,CAhDxBC,WAqCuBpB,EAAAqB,eArCvB,sBAAAC,EAAA,KAAAA,EAAA,GAAAC,GAqCuBvB,EAAAqB,eAAcE,GACtBC,SAAQxB,EAAAyB,eACTC,YAAY,UACZC,MAAA,gB,CAxCdnC,SAAAC,EAAAA,EAAAA,KA2CgB,IAAkC,gBADpCV,EAAAA,EAAAA,IAKEO,EAAAA,GAAA,MA/ChBsC,EAAAA,EAAAA,IA2C0C5B,EAAA6B,SA3C1C,CA2CwBC,EAAQC,M,WADlB5B,EAAAA,EAAAA,IAKE6B,EAAA,CAHC3C,IAAK0C,EACLb,MAAOY,EACPG,MAAOF,G,sCA9CxBlC,EAAA,G,gCAAAA,EAAA,OAAAA,EAAA,KAqDQZ,EAAAA,EAAAA,IAQS8B,EAAA,CARAC,KAAM,IAAE,CArDzBxB,SAAAC,EAAAA,EAAAA,KAsDU,IAMe,EANfR,EAAAA,EAAAA,IAMegC,EAAA,CANDC,MAAM,QAAM,CAtDpC1B,SAAAC,EAAAA,EAAAA,KAuDY,IAIY,EAJZR,EAAAA,EAAAA,IAIYkC,EAAA,CA3DxBC,WAuDgCpB,EAAAkC,OAAOC,UAvDvC,sBAAAb,EAAA,KAAAA,EAAA,GAAAC,GAuDgCvB,EAAAkC,OAAOC,UAASZ,GAAEI,MAAA,gB,CAvDlDnC,SAAAC,EAAAA,EAAAA,KAwDc,IAAoC,EAApCR,EAAAA,EAAAA,IAAoC+C,EAAA,CAAzBd,MAAM,KAAKe,MAAM,SAC5BhD,EAAAA,EAAAA,IAAqC+C,EAAA,CAA1Bd,MAAM,KAAKe,MAAM,UAC5BhD,EAAAA,EAAAA,IAAmC+C,EAAA,CAAxBd,MAAM,KAAKe,MAAM,UA1D1CpC,EAAA,G,qBAAAA,EAAA,OAAAA,EAAA,KAgEQZ,EAAAA,EAAAA,IAwBS8B,EAAA,CAxBAC,KAAM,IAAE,CAhEzBxB,SAAAC,EAAAA,EAAAA,KAiEU,IAsBe,EAtBfR,EAAAA,EAAAA,IAsBegC,EAAA,CAtBDC,MAAM,aAAW,CAjEzC1B,SAAAC,EAAAA,EAAAA,KAkEY,IAoBS,EApBTR,EAAAA,EAAAA,IAoBS4B,EAAA,CApBAC,OAAQ,IAAE,CAlE/BtB,SAAAC,EAAAA,EAAAA,KAmEc,IAQS,EARTR,EAAAA,EAAAA,IAQS8B,EAAA,CARAC,KAAM,IAAE,CAnE/BxB,SAAAC,EAAAA,EAAAA,KAoEgB,IAME,EANFR,EAAAA,EAAAA,IAMEmD,EAAA,CA1ElBhB,WAqE2BpB,EAAAkC,OAAOG,MArElC,sBAAAf,EAAA,KAAAA,EAAA,GAAAC,GAqE2BvB,EAAAkC,OAAOG,MAAKd,GACpBe,IAAK,GACLC,IAAK,KACLC,KAAM,GACPb,MAAA,gB,0BAzElB9B,EAAA,KA4EcZ,EAAAA,EAAAA,IAAkD8B,EAAA,CAAzCC,KAAM,EAAG7B,MAAM,iB,CA5EtCK,SAAAC,EAAAA,EAAAA,KA4EsD,IAAC,EA5EvDK,EAAAA,EAAAA,IA4EsD,QA5EtDD,EAAA,KA6EcZ,EAAAA,EAAAA,IAQS8B,EAAA,CARAC,KAAM,IAAE,CA7E/BxB,SAAAC,EAAAA,EAAAA,KA8EgB,IAME,EANFR,EAAAA,EAAAA,IAMEmD,EAAA,CApFlBhB,WA+E2BpB,EAAAkC,OAAOO,MA/ElC,sBAAAnB,EAAA,KAAAA,EAAA,GAAAC,GA+E2BvB,EAAAkC,OAAOO,MAAKlB,GACpBe,IAAK,GACLC,IAAK,KACLC,KAAM,GACPb,MAAA,gB,0BAnFlB9B,EAAA,OAAAA,EAAA,OAAAA,EAAA,OAAAA,EAAA,KA2FQZ,EAAAA,EAAAA,IAoBS8B,EAAA,CApBAC,KAAM,IAAE,CA3FzBxB,SAAAC,EAAAA,EAAAA,KA4FU,IAkBe,EAlBfR,EAAAA,EAAAA,IAkBegC,EAAA,CAlBDC,MAAM,QAAM,CA5FpC1B,SAAAC,EAAAA,EAAAA,KA6FY,IAgBM,EAhBNL,EAAAA,EAAAA,IAgBM,MAhBNsD,EAgBM,EAfJtD,EAAAA,EAAAA,IAQM,MARNuD,EAQM,EAPJ1D,EAAAA,EAAAA,IAEC2D,EAAA,CAjGjBxB,WA+FsCpB,EAAAkC,OAAOW,WA/F7C,sBAAAvB,EAAA,KAAAA,EAAA,GAAAC,GA+FsCvB,EAAAkC,OAAOW,WAAUtB,I,CA/FvD/B,SAAAC,EAAAA,EAAAA,KAgGmB,IAAK,EAhGxBK,EAAAA,EAAAA,IAgGmB,YAhGnBD,EAAA,G,mBAkGgBZ,EAAAA,EAAAA,IAEC2D,EAAA,CApGjBxB,WAkGsCpB,EAAAkC,OAAOY,eAlG7C,sBAAAxB,EAAA,KAAAA,EAAA,GAAAC,GAkGsCvB,EAAAkC,OAAOY,eAAcvB,I,CAlG3D/B,SAAAC,EAAAA,EAAAA,KAmGmB,IAAI,EAnGvBK,EAAAA,EAAAA,IAmGmB,WAnGnBD,EAAA,G,mBAqGgBZ,EAAAA,EAAAA,IAAyD2D,EAAA,CArGzExB,WAqGsCpB,EAAAkC,OAAOa,SArG7C,sBAAAzB,EAAA,KAAAA,EAAA,GAAAC,GAqGsCvB,EAAAkC,OAAOa,SAAQxB,I,CArGrD/B,SAAAC,EAAAA,EAAAA,KAqGuD,IAAI,EArG3DK,EAAAA,EAAAA,IAqGuD,WArGvDD,EAAA,G,qBAuGcT,EAAAA,EAAAA,IAKM,MALN4D,EAKM,EAJJ/D,EAAAA,EAAAA,IAA2D2D,EAAA,CAxG3ExB,WAwGsCpB,EAAAkC,OAAOe,WAxG7C,sBAAA3B,EAAA,KAAAA,EAAA,GAAAC,GAwGsCvB,EAAAkC,OAAOe,WAAU1B,I,CAxGvD/B,SAAAC,EAAAA,EAAAA,KAwGyD,IAAI,EAxG7DK,EAAAA,EAAAA,IAwGyD,WAxGzDD,EAAA,G,mBAyGgBZ,EAAAA,EAAAA,IAEC2D,EAAA,CA3GjBxB,WAyGsCpB,EAAAkC,OAAOgB,oBAzG7C,sBAAA5B,EAAA,KAAAA,EAAA,GAAAC,GAyGsCvB,EAAAkC,OAAOgB,oBAAmB3B,I,CAzGhE/B,SAAAC,EAAAA,EAAAA,KA0GmB,IAAI,EA1GvBK,EAAAA,EAAAA,IA0GmB,WA1GnBD,EAAA,G,yBAAAA,EAAA,OAAAA,EAAA,OAAAA,EAAA,OAAAA,EAAA,KAoHIZ,EAAAA,EAAAA,IA8DUM,EAAA,CA9DDJ,MAAM,eAAa,CApHhCK,SAAAC,EAAAA,EAAAA,KAqHM,IA4DS,EA5DTR,EAAAA,EAAAA,IA4DS4B,EAAA,CA5DAC,OAAQ,IAAE,CArHzBtB,SAAAC,EAAAA,EAAAA,KAsHQ,IAUS,EAVTR,EAAAA,EAAAA,IAUS8B,EAAA,CAVAC,KAAM,GAAC,CAtHxBxB,SAAAC,EAAAA,EAAAA,KAuHU,IAQY,EARZR,EAAAA,EAAAA,IAQYkE,EAAA,CAPV5C,KAAK,UACJ6C,QAAOpD,EAAAqD,YACPC,QAAStD,EAAAsD,QAAQzB,QAClBF,MAAA,gB,CA3HZnC,SAAAC,EAAAA,EAAAA,KA6HY,IAA8B,EAA9BR,EAAAA,EAAAA,IAA8BU,EAAA,MA7H1CH,SAAAC,EAAAA,EAAAA,KA6HqB,IAAW,EAAXR,EAAAA,EAAAA,IAAWsE,MA7HhC1D,EAAA,KAAAC,EAAAA,EAAAA,IA6H0C,aA7H1CD,EAAA,G,4BAAAA,EAAA,KAiIQZ,EAAAA,EAAAA,IAWS8B,EAAA,CAXAC,KAAM,GAAC,CAjIxBxB,SAAAC,EAAAA,EAAAA,KAkIU,IASY,EATZR,EAAAA,EAAAA,IASYkE,EAAA,CARV5C,KAAK,UACJ6C,QAAOpD,EAAAwD,UACPF,QAAStD,EAAAsD,QAAQG,KACjBC,UAA8B,IAApB1D,EAAAqB,iBAA0BrB,EAAAC,YACrC0B,MAAA,gB,CAvIZnC,SAAAC,EAAAA,EAAAA,KAyIY,IAA6B,EAA7BR,EAAAA,EAAAA,IAA6BU,EAAA,MAzIzCH,SAAAC,EAAAA,EAAAA,KAyIqB,IAAU,EAAVR,EAAAA,EAAAA,IAAUW,MAzI/BC,EAAA,KAAAC,EAAAA,EAAAA,IAyIyC,aAzIzCD,EAAA,G,uCAAAA,EAAA,KA6IQZ,EAAAA,EAAAA,IAUS8B,EAAA,CAVAC,KAAM,GAAC,CA7IxBxB,SAAAC,EAAAA,EAAAA,KA8IU,IAQY,EARZR,EAAAA,EAAAA,IAQYkE,EAAA,CAPV5C,KAAK,OACJ6C,QAAOpD,EAAA2D,YACPL,QAAStD,EAAAsD,QAAQM,OAClBjC,MAAA,gB,CAlJZnC,SAAAC,EAAAA,EAAAA,KAoJY,IAA8B,EAA9BR,EAAAA,EAAAA,IAA8BU,EAAA,MApJ1CH,SAAAC,EAAAA,EAAAA,KAoJqB,IAAW,EAAXR,EAAAA,EAAAA,IAAW4E,MApJhChE,EAAA,KAAAC,EAAAA,EAAAA,IAoJ0C,aApJ1CD,EAAA,G,4BAAAA,EAAA,KAwJQZ,EAAAA,EAAAA,IAIS8B,EAAA,CAJAC,KAAM,GAAC,CAxJxBxB,SAAAC,EAAAA,EAAAA,KAyJU,IAEY,EAFZR,EAAAA,EAAAA,IAEYkE,EAAA,CAFD5C,KAAK,UAAW6C,QAAOpD,EAAA8D,SAAUnC,MAAA,gB,CAzJtDnC,SAAAC,EAAAA,EAAAA,KAyJ0E,IAEhE,EA3JVK,EAAAA,EAAAA,IAyJ0E,iBAzJ1ED,EAAA,G,kBAAAA,EAAA,KA6JQZ,EAAAA,EAAAA,IASS8B,EAAA,CATAC,KAAM,GAAC,CA7JxBxB,SAAAC,EAAAA,EAAAA,KA8JU,IAOY,EAPZR,EAAAA,EAAAA,IAOYkE,EAAA,CANV5C,KAAK,UACJ6C,QAAOpD,EAAA+D,iBACPL,SAA4B,IAAlB1D,EAAA4D,OAAOI,OAClBrC,MAAA,gB,CAlKZnC,SAAAC,EAAAA,EAAAA,KAmKW,IAED,EArKVK,EAAAA,EAAAA,IAmKW,gBAnKXD,EAAA,G,6BAAAA,EAAA,KAuKQZ,EAAAA,EAAAA,IASS8B,EAAA,CATAC,KAAM,GAAC,CAvKxBxB,SAAAC,EAAAA,EAAAA,KAwKU,IAOY,EAPZR,EAAAA,EAAAA,IAOYkE,EAAA,CANV5C,KAAK,UACJ6C,QAAOpD,EAAAiE,OACPP,SAA4B,IAAlB1D,EAAA4D,OAAOI,OAClBrC,MAAA,gB,CA5KZnC,SAAAC,EAAAA,EAAAA,KA6KW,IAED,EA/KVK,EAAAA,EAAAA,IA6KW,gBA7KXD,EAAA,G,6BAAAA,EAAA,OAAAA,EAAA,OAAAA,EAAA,IAqLuCG,EAAA4D,OAAOI,OAAS,IAAH,WAAhD7D,EAAAA,EAAAA,IAmHUZ,EAAA,CAxSdF,IAAA,EAqLaF,MAAM,e,CACFsB,QAAMhB,EAAAA,EAAAA,KACf,IAqBM,EArBNL,EAAAA,EAAAA,IAqBM,MArBN8E,EAqBM,EApBJjF,EAAAA,EAAAA,IAA8BU,EAAA,MAxLxCH,SAAAC,EAAAA,EAAAA,KAwLmB,IAAW,EAAXR,EAAAA,EAAAA,IAAW4E,MAxL9BhE,EAAA,KAyLUT,EAAAA,EAAAA,IAA2C,YAArC,UAAM+E,EAAAA,EAAAA,IAAGnE,EAAA4D,OAAOI,QAAS,QAAK,IACpC5E,EAAAA,EAAAA,IAiBM,MAjBNgF,EAiBM,EAhBJnF,EAAAA,EAAAA,IAekBoF,EAAA,MA1M9B7E,SAAAC,EAAAA,EAAAA,KA4Lc,IAMY,EANZR,EAAAA,EAAAA,IAMYkE,EAAA,CALT5C,KAAmB,SAAbP,EAAAsE,SAAsB,UAAY,UACxClB,QAAK9B,EAAA,KAAAA,EAAA,GAAAC,GAAEvB,EAAAsE,SAAW,QACnBC,KAAK,S,CA/LrB/E,SAAAC,EAAAA,EAAAA,KAgMe,IAED,EAlMdK,EAAAA,EAAAA,IAgMe,cAhMfD,EAAA,G,aAmMcZ,EAAAA,EAAAA,IAMYkE,EAAA,CALT5C,KAAmB,SAAbP,EAAAsE,SAAsB,UAAY,UACxClB,QAAK9B,EAAA,MAAAA,EAAA,IAAAC,GAAEvB,EAAAsE,SAAW,QACnBC,KAAK,S,CAtMrB/E,SAAAC,EAAAA,EAAAA,KAuMe,IAED,EAzMdK,EAAAA,EAAAA,IAuMe,cAvMfD,EAAA,G,eAAAA,EAAA,WAAAL,SAAAC,EAAAA,EAAAA,KAgNM,IAuCM,CAvCkB,SAAbO,EAAAsE,WAAQ,WAAnBvF,EAAAA,EAAAA,IAuCM,MAvCNyF,EAuCM,gBAtCJzF,EAAAA,EAAAA,IAqCMO,EAAAA,GAAA,MAtPdsC,EAAAA,EAAAA,IAiNsC5B,EAAA4D,QAjNtC,CAiNqBa,EAAO1C,M,WAApBhD,EAAAA,EAAAA,IAqCM,OArCiCM,IAAK0C,EAAO5C,MAAM,c,EACvDC,EAAAA,EAAAA,IAgCM,MAhCNsF,EAgCM,EA/BJtF,EAAAA,EAAAA,IAKE,OAJCuF,IAAKF,EAAME,IACXC,IAAG,QAAU7C,EAAQ,IACrBqB,QAAK7B,GAAEvB,EAAA6E,aAAa9C,GACrB5C,MAAM,c,OAvNpB2F,IAyNY1F,EAAAA,EAAAA,IAwBM,MAxBN2F,EAwBM,EAvBJ9F,EAAAA,EAAAA,IAsBkBoF,EAAA,MAhPhC7E,SAAAC,EAAAA,EAAAA,KA2NgB,IAMY,EANZR,EAAAA,EAAAA,IAMYkE,EAAA,CALV5C,KAAK,UACLgE,KAAK,QACJnB,QAAK7B,GAAEvB,EAAA6E,aAAa9C,I,CA9NvCvC,SAAAC,EAAAA,EAAAA,KA+NiB,IAED,EAjOhBK,EAAAA,EAAAA,IA+NiB,YA/NjBD,EAAA,G,mBAkOgBZ,EAAAA,EAAAA,IAMYkE,EAAA,CALV5C,KAAK,UACLgE,KAAK,QACJnB,QAAK7B,GAAEvB,EAAAgF,cAAcjD,I,CArOxCvC,SAAAC,EAAAA,EAAAA,KAsOiB,IAED,EAxOhBK,EAAAA,EAAAA,IAsOiB,WAtOjBD,EAAA,G,mBAyOgBZ,EAAAA,EAAAA,IAMYkE,EAAA,CALV5C,KAAK,SACLgE,KAAK,QACJnB,QAAK7B,GAAEvB,EAAAiF,YAAYlD,I,CA5OtCvC,SAAAC,EAAAA,EAAAA,KA6OiB,IAED,EA/OhBK,EAAAA,EAAAA,IA6OiB,YA7OjBD,EAAA,G,qBAAAA,EAAA,G,WAmPUT,EAAAA,EAAAA,IAEM,MAFN8F,EAEM,EADJ9F,EAAAA,EAAAA,IAAmD,OAAnD+F,EAA0B,OAAGhB,EAAAA,EAAAA,IAAGpC,EAAQ,GAAH,U,sBAM3C5B,EAAAA,EAAAA,IA6CWiF,EAAA,CAvSjB/F,IAAA,EA0PwBgG,KAAMrF,EAAA4D,OAAQ0B,OAAA,I,CA1PtC9F,SAAAC,EAAAA,EAAAA,KA2PQ,IAQkB,EARlBR,EAAAA,EAAAA,IAQkBsG,EAAA,CARDrE,MAAM,KAAKsE,MAAM,O,CACrBhG,SAAOC,EAAAA,EAAAA,KAChB,EADoBgG,MAAKC,YAAM,EAC/BtG,EAAAA,EAAAA,IAIE,OAHCuF,IAAKc,EAAId,IACVxF,MAAM,kBACLiE,QAAK7B,GAAEvB,EAAA6E,aAAaa,I,OAhQnCC,MAAA9F,EAAA,KAoQQZ,EAAAA,EAAAA,IAEkBsG,EAAA,CAFDrE,MAAM,OAAO0E,KAAK,QAAQJ,MAAM,O,CACpChG,SAAOC,EAAAA,EAAAA,KAAc,EAAViG,YAAM,EArQtC5F,EAAAA,EAAAA,IAqQ0C,QAAIqE,EAAAA,EAAAA,IAAGuB,EAAS,GAAH,MArQvD7F,EAAA,KAuQQZ,EAAAA,EAAAA,IAIkBsG,EAAA,CAJDrE,MAAM,KAAKsE,MAAM,O,CACrBhG,SAAOC,EAAAA,EAAAA,KAChB,IAAiC,EAAjCR,EAAAA,EAAAA,IAAiC4G,EAAA,CAAzBtF,KAAK,QAAM,CAzQ/Bf,SAAAC,EAAAA,EAAAA,KAyQgC,IAAI,EAzQpCK,EAAAA,EAAAA,IAyQgC,WAzQhCD,EAAA,OAAAA,EAAA,KA4QQZ,EAAAA,EAAAA,IA0BkBsG,EAAA,CA1BDrE,MAAM,KAAKsE,MAAM,O,CACrBhG,SAAOC,EAAAA,EAAAA,KAChB,EADoBiG,YAAM,EAC1BzG,EAAAA,EAAAA,IAsBkBoF,EAAA,MApS9B7E,SAAAC,EAAAA,EAAAA,KA+Qc,IAMY,EANZR,EAAAA,EAAAA,IAMYkE,EAAA,CALV5C,KAAK,UACLgE,KAAK,QACJnB,QAAK7B,GAAEvB,EAAA6E,aAAaa,I,CAlRrClG,SAAAC,EAAAA,EAAAA,KAmRe,IAED,EArRdK,EAAAA,EAAAA,IAmRe,eAnRfD,EAAA,G,mBAsRcZ,EAAAA,EAAAA,IAMYkE,EAAA,CALV5C,KAAK,UACLgE,KAAK,QACJnB,QAAK7B,GAAEvB,EAAAgF,cAAcU,I,CAzRtClG,SAAAC,EAAAA,EAAAA,KA0Re,IAED,EA5RdK,EAAAA,EAAAA,IA0Re,cA1RfD,EAAA,G,mBA6RcZ,EAAAA,EAAAA,IAMYkE,EAAA,CALV5C,KAAK,SACLgE,KAAK,QACJnB,QAAK7B,GAAEvB,EAAAiF,YAAYS,I,CAhSpClG,SAAAC,EAAAA,EAAAA,KAiSe,IAED,EAnSdK,EAAAA,EAAAA,IAiSe,eAjSfD,EAAA,G,qBAAAA,EAAA,G,SAAAA,EAAA,OAAAA,EAAA,G,gBAAAA,EAAA,O,WA2SIM,EAAAA,EAAAA,IAQW2F,EAAA,CAnTfzG,IAAA,EA2SqBiB,YAAY,SAASnB,MAAM,e,CA3ShDK,SAAAC,EAAAA,EAAAA,KA4SM,IAMY,EANZR,EAAAA,EAAAA,IAMYkE,EAAA,CALV5C,KAAK,UACJ6C,QAAOpD,EAAAwD,UACPE,UAA8B,IAApB1D,EAAAqB,iBAA0BrB,EAAAC,a,CA/S7CT,SAAAC,EAAAA,EAAAA,KAgTO,IAED,EAlTNK,EAAAA,EAAAA,IAgTO,aAhTPD,EAAA,G,6BAAAA,EAAA,QAuTEZ,EAAAA,EAAAA,IAqCY8G,GAAA,CA5Vd3E,WAwTapB,EAAAgG,aAAaC,QAxT1B,sBAAA3E,EAAA,MAAAA,EAAA,IAAAC,GAwTavB,EAAAgG,aAAaC,QAAO1E,GAC7BlB,MAAM,OACNmF,MAAM,QACL,wBAAsB,G,CAuBZU,QAAMzG,EAAAA,EAAAA,KACf,IAA+D,EAA/DR,EAAAA,EAAAA,IAA+DkE,EAAA,CAAnDC,QAAK9B,EAAA,MAAAA,EAAA,IAAAC,GAAEvB,EAAAgG,aAAaC,SAAU,I,CAnVhDzG,SAAAC,EAAAA,EAAAA,KAmVuD,IAAE,EAnVzDK,EAAAA,EAAAA,IAmVuD,SAnVvDD,EAAA,KAoVMZ,EAAAA,EAAAA,IAMYkE,EAAA,CALV5C,KAAK,UACJ6C,QAAOpD,EAAAmG,cACP7C,QAAStD,EAAAsD,QAAQ8C,Q,CAvV1B5G,SAAAC,EAAAA,EAAAA,KAwVO,IAED,EA1VNK,EAAAA,EAAAA,IAwVO,aAxVPD,EAAA,G,4BAAAL,SAAAC,EAAAA,EAAAA,KA6TI,IAmBU,EAnBVR,EAAAA,EAAAA,IAmBUoH,EAAA,CAnBAC,MAAOtG,EAAAgG,aAAaO,KAAM,cAAY,S,CA7TpD/G,SAAAC,EAAAA,EAAAA,KA8TM,IAEe,EAFfR,EAAAA,EAAAA,IAEegC,EAAA,CAFDC,MAAM,QAAM,CA9ThC1B,SAAAC,EAAAA,EAAAA,KA+TQ,IAAiE,EAAjER,EAAAA,EAAAA,IAAiEuH,EAAA,CA/TzEpF,WA+T2BpB,EAAAgG,aAAaO,KAAKzH,GA/T7C,sBAAAwC,EAAA,MAAAA,EAAA,IAAAC,GA+T2BvB,EAAAgG,aAAaO,KAAKzH,GAAEyC,GAAEG,YAAY,W,0BA/T7D7B,EAAA,KAiUMZ,EAAAA,EAAAA,IAOegC,EAAA,CAPDC,MAAM,QAAM,CAjUhC1B,SAAAC,EAAAA,EAAAA,KAkUQ,IAKE,EALFR,EAAAA,EAAAA,IAKEuH,EAAA,CAvUVpF,WAmUmBpB,EAAAgG,aAAaO,KAAKjG,YAnUrC,sBAAAgB,EAAA,MAAAA,EAAA,IAAAC,GAmUmBvB,EAAAgG,aAAaO,KAAKjG,YAAWiB,GACtChB,KAAK,WACJkG,KAAM,EACP/E,YAAY,W,0BAtUtB7B,EAAA,KAyUMZ,EAAAA,EAAAA,IAMegC,EAAA,CANDC,MAAM,QAAM,CAzUhC1B,SAAAC,EAAAA,EAAAA,KA0UQ,IAIiB,EAJjBR,EAAAA,EAAAA,IAIiByH,EAAA,CA9UzBtF,WA0UiCpB,EAAAgG,aAAaO,KAAKI,OA1UnD,sBAAArF,EAAA,MAAAA,EAAA,IAAAC,GA0UiCvB,EAAAgG,aAAaO,KAAKI,OAAMpF,I,CA1UzD/B,SAAAC,EAAAA,EAAAA,KA2UU,IAAsC,EAAtCR,EAAAA,EAAAA,IAAsC2H,EAAA,CAA5B1F,MAAM,OAAK,CA3U/B1B,SAAAC,EAAAA,EAAAA,KA2UgC,IAAK,EA3UrCK,EAAAA,EAAAA,IA2UgC,YA3UhCD,EAAA,KA4UUZ,EAAAA,EAAAA,IAAwC2H,EAAA,CAA9B1F,MAAM,QAAM,CA5UhC1B,SAAAC,EAAAA,EAAAA,KA4UiC,IAAM,EA5UvCK,EAAAA,EAAAA,IA4UiC,aA5UjCD,EAAA,KA6UUZ,EAAAA,EAAAA,IAAsC2H,EAAA,CAA5B1F,MAAM,OAAK,CA7U/B1B,SAAAC,EAAAA,EAAAA,KA6UgC,IAAK,EA7UrCK,EAAAA,EAAAA,IA6UgC,YA7UhCD,EAAA,OAAAA,EAAA,G,qBAAAA,EAAA,OAAAA,EAAA,G,gBAAAA,EAAA,G,mBA+VEZ,EAAAA,EAAAA,IAsCY8G,GAAA,CArYd3E,WAgWapB,EAAA6G,cAAcZ,QAhW3B,sBAAA3E,EAAA,MAAAA,EAAA,IAAAC,GAgWavB,EAAA6G,cAAcZ,QAAO1E,GAC9BlB,MAAM,OACNmF,MAAM,MACL,wBAAsB,G,CASZU,QAAMzG,EAAAA,EAAAA,KACf,IAsBM,EAtBNL,EAAAA,EAAAA,IAsBM,MAtBN0H,EAsBM,EArBJ1H,EAAAA,EAAAA,IAEM,MAFN2H,EAA0B,QACrB5C,EAAAA,EAAAA,IAAGnE,EAAA6G,cAAcG,aAAe,GAAI,OAAG7C,EAAAA,EAAAA,IAAGnE,EAAA4D,OAAOI,QAAM,IAE5D5E,EAAAA,EAAAA,IAiBM,MAjBN6H,EAiBM,EAhBJhI,EAAAA,EAAAA,IAKYkE,EAAA,CAJTC,QAAOpD,EAAAkH,UACPxD,SAAU1D,EAAA6G,cAAcG,cAAgB,G,CApXrDxH,SAAAC,EAAAA,EAAAA,KAqXW,IAED,EAvXVK,EAAAA,EAAAA,IAqXW,eArXXD,EAAA,G,2BAwXUZ,EAAAA,EAAAA,IAKYkE,EAAA,CAJTC,QAAOpD,EAAAmH,UACPzD,SAAU1D,EAAA6G,cAAcG,cAAgBhH,EAAA4D,OAAOI,OAAS,G,CA1XrExE,SAAAC,EAAAA,EAAAA,KA2XW,IAED,EA7XVK,EAAAA,EAAAA,IA2XW,eA3XXD,EAAA,G,2BA8XUZ,EAAAA,EAAAA,IAEYkE,EAAA,CAFD5C,KAAK,UAAW6C,QAAOpD,EAAAoH,sB,CA9X5C5H,SAAAC,EAAAA,EAAAA,KA8XkE,IAExD,EAhYVK,EAAAA,EAAAA,IA8XkE,cA9XlED,EAAA,G,gBAiYUZ,EAAAA,EAAAA,IAAgEkE,EAAA,CAApDC,QAAK9B,EAAA,MAAAA,EAAA,IAAAC,GAAEvB,EAAA6G,cAAcZ,SAAU,I,CAjYrDzG,SAAAC,EAAAA,EAAAA,KAiY4D,IAAE,EAjY9DK,EAAAA,EAAAA,IAiY4D,SAjY5DD,EAAA,WAAAL,SAAAC,EAAAA,EAAAA,KAqWI,IAMM,CAN+BO,EAAA6G,cAAcQ,eAAY,WAA/DtI,EAAAA,EAAAA,IAMM,MANNuI,EAMM,EALJlI,EAAAA,EAAAA,IAIE,OAHCuF,IAAK3E,EAAA6G,cAAcQ,aAAa1C,IACjCxF,MAAM,gBACLyF,IAAG,QAAU5E,EAAA6G,cAAcG,aAAe,K,OAzWnDO,OAAArH,EAAAA,EAAAA,IAAA,UAAAL,EAAA,G,+KCIA,MAAM2H,EAAMC,EAAAA,EAAMC,OAAO,CACvBC,QAAS,wBACTC,QAAS,IACTC,QAAS,CACP,eAAgB,sBAKpBL,EAAIM,aAAaC,QAAQC,KACvB9F,IACE+F,QAAQC,IAAI,QAAShG,EAAOiG,QAAQC,cAAelG,EAAOmG,KACnDnG,KAEToG,IACEL,QAAQK,MAAM,QAASA,GAChBC,QAAQC,OAAOF,MAK1Bd,EAAIM,aAAaW,SAAST,KACxBS,IACER,QAAQC,IAAI,QAASO,EAASC,OAAQD,EAASvG,OAAOmG,KAC/CI,KAETH,IACEL,QAAQK,MAAM,QAASA,EAAMG,UAAUC,OAAQJ,EAAMK,SACtB,MAA3BL,EAAMG,UAAUC,OAClBT,QAAQK,MAAM,YACLA,EAAMG,UAAUC,QAAU,IACnCT,QAAQK,MAAM,WACU,iBAAfA,EAAMM,MACfX,QAAQK,MAAM,uBAETC,QAAQC,OAAOF,MAKnB,MAwDMO,EAAQ,CAEnBC,YAAAA,CAAaC,EAAQC,EAAW,cAC9B,MAAMC,EAAiBC,KAAKH,GACtBI,EAAc,IAAIC,MAAMH,EAAejF,QAC7C,IAAK,IAAIqF,EAAI,EAAGA,EAAIJ,EAAejF,OAAQqF,IACzCF,EAAYE,GAAKJ,EAAeK,WAAWD,GAE7C,MAAME,EAAY,IAAIC,WAAWL,GACjC,OAAO,IAAIM,KAAK,CAACF,GAAY,CAAEhJ,KAAMyI,GACvC,EAGAU,mBAAAA,CAAoBX,EAAQY,EAAW,aACrC,MAAMC,EAAOC,KAAKf,aAAaC,GACzBV,EAAMyB,IAAIC,gBAAgBH,GAC1BI,EAAOC,SAASC,cAAc,KACpCF,EAAKG,KAAO9B,EACZ2B,EAAKI,SAAWT,EAChBM,SAASI,KAAKC,YAAYN,GAC1BA,EAAKO,QACLN,SAASI,KAAKG,YAAYR,GAC1BF,IAAIW,gBAAgBpC,EACtB,EAGAqC,cAAAA,CAAeC,GACb,GAAc,IAAVA,EAAa,MAAO,UACxB,MAAMC,EAAI,KACJC,EAAQ,CAAC,QAAS,KAAM,KAAM,MAC9BxB,EAAIyB,KAAKC,MAAMD,KAAK5C,IAAIyC,GAASG,KAAK5C,IAAI0C,IAChD,OAAOI,YAAYL,EAAQG,KAAKG,IAAIL,EAAGvB,IAAI6B,QAAQ,IAAM,IAAML,EAAMxB,EACvE,EAGA8B,UAAAA,GACE,OAAOC,KAAKC,MAAMC,SAAS,IAAMR,KAAKS,SAASD,SAAS,IAAIE,OAAO,EACrE,GAGF,IDmQA,GACEC,KAAM,YACNC,WAAY,CACVC,OAAM,SACNC,QAAO,UACPC,QAAO,UACPC,QAAOA,EAAAA,SAETC,KAAAA,GAEE,MAAMC,GAAYC,EAAAA,EAAAA,IAAI,MAChBhM,GAAcgM,EAAAA,EAAAA,KAAI,GAClBpK,GAAUoK,EAAAA,EAAAA,IAAI,IACd5K,GAAiB4K,EAAAA,EAAAA,KAAK,GACtBrI,GAASqI,EAAAA,EAAAA,IAAI,IACb3H,GAAW2H,EAAAA,EAAAA,IAAI,QAGf/J,GAASgK,EAAAA,EAAAA,IAAS,CACtB7J,MAAO,IACPI,MAAO,IACPN,UAAW,MACXU,YAAY,EACZC,gBAAgB,EAChBqJ,UAAU,EACVpJ,UAAU,EACVE,YAAY,EACZC,qBAAqB,IAIjBI,GAAU4I,EAAAA,EAAAA,IAAS,CACvBrK,SAAS,EACT4B,MAAM,EACNG,QAAQ,EACRwC,QAAQ,IAIJJ,GAAekG,EAAAA,EAAAA,IAAS,CAC5BjG,SAAS,EACTM,KAAM,CACJzH,GAAI,GACJwB,YAAa,GACbqG,OAAQ,SAKNE,GAAgBqF,EAAAA,EAAAA,IAAS,CAC7BjG,SAAS,EACTe,aAAc,EACdK,aAAc,OAIV+E,EAAkBA,KACtB,IACEJ,EAAU/J,MAAQ,IAAIoK,KAGtBL,EAAU/J,MAAMqK,sBAAyBC,IACvC1K,EAAQI,MAAQsK,EAAI1K,SAAW,GAE3BA,EAAQI,MAAM+B,OAAS,GACzB3C,EAAeY,MAAQsK,EAAIvF,cAAgB,EAAIuF,EAAIvF,aAAe,EAE9D3F,EAAeY,OAAS,GAC1BuK,YAAW,KACT/K,EAAeJ,EAAeY,MAAM,GACnC,MAGLZ,EAAeY,OAAS,EAE1BqB,EAAQzB,SAAU,EAClB5B,EAAYgC,OAAQ,EACpBwK,EAAAA,GAAUC,QACP,MAAK7K,EAAQI,MAAM+B,eAClB3C,EAAeY,OAAS,EAAI,cAAgB,KAE/C,EAGH+J,EAAU/J,MAAM0K,oBAAuBJ,IACrCjJ,EAAQG,MAAO,EACfgJ,EAAAA,GAAUC,QAAS,YAAWH,EAAIK,uBAClCjJ,GAAa,EAGfqI,EAAU/J,MAAM4K,mBAAsBN,IACpCjJ,EAAQM,QAAS,EACb2I,EAAI3I,QAAU2I,EAAI3I,OAAOI,OAAS,GACpCJ,EAAO3B,MAAQsK,EAAI3I,OAAOkJ,KAAI,CAACrI,EAAO1C,KAAU,CAC9C4C,IAAM,yBAAwBF,IAC9B1C,MAAOA,EACPgH,OAAQtE,MAEVgI,EAAAA,GAAUC,QAAS,OAAM9I,EAAO3B,MAAM+B,eAEtCyI,EAAAA,GAAUM,KAAK,SACjB,EAGFf,EAAU/J,MAAM+K,oBAAuBT,IACjCA,EAAIU,aACNC,EAASX,EAAIU,YACf,EAGFjB,EAAU/J,MAAMkL,mBAAsBZ,IACpCE,EAAAA,GAAUM,KAAM,MAAKR,EAAIa,WAAa,SAClCb,EAAIU,aACNI,EAAUd,EAAIa,WAAYb,EAAIU,YAChC,EAGFjB,EAAU/J,MAAMqL,cAAgB,KAC9Bb,EAAAA,GAAUM,KAAK,aACfhJ,GAAkB,EAIpByI,YAAW,KACoB,IAAzB3K,EAAQI,MAAM+B,SAChB/D,EAAYgC,OAAQ,EACpBwK,EAAAA,GAAUc,QAAQ,sBACpB,GACC,IACL,CAAE,MAAOjF,GACPL,QAAQK,MAAM,aAAcA,GAC5BrI,EAAYgC,OAAQ,EACpBwK,EAAAA,GAAUnE,MAAM,YAClB,GAIIjF,EAAcmK,UAClB,GAAKxB,EAAU/J,MAAf,CAKAqB,EAAQzB,SAAU,EAClB,IACEmK,EAAU/J,MAAMoB,aAClB,CAAE,MAAOiF,GACPhF,EAAQzB,SAAU,EAClB4K,EAAAA,GAAUnE,MAAM,WAClB,CARA,MAFEmE,EAAAA,GAAUnE,MAAM,WAUlB,EAII7G,EAAkBgM,IAClBzB,EAAU/J,QACZ+J,EAAU/J,MAAMyL,iBAAiBD,GACjChB,EAAAA,GAAUM,KAAM,UAASlL,EAAQI,MAAMwL,MACzC,EAIIjK,EAAYgK,UAChB,GAAKxB,EAAU/J,MAKf,IAA8B,IAA1BZ,EAAeY,MAAnB,CAKAqB,EAAQG,MAAO,EACf,IAEEuI,EAAU/J,MAAM0L,mBAAqB,IAChC3B,EAAU/J,MAAM0L,sBAChBzL,EACHuL,YAAapM,EAAeY,OAG9B+J,EAAU/J,MAAMuB,YAChBiJ,EAAAA,GAAUM,KAAK,UACjB,CAAE,MAAOzE,GACPhF,EAAQG,MAAO,EACfgJ,EAAAA,GAAUnE,MAAM,SAClB,CAhBA,MAFEmE,EAAAA,GAAUc,QAAQ,iBALlBd,EAAAA,GAAUnE,MAAM,WAuBlB,EAII3E,EAAc6J,UAClB,GAAKxB,EAAU/J,MAAf,CAKAqB,EAAQM,QAAS,EACjB,IACEoI,EAAU/J,MAAM0B,aAClB,CAAE,MAAO2E,GACPhF,EAAQM,QAAS,EACjB6I,EAAAA,GAAUnE,MAAM,SAClB,CARA,MAFEmE,EAAAA,GAAUnE,MAAM,WAUlB,EAIIxE,EAAW0J,UACf,UACQI,EAAAA,EAAaC,QAAQ,gBAAiB,OAAQ,CAClDC,kBAAmB,KACnBC,iBAAkB,KAClBxN,KAAM,YAGJyL,EAAU/J,QACZ+J,EAAU/J,MAAM6B,WAChBF,EAAO3B,MAAQ,GACfwK,EAAAA,GAAUC,QAAQ,aAEtB,CAAE,MACA,GAKEQ,EAAYD,IAChBrJ,EAAO3B,MAAM+L,KAAK,CAChBrJ,IAAM,yBAAwBsI,IAC9BlL,MAAO6B,EAAO3B,MAAM+B,OACpB+E,OAAQkE,GACR,EAIEI,EAAYA,CAACtL,EAAOkL,KACpBlL,GAAS,GAAKA,EAAQ6B,EAAO3B,MAAM+B,SACrCJ,EAAO3B,MAAMF,GAAS,CACpB4C,IAAM,yBAAwBsI,IAC9BlL,MAAOA,EACPgH,OAAQkE,GAEZ,EAIIlJ,EAAmBA,KACK,IAAxBH,EAAO3B,MAAM+B,QAKjBgC,EAAaO,KAAKzH,GAAK+J,EAAMsC,aAC7BnF,EAAaO,KAAKjG,YAAe,SAAO,IAAI8K,MAAO6C,mBACnDjI,EAAaC,SAAU,GANrBwG,EAAAA,GAAUc,QAAQ,WAMO,EAIvBpH,EAAgBqH,UACpB,GAAKxH,EAAaO,KAAKzH,GAAGoP,OAK1B,GAAKlC,EAAU/J,MAKf,GAA4B,IAAxB2B,EAAO3B,MAAM+B,OAAjB,CAKAV,EAAQ8C,QAAS,EACjB,IACE,MAAM,GAAEtH,EAAE,YAAEwB,EAAW,OAAEqG,GAAWX,EAAaO,KAC3C4H,EAAY,+BA+FlB,GA5FAnC,EAAU/J,MAAMmM,gCAAmC7B,IACjDtE,QAAQC,IAAI,WAAYqE,GACxBjJ,EAAQ8C,QAAS,EAEjB,IAEE,IAAIiI,EAAe,KAQnB,GAPI9B,EAAI8B,eACNA,EAC8B,kBAArB9B,EAAI8B,aACPC,KAAKC,MAAMhC,EAAI8B,cACf9B,EAAI8B,cAGRA,GAAgBA,EAAa3B,QAC/BD,EAAAA,GAAUC,QACP,eAAcH,EAAIiC,YAAc,SAEnCxI,EAAaC,SAAU,EACvBgC,QAAQC,IAAI,QAASmG,OAChB,CACL,MAAMI,EACJJ,GAAc1F,SAAW4D,EAAI5D,SAAW,OAC1C8D,EAAAA,GAAUnE,MAAO,YAAWmG,IAC9B,CACF,CAAE,MAAOnG,GACPL,QAAQK,MAAM,YAAaA,EAAOiE,GAClCE,EAAAA,GAAUnE,MAAO,kBACnB,GAGF0D,EAAU/J,MAAMyM,iCAAoCnC,IAClDtE,QAAQC,IAAI,YAAaqE,GACzBjJ,EAAQ8C,QAAS,EAEjB,IAEE,IAAIiI,EAAe,KAQnB,GAPI9B,EAAI8B,eACNA,EAC8B,kBAArB9B,EAAI8B,aACPC,KAAKC,MAAMhC,EAAI8B,cACf9B,EAAI8B,cAGRA,GAAgBA,EAAa3B,QAC/BD,EAAAA,GAAUC,QACP,gBAAeH,EAAIiC,YAAc,SAEpCxI,EAAaC,SAAU,EACvBgC,QAAQC,IAAI,QAASmG,OAChB,CACL,MAAMI,EACJJ,GAAc1F,SAAW4D,EAAI5D,SAAW,OAC1C8D,EAAAA,GAAUnE,MAAO,aAAYmG,IAC/B,CACF,CAAE,MAAOnG,GACPL,QAAQK,MAAM,YAAaA,EAAOiE,GAClCE,EAAAA,GAAUnE,MAAO,mBACnB,GAGF0D,EAAU/J,MAAM0M,6BAAgCpC,IAC9CtE,QAAQC,IAAI,WAAYqE,GACxBjJ,EAAQ8C,QAAS,EAEjB,IAEE,IAAIiI,EAAe,KAQnB,GAPI9B,EAAI8B,eACNA,EAC8B,kBAArB9B,EAAI8B,aACPC,KAAKC,MAAMhC,EAAI8B,cACf9B,EAAI8B,cAGRA,GAAgBA,EAAa3B,QAC/BD,EAAAA,GAAUC,QAAS,cACnB1G,EAAaC,SAAU,EACvBgC,QAAQC,IAAI,QAASmG,OAChB,CACL,MAAMI,EACJJ,GAAc1F,SAAW4D,EAAI5D,SAAW,OAC1C8D,EAAAA,GAAUnE,MAAO,YAAWmG,IAC9B,CACF,CAAE,MAAOnG,GACPL,QAAQK,MAAM,YAAaA,EAAOiE,GAClCE,EAAAA,GAAUnE,MAAO,kBACnB,GAIa,QAAX3B,EACF8F,EAAAA,GAAUM,KAAK,gBACff,EAAU/J,MAAM2M,yBAAyBT,EAAWrP,EAAIwB,QACnD,GAAe,SAAXqG,EAAmB,CAC5B8F,EAAAA,GAAUM,KAAK,iBACf,MAAM8B,EAAgB,oCACtB7C,EAAU/J,MAAM6M,0BACdD,EACA/P,EACAwB,EAEJ,MAAO,GAAe,QAAXqG,EAAkB,CAC3B8F,EAAAA,GAAUM,KAAK,gBACf,MAAMgC,EAAe,mCAErB/C,EAAU/J,MAAM+M,sBACdD,EACAjQ,EACAwB,EACA,EAEJ,CACF,CAAE,MAAOgI,GACPhF,EAAQ8C,QAAS,EACjB6B,QAAQK,MAAM,QAASA,GACvBmE,EAAAA,GAAUnE,MAAO,SAAQA,EAAMK,SAAW,SAC5C,CA9HA,MAFE8D,EAAAA,GAAUc,QAAQ,iBALlBd,EAAAA,GAAUnE,MAAM,iBALhBmE,EAAAA,GAAUc,QAAQ,UA0IpB,EAII1I,EAAgB9C,IAChBA,GAAS,GAAKA,EAAQ6B,EAAO3B,MAAM+B,SACrC6C,EAAcG,aAAejF,EAC7B8E,EAAcQ,aAAezD,EAAO3B,MAAMF,GAC1C8E,EAAcZ,SAAU,EAC1B,EAIIiB,EAAYA,KACZL,EAAcG,aAAe,IAC/BH,EAAcG,eACdH,EAAcQ,aAAezD,EAAO3B,MAAM4E,EAAcG,cAC1D,EAIIG,EAAYA,KACZN,EAAcG,aAAepD,EAAO3B,MAAM+B,OAAS,IACrD6C,EAAcG,eACdH,EAAcQ,aAAezD,EAAO3B,MAAM4E,EAAcG,cAC1D,EAIIhC,EAAiBjD,IACrB,GAAIA,GAAS,GAAKA,EAAQ6B,EAAO3B,MAAM+B,OAAQ,CAC7C,MAAMS,EAAQb,EAAO3B,MAAMF,GACrB4H,EAAY,cAAa5H,EAAQ,QACvC8G,EAAMa,oBAAoBjF,EAAMsE,OAAQY,GACxC8C,EAAAA,GAAUC,QAAS,MAAK3K,EAAQ,SAClC,GAIIqF,EAAuBA,KAC3BpC,EAAc6B,EAAcG,aAAa,EAIrC/B,EAAcuI,UAClB,UACQI,EAAAA,EAAaC,QAChB,WAAU9L,EAAQ,OACnB,OACA,CACE+L,kBAAmB,KACnBC,iBAAkB,KAClBxN,KAAM,YAIVqD,EAAO3B,MAAMgN,OAAOlN,EAAO,GAE3B6B,EAAO3B,MAAMiN,SAAQ,CAACC,EAAKC,KACzBD,EAAIpN,MAAQqN,CAAG,IAGjB3C,EAAAA,GAAUC,QAAQ,SACpB,CAAE,MACA,GAKEzI,EAASA,KACb,GAAK+H,EAAU/J,MAKf,GAA4B,IAAxB2B,EAAO3B,MAAM+B,OAKjB,IACE,MAAM2F,EAAY,WAAUyB,KAAKC,YACjCW,EAAU/J,MAAMoN,oBAAoB1F,GACpC8C,EAAAA,GAAUC,QAAQ,SACpB,CAAE,MAAOpE,GACPmE,EAAAA,GAAUnE,MAAM,OAClB,MAVEmE,EAAAA,GAAUc,QAAQ,iBALlBd,EAAAA,GAAUnE,MAAM,WAelB,EAoBF,OAhBAgH,EAAAA,EAAAA,KAAU,KACRlD,IAEAI,YAAW,KACTnJ,GAAa,GACZ,IAAK,KAGVkM,EAAAA,EAAAA,KAAY,KAENvD,EAAU/J,OAAS+J,EAAU/J,MAAMuN,UACrCxD,EAAU/J,MAAMuN,SAASC,OAC3B,IAIK,CAELxP,cACA4B,UACAR,iBACAuC,SACAU,WACApC,SACAoB,UACA0C,eACAa,gBAGAxD,cACA5B,iBACA+B,YACAG,cACAG,WACAC,mBACAoC,gBACAtB,eACAqC,YACAC,YACAnC,gBACAoC,uBACAnC,cACAhB,SAEJ,G,UE35BF,MAAMyL,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS,GAAQ,CAAC,YAAY,qBAEzF,QHAA,GACEjE,KAAM,MACNC,WAAY,CACVW,UAASA,IILb,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASsD,KAEpE,QCHA,MAAMC,GAAMC,EAAAA,EAAAA,IAAUC,GAGtBF,EAAI5H,IAAI+H,EAAAA,GAGR,IAAK,MAAO1Q,EAAK2Q,KAAcC,OAAOC,QAAQC,GAC5CP,EAAII,UAAU3Q,EAAK2Q,GAGrBJ,EAAIQ,MAAM,O,GCfNC,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAaE,QAGrB,IAAIC,EAASN,EAAyBE,GAAY,CAGjDG,QAAS,CAAC,GAOX,OAHAE,EAAoBL,GAAUM,KAAKF,EAAOD,QAASC,EAAQA,EAAOD,QAASJ,GAGpEK,EAAOD,OACf,CAGAJ,EAAoBQ,EAAIF,E,WCzBxB,IAAIG,EAAW,GACfT,EAAoBU,EAAI,SAASC,EAAQC,EAAUC,EAAIC,GACtD,IAAGF,EAAH,CAMA,IAAIG,EAAeC,IACnB,IAASjI,EAAI,EAAGA,EAAI0H,EAAS/M,OAAQqF,IAAK,CACrC6H,EAAWH,EAAS1H,GAAG,GACvB8H,EAAKJ,EAAS1H,GAAG,GACjB+H,EAAWL,EAAS1H,GAAG,GAE3B,IAJA,IAGIkI,GAAY,EACPC,EAAI,EAAGA,EAAIN,EAASlN,OAAQwN,MACpB,EAAXJ,GAAsBC,GAAgBD,IAAanB,OAAOwB,KAAKnB,EAAoBU,GAAGU,OAAM,SAASrS,GAAO,OAAOiR,EAAoBU,EAAE3R,GAAK6R,EAASM,GAAK,IAChKN,EAASjC,OAAOuC,IAAK,IAErBD,GAAY,EACTH,EAAWC,IAAcA,EAAeD,IAG7C,GAAGG,EAAW,CACbR,EAAS9B,OAAO5F,IAAK,GACrB,IAAIsI,EAAIR,SACEV,IAANkB,IAAiBV,EAASU,EAC/B,CACD,CACA,OAAOV,CArBP,CAJCG,EAAWA,GAAY,EACvB,IAAI,IAAI/H,EAAI0H,EAAS/M,OAAQqF,EAAI,GAAK0H,EAAS1H,EAAI,GAAG,GAAK+H,EAAU/H,IAAK0H,EAAS1H,GAAK0H,EAAS1H,EAAI,GACrG0H,EAAS1H,GAAK,CAAC6H,EAAUC,EAAIC,EAwB/B,C,eC5BAd,EAAoBsB,EAAI,SAASjB,GAChC,IAAIkB,EAASlB,GAAUA,EAAOmB,WAC7B,WAAa,OAAOnB,EAAO,UAAY,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAL,EAAoByB,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,CACR,C,eCNAvB,EAAoByB,EAAI,SAASrB,EAASuB,GACzC,IAAI,IAAI5S,KAAO4S,EACX3B,EAAoB4B,EAAED,EAAY5S,KAASiR,EAAoB4B,EAAExB,EAASrR,IAC5E4Q,OAAOkC,eAAezB,EAASrR,EAAK,CAAE+S,YAAY,EAAMC,IAAKJ,EAAW5S,IAG3E,C,eCPAiR,EAAoBgC,EAAI,WACvB,GAA0B,kBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAO1I,MAAQ,IAAI2I,SAAS,cAAb,EAChB,CAAE,MAAOC,GACR,GAAsB,kBAAXC,OAAqB,OAAOA,MACxC,CACA,CAPuB,E,eCAxBpC,EAAoB4B,EAAI,SAASS,EAAK/M,GAAQ,OAAOqK,OAAO2C,UAAUC,eAAehC,KAAK8B,EAAK/M,EAAO,C,eCCtG0K,EAAoBqB,EAAI,SAASjB,GACX,qBAAXoC,QAA0BA,OAAOC,aAC1C9C,OAAOkC,eAAezB,EAASoC,OAAOC,YAAa,CAAE9Q,MAAO,WAE7DgO,OAAOkC,eAAezB,EAAS,aAAc,CAAEzO,OAAO,GACvD,C,eCDA,IAAI+Q,EAAkB,CACrB,IAAK,GAaN1C,EAAoBU,EAAEQ,EAAI,SAASyB,GAAW,OAAoC,IAA7BD,EAAgBC,EAAgB,EAGrF,IAAIC,EAAuB,SAASC,EAA4B9N,GAC/D,IAKIkL,EAAU0C,EALV/B,EAAW7L,EAAK,GAChB+N,EAAc/N,EAAK,GACnBgO,EAAUhO,EAAK,GAGIgE,EAAI,EAC3B,GAAG6H,EAASoC,MAAK,SAASxU,GAAM,OAA+B,IAAxBkU,EAAgBlU,EAAW,IAAI,CACrE,IAAIyR,KAAY6C,EACZ9C,EAAoB4B,EAAEkB,EAAa7C,KACrCD,EAAoBQ,EAAEP,GAAY6C,EAAY7C,IAGhD,GAAG8C,EAAS,IAAIpC,EAASoC,EAAQ/C,EAClC,CAEA,IADG6C,GAA4BA,EAA2B9N,GACrDgE,EAAI6H,EAASlN,OAAQqF,IACzB4J,EAAU/B,EAAS7H,GAChBiH,EAAoB4B,EAAEc,EAAiBC,IAAYD,EAAgBC,IACrED,EAAgBC,GAAS,KAE1BD,EAAgBC,GAAW,EAE5B,OAAO3C,EAAoBU,EAAEC,EAC9B,EAEIsC,EAAqBC,KAAK,wBAA0BA,KAAK,yBAA2B,GACxFD,EAAmBrE,QAAQgE,EAAqBO,KAAK,KAAM,IAC3DF,EAAmBvF,KAAOkF,EAAqBO,KAAK,KAAMF,EAAmBvF,KAAKyF,KAAKF,G,IC/CvF,IAAIG,EAAsBpD,EAAoBU,OAAEP,EAAW,CAAC,MAAM,WAAa,OAAOH,EAAoB,KAAO,IACjHoD,EAAsBpD,EAAoBU,EAAE0C,E", "sources": ["webpack://vue3demo/./src/App.vue", "webpack://vue3demo/./src/components/ScanOnWeb.vue", "webpack://vue3demo/./src/services/api.js", "webpack://vue3demo/./src/components/ScanOnWeb.vue?0aac", "webpack://vue3demo/./src/App.vue?7ccd", "webpack://vue3demo/./src/main.js", "webpack://vue3demo/webpack/bootstrap", "webpack://vue3demo/webpack/runtime/chunk loaded", "webpack://vue3demo/webpack/runtime/compat get default export", "webpack://vue3demo/webpack/runtime/define property getters", "webpack://vue3demo/webpack/runtime/global", "webpack://vue3demo/webpack/runtime/hasOwnProperty shorthand", "webpack://vue3demo/webpack/runtime/make namespace object", "webpack://vue3demo/webpack/runtime/jsonp chunk loading", "webpack://vue3demo/webpack/startup"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <ScanOnWeb />\n  </div>\n</template>\n\n<script>\nimport ScanOnWeb from './components/ScanOnWeb.vue'\n\nexport default {\n  name: 'App',\n  components: {\n    ScanOnWeb\n  }\n}\n</script>\n\n<style>\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nbody {\n  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: #f5f7fa;\n}\n\n#app {\n  min-height: 100vh;\n}\n\n/* 全局滚动条样式 */\n::-webkit-scrollbar {\n  width: 8px;\n  height: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n</style>\n", "<!-- eslint-disable vue/no-unused-components, no-unused-vars -->\n<template>\n  <div class=\"scan-container\">\n    <!-- 页面标题 -->\n    <el-card class=\"header-card\">\n      <h1 class=\"page-title\">\n        <el-icon><Camera /></el-icon>\n        文档扫描系统\n      </h1>\n      <p class=\"page-subtitle\">专业的文档扫描与管理解决方案</p>\n    </el-card>\n\n    <!-- 连接状态指示器 -->\n    <el-alert\n      v-if=\"!isConnected\"\n      title=\"扫描服务未连接\"\n      description=\"请确保扫描服务程序已启动并正在运行\"\n      type=\"warning\"\n      :closable=\"false\"\n      show-icon\n      class=\"connection-alert\"\n    />\n\n    <!-- 扫描配置区域 -->\n    <el-card class=\"config-card\">\n      <template #header>\n        <div class=\"card-header\">\n          <el-icon><Setting /></el-icon>\n          <span>扫描配置</span>\n        </div>\n      </template>\n\n      <el-row :gutter=\"20\">\n        <!-- 设备选择 -->\n        <el-col :span=\"12\">\n          <el-form-item label=\"扫描设备\">\n            <el-select\n              v-model=\"selectedDevice\"\n              @change=\"onDeviceChange\"\n              placeholder=\"请选择扫描设备\"\n              style=\"width: 100%\"\n            >\n              <el-option\n                v-for=\"(device, index) in devices\"\n                :key=\"index\"\n                :label=\"device\"\n                :value=\"index\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-col>\n\n        <!-- 色彩模式 -->\n        <el-col :span=\"12\">\n          <el-form-item label=\"色彩模式\">\n            <el-select v-model=\"config.colorMode\" style=\"width: 100%\">\n              <el-option label=\"彩色\" value=\"RGB\" />\n              <el-option label=\"灰度\" value=\"GRAY\" />\n              <el-option label=\"黑白\" value=\"BW\" />\n            </el-select>\n          </el-form-item>\n        </el-col>\n\n        <!-- 分辨率设置 -->\n        <el-col :span=\"12\">\n          <el-form-item label=\"分辨率 (DPI)\">\n            <el-row :gutter=\"10\">\n              <el-col :span=\"11\">\n                <el-input-number\n                  v-model=\"config.dpi_x\"\n                  :min=\"75\"\n                  :max=\"1200\"\n                  :step=\"25\"\n                  style=\"width: 100%\"\n                />\n              </el-col>\n              <el-col :span=\"2\" class=\"dpi-separator\">×</el-col>\n              <el-col :span=\"11\">\n                <el-input-number\n                  v-model=\"config.dpi_y\"\n                  :min=\"75\"\n                  :max=\"1200\"\n                  :step=\"25\"\n                  style=\"width: 100%\"\n                />\n              </el-col>\n            </el-row>\n          </el-form-item>\n        </el-col>\n\n        <!-- 高级选项 -->\n        <el-col :span=\"12\">\n          <el-form-item label=\"高级选项\">\n            <div class=\"advanced-options-compact\">\n              <div class=\"options-row\">\n                <el-checkbox v-model=\"config.showDialog\"\n                  >设备对话框</el-checkbox\n                >\n                <el-checkbox v-model=\"config.autoFeedEnable\"\n                  >自动进纸</el-checkbox\n                >\n                <el-checkbox v-model=\"config.dupxMode\">双面扫描</el-checkbox>\n              </div>\n              <div class=\"options-row\">\n                <el-checkbox v-model=\"config.autoDeskew\">自动纠偏</el-checkbox>\n                <el-checkbox v-model=\"config.autoBorderDetection\"\n                  >边框检测</el-checkbox\n                >\n              </div>\n            </div>\n          </el-form-item>\n        </el-col>\n      </el-row>\n    </el-card>\n\n    <!-- 操作按钮区域 -->\n    <el-card class=\"action-card\">\n      <el-row :gutter=\"15\">\n        <el-col :span=\"4\">\n          <el-button\n            type=\"primary\"\n            @click=\"loadDevices\"\n            :loading=\"loading.devices\"\n            style=\"width: 100%\"\n          >\n            <el-icon><Refresh /></el-icon>\n            刷新设备\n          </el-button>\n        </el-col>\n        <el-col :span=\"4\">\n          <el-button\n            type=\"success\"\n            @click=\"startScan\"\n            :loading=\"loading.scan\"\n            :disabled=\"selectedDevice === -1 || !isConnected\"\n            style=\"width: 100%\"\n          >\n            <el-icon><Camera /></el-icon>\n            开始扫描\n          </el-button>\n        </el-col>\n        <el-col :span=\"4\">\n          <el-button\n            type=\"info\"\n            @click=\"getAllImage\"\n            :loading=\"loading.images\"\n            style=\"width: 100%\"\n          >\n            <el-icon><Picture /></el-icon>\n            获取图像\n          </el-button>\n        </el-col>\n        <el-col :span=\"4\">\n          <el-button type=\"warning\" @click=\"clearAll\" style=\"width: 100%\">\n            🗑️ 清空结果\n          </el-button>\n        </el-col>\n        <el-col :span=\"4\">\n          <el-button\n            type=\"primary\"\n            @click=\"showUploadDialog\"\n            :disabled=\"images.length === 0\"\n            style=\"width: 100%\"\n          >\n            📤 上传文档\n          </el-button>\n        </el-col>\n        <el-col :span=\"4\">\n          <el-button\n            type=\"default\"\n            @click=\"saveAs\"\n            :disabled=\"images.length === 0\"\n            style=\"width: 100%\"\n          >\n            💾 本地保存\n          </el-button>\n        </el-col>\n      </el-row>\n    </el-card>\n\n    <!-- 扫描结果展示区域 -->\n    <el-card class=\"result-card\" v-if=\"images.length > 0\">\n      <template #header>\n        <div class=\"card-header\">\n          <el-icon><Picture /></el-icon>\n          <span>扫描结果 ({{ images.length }} 张图像)</span>\n          <div class=\"header-actions\">\n            <el-button-group>\n              <el-button\n                :type=\"viewMode === 'grid' ? 'primary' : 'default'\"\n                @click=\"viewMode = 'grid'\"\n                size=\"small\"\n              >\n                🔲 网格\n              </el-button>\n              <el-button\n                :type=\"viewMode === 'list' ? 'primary' : 'default'\"\n                @click=\"viewMode = 'list'\"\n                size=\"small\"\n              >\n                📋 列表\n              </el-button>\n            </el-button-group>\n          </div>\n        </div>\n      </template>\n\n      <!-- 网格视图 -->\n      <div v-if=\"viewMode === 'grid'\" class=\"image-grid\">\n        <div v-for=\"(image, index) in images\" :key=\"index\" class=\"image-item\">\n          <div class=\"image-wrapper\">\n            <img\n              :src=\"image.src\"\n              :alt=\"`扫描图像 ${index + 1}`\"\n              @click=\"previewImage(index)\"\n              class=\"scan-image\"\n            />\n            <div class=\"image-overlay\">\n              <el-button-group>\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  @click=\"previewImage(index)\"\n                >\n                  👁️\n                </el-button>\n                <el-button\n                  type=\"success\"\n                  size=\"small\"\n                  @click=\"downloadImage(index)\"\n                >\n                  💾\n                </el-button>\n                <el-button\n                  type=\"danger\"\n                  size=\"small\"\n                  @click=\"deleteImage(index)\"\n                >\n                  🗑️\n                </el-button>\n              </el-button-group>\n            </div>\n          </div>\n          <div class=\"image-info\">\n            <span class=\"image-index\">图像 {{ index + 1 }}</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 列表视图 -->\n      <el-table v-else :data=\"images\" stripe>\n        <el-table-column label=\"预览\" width=\"120\">\n          <template #default=\"{ row, $index }\">\n            <img\n              :src=\"row.src\"\n              class=\"table-thumbnail\"\n              @click=\"previewImage($index)\"\n            />\n          </template>\n        </el-table-column>\n        <el-table-column label=\"图像编号\" prop=\"index\" width=\"100\">\n          <template #default=\"{ $index }\"> 图像 {{ $index + 1 }} </template>\n        </el-table-column>\n        <el-table-column label=\"格式\" width=\"100\">\n          <template #default>\n            <el-tag type=\"info\">JPEG</el-tag>\n          </template>\n        </el-table-column>\n        <el-table-column label=\"操作\" width=\"200\">\n          <template #default=\"{ $index }\">\n            <el-button-group>\n              <el-button\n                type=\"primary\"\n                size=\"small\"\n                @click=\"previewImage($index)\"\n              >\n                👁️ 预览\n              </el-button>\n              <el-button\n                type=\"success\"\n                size=\"small\"\n                @click=\"downloadImage($index)\"\n              >\n                💾 下载\n              </el-button>\n              <el-button\n                type=\"danger\"\n                size=\"small\"\n                @click=\"deleteImage($index)\"\n              >\n                🗑️ 删除\n              </el-button>\n            </el-button-group>\n          </template>\n        </el-table-column>\n      </el-table>\n    </el-card>\n\n    <!-- 空状态 -->\n    <el-empty v-else description=\"暂无扫描结果\" class=\"empty-state\">\n      <el-button\n        type=\"primary\"\n        @click=\"startScan\"\n        :disabled=\"selectedDevice === -1 || !isConnected\"\n      >\n        开始扫描\n      </el-button>\n    </el-empty>\n  </div>\n\n  <!-- 上传对话框 -->\n  <el-dialog\n    v-model=\"uploadDialog.visible\"\n    title=\"上传文档\"\n    width=\"500px\"\n    :close-on-click-modal=\"false\"\n  >\n    <el-form :model=\"uploadDialog.form\" label-width=\"100px\">\n      <el-form-item label=\"文档ID\">\n        <el-input v-model=\"uploadDialog.form.id\" placeholder=\"请输入文档ID\" />\n      </el-form-item>\n      <el-form-item label=\"文档描述\">\n        <el-input\n          v-model=\"uploadDialog.form.description\"\n          type=\"textarea\"\n          :rows=\"3\"\n          placeholder=\"请输入文档描述\"\n        />\n      </el-form-item>\n      <el-form-item label=\"上传格式\">\n        <el-radio-group v-model=\"uploadDialog.form.format\">\n          <el-radio label=\"pdf\">PDF文档</el-radio>\n          <el-radio label=\"tiff\">TIFF图像</el-radio>\n          <el-radio label=\"jpg\">JPG图像</el-radio>\n        </el-radio-group>\n      </el-form-item>\n    </el-form>\n\n    <template #footer>\n      <el-button @click=\"uploadDialog.visible = false\">取消</el-button>\n      <el-button\n        type=\"primary\"\n        @click=\"confirmUpload\"\n        :loading=\"loading.upload\"\n      >\n        确认上传\n      </el-button>\n    </template>\n  </el-dialog>\n\n  <!-- 图像预览对话框 -->\n  <el-dialog\n    v-model=\"previewDialog.visible\"\n    title=\"图像预览\"\n    width=\"80%\"\n    :close-on-click-modal=\"false\"\n  >\n    <div class=\"preview-container\" v-if=\"previewDialog.currentImage\">\n      <img\n        :src=\"previewDialog.currentImage.src\"\n        class=\"preview-image\"\n        :alt=\"`预览图像 ${previewDialog.currentIndex + 1}`\"\n      />\n    </div>\n    <template #footer>\n      <div class=\"preview-footer\">\n        <div class=\"preview-info\">\n          图像 {{ previewDialog.currentIndex + 1 }} / {{ images.length }}\n        </div>\n        <div class=\"preview-actions\">\n          <el-button\n            @click=\"prevImage\"\n            :disabled=\"previewDialog.currentIndex <= 0\"\n          >\n            ⬅️ 上一张\n          </el-button>\n          <el-button\n            @click=\"nextImage\"\n            :disabled=\"previewDialog.currentIndex >= images.length - 1\"\n          >\n            下一张 ➡️\n          </el-button>\n          <el-button type=\"success\" @click=\"downloadCurrentImage\">\n            💾 下载\n          </el-button>\n          <el-button @click=\"previewDialog.visible = false\">关闭</el-button>\n        </div>\n      </div>\n    </template>\n  </el-dialog>\n</template>\n\n<script>\nimport { ref, reactive, onMounted, onUnmounted } from \"vue\";\nimport { ElMessage, ElMessageBox } from \"element-plus\";\nimport { Camera, Setting, Picture, Refresh } from \"@element-plus/icons-vue\";\nimport ScanOnWeb from \"scanonweb\";\nimport { utils } from \"@/services/api.js\";\n\nexport default {\n  name: \"ScanOnWeb\",\n  components: {\n    Camera,\n    Setting,\n    Picture,\n    Refresh,\n  },\n  setup() {\n    // 响应式数据\n    const scanonweb = ref(null);\n    const isConnected = ref(false);\n    const devices = ref([]);\n    const selectedDevice = ref(-1);\n    const images = ref([]);\n    const viewMode = ref(\"grid\");\n\n    // 扫描配置\n    const config = reactive({\n      dpi_x: 300,\n      dpi_y: 300,\n      colorMode: \"RGB\",\n      showDialog: false,\n      autoFeedEnable: true,\n      autoFeed: false,\n      dupxMode: false,\n      autoDeskew: false,\n      autoBorderDetection: false,\n    });\n\n    // 加载状态\n    const loading = reactive({\n      devices: false,\n      scan: false,\n      images: false,\n      upload: false,\n    });\n\n    // 上传对话框\n    const uploadDialog = reactive({\n      visible: false,\n      form: {\n        id: \"\",\n        description: \"\",\n        format: \"pdf\",\n      },\n    });\n\n    // 预览对话框\n    const previewDialog = reactive({\n      visible: false,\n      currentIndex: 0,\n      currentImage: null,\n    });\n\n    // 初始化扫描服务\n    const initScanService = () => {\n      try {\n        scanonweb.value = new ScanOnWeb();\n\n        // 设置事件回调\n        scanonweb.value.onGetDevicesListEvent = (msg) => {\n          devices.value = msg.devices || [];\n          // 自动选择第一个设备，如果没有当前设备则选择第一个\n          if (devices.value.length > 0) {\n            selectedDevice.value = msg.currentIndex >= 0 ? msg.currentIndex : 0;\n            // 如果自动选择了设备，调用设备选择事件\n            if (selectedDevice.value >= 0) {\n              setTimeout(() => {\n                onDeviceChange(selectedDevice.value);\n              }, 100);\n            }\n          } else {\n            selectedDevice.value = -1;\n          }\n          loading.devices = false;\n          isConnected.value = true;\n          ElMessage.success(\n            `发现 ${devices.value.length} 个扫描设备${\n              selectedDevice.value >= 0 ? \"，已自动选择第一个设备\" : \"\"\n            }`\n          );\n        };\n\n        scanonweb.value.onScanFinishedEvent = (msg) => {\n          loading.scan = false;\n          ElMessage.success(`扫描完成！共扫描 ${msg.imageAfterCount} 张图像`);\n          getAllImage();\n        };\n\n        scanonweb.value.onGetAllImageEvent = (msg) => {\n          loading.images = false;\n          if (msg.images && msg.images.length > 0) {\n            images.value = msg.images.map((image, index) => ({\n              src: `data:image/jpg;base64,${image}`,\n              index: index,\n              base64: image,\n            }));\n            ElMessage.success(`获取到 ${images.value.length} 张图像`);\n          } else {\n            ElMessage.info(\"暂无扫描图像\");\n          }\n        };\n\n        scanonweb.value.onGetImageByIdEvent = (msg) => {\n          if (msg.imageBase64) {\n            addImage(msg.imageBase64);\n          }\n        };\n\n        scanonweb.value.onImageEditedEvent = (msg) => {\n          ElMessage.info(`图像 ${msg.imageIndex + 1} 已编辑`);\n          if (msg.imageBase64) {\n            editImage(msg.imageIndex, msg.imageBase64);\n          }\n        };\n\n        scanonweb.value.onUploadEvent = () => {\n          ElMessage.info(\"用户点击了上传按钮\");\n          showUploadDialog();\n        };\n\n        // 检查连接状态\n        setTimeout(() => {\n          if (devices.value.length === 0) {\n            isConnected.value = false;\n            ElMessage.warning(\"扫描服务连接失败，请检查服务是否启动\");\n          }\n        }, 3000);\n      } catch (error) {\n        console.error(\"初始化扫描服务失败:\", error);\n        isConnected.value = false;\n        ElMessage.error(\"初始化扫描服务失败\");\n      }\n    };\n\n    // 加载设备列表\n    const loadDevices = async () => {\n      if (!scanonweb.value) {\n        ElMessage.error(\"扫描服务未初始化\");\n        return;\n      }\n\n      loading.devices = true;\n      try {\n        scanonweb.value.loadDevices();\n      } catch (error) {\n        loading.devices = false;\n        ElMessage.error(\"获取设备列表失败\");\n      }\n    };\n\n    // 设备选择变化\n    const onDeviceChange = (deviceIndex) => {\n      if (scanonweb.value) {\n        scanonweb.value.selectScanDevice(deviceIndex);\n        ElMessage.info(`已选择设备: ${devices.value[deviceIndex]}`);\n      }\n    };\n\n    // 开始扫描\n    const startScan = async () => {\n      if (!scanonweb.value) {\n        ElMessage.error(\"扫描服务未初始化\");\n        return;\n      }\n\n      if (selectedDevice.value === -1) {\n        ElMessage.warning(\"请先选择扫描设备\");\n        return;\n      }\n\n      loading.scan = true;\n      try {\n        // 更新扫描配置\n        scanonweb.value.scaner_work_config = {\n          ...scanonweb.value.scaner_work_config,\n          ...config,\n          deviceIndex: selectedDevice.value,\n        };\n\n        scanonweb.value.startScan();\n        ElMessage.info(\"开始扫描...\");\n      } catch (error) {\n        loading.scan = false;\n        ElMessage.error(\"启动扫描失败\");\n      }\n    };\n\n    // 获取所有图像\n    const getAllImage = async () => {\n      if (!scanonweb.value) {\n        ElMessage.error(\"扫描服务未初始化\");\n        return;\n      }\n\n      loading.images = true;\n      try {\n        scanonweb.value.getAllImage();\n      } catch (error) {\n        loading.images = false;\n        ElMessage.error(\"获取图像失败\");\n      }\n    };\n\n    // 清空所有图像\n    const clearAll = async () => {\n      try {\n        await ElMessageBox.confirm(\"确定要清空所有扫描结果吗？\", \"确认操作\", {\n          confirmButtonText: \"确定\",\n          cancelButtonText: \"取消\",\n          type: \"warning\",\n        });\n\n        if (scanonweb.value) {\n          scanonweb.value.clearAll();\n          images.value = [];\n          ElMessage.success(\"已清空所有扫描结果\");\n        }\n      } catch {\n        // 用户取消操作\n      }\n    };\n\n    // 添加图像\n    const addImage = (imageBase64) => {\n      images.value.push({\n        src: `data:image/jpg;base64,${imageBase64}`,\n        index: images.value.length,\n        base64: imageBase64,\n      });\n    };\n\n    // 编辑图像\n    const editImage = (index, imageBase64) => {\n      if (index >= 0 && index < images.value.length) {\n        images.value[index] = {\n          src: `data:image/jpg;base64,${imageBase64}`,\n          index: index,\n          base64: imageBase64,\n        };\n      }\n    };\n\n    // 显示上传对话框\n    const showUploadDialog = () => {\n      if (images.value.length === 0) {\n        ElMessage.warning(\"没有可上传的图像\");\n        return;\n      }\n\n      uploadDialog.form.id = utils.generateId();\n      uploadDialog.form.description = `扫描文档_${new Date().toLocaleString()}`;\n      uploadDialog.visible = true;\n    };\n\n    // 确认上传\n    const confirmUpload = async () => {\n      if (!uploadDialog.form.id.trim()) {\n        ElMessage.warning(\"请输入文档ID\");\n        return;\n      }\n\n      if (!scanonweb.value) {\n        ElMessage.error(\"扫描服务未初始化\");\n        return;\n      }\n\n      if (images.value.length === 0) {\n        ElMessage.warning(\"没有可上传的图像\");\n        return;\n      }\n\n      loading.upload = true;\n      try {\n        const { id, description, format } = uploadDialog.form;\n        const uploadUrl = \"http://localhost:8080/upload\";\n\n        // 设置上传完成回调\n        scanonweb.value.onUploadAllImageAsPdfToUrlEvent = (msg) => {\n          console.log(\"PDF上传回调:\", msg);\n          loading.upload = false;\n\n          try {\n            // 解析uploadResult字符串\n            let uploadResult = null;\n            if (msg.uploadResult) {\n              uploadResult =\n                typeof msg.uploadResult === \"string\"\n                  ? JSON.parse(msg.uploadResult)\n                  : msg.uploadResult;\n            }\n\n            if (uploadResult && uploadResult.success) {\n              ElMessage.success(\n                `PDF文档上传成功！共 ${msg.imageCount || 0} 张图像`\n              );\n              uploadDialog.visible = false;\n              console.log(\"上传详情:\", uploadResult);\n            } else {\n              const errorMsg =\n                uploadResult?.message || msg.message || \"未知错误\";\n              ElMessage.error(`PDF上传失败: ${errorMsg}`);\n            }\n          } catch (error) {\n            console.error(\"解析上传结果失败:\", error, msg);\n            ElMessage.error(`PDF上传失败: 响应解析错误`);\n          }\n        };\n\n        scanonweb.value.onUploadAllImageAsTiffToUrlEvent = (msg) => {\n          console.log(\"TIFF上传回调:\", msg);\n          loading.upload = false;\n\n          try {\n            // 解析uploadResult字符串\n            let uploadResult = null;\n            if (msg.uploadResult) {\n              uploadResult =\n                typeof msg.uploadResult === \"string\"\n                  ? JSON.parse(msg.uploadResult)\n                  : msg.uploadResult;\n            }\n\n            if (uploadResult && uploadResult.success) {\n              ElMessage.success(\n                `TIFF文档上传成功！共 ${msg.imageCount || 0} 张图像`\n              );\n              uploadDialog.visible = false;\n              console.log(\"上传详情:\", uploadResult);\n            } else {\n              const errorMsg =\n                uploadResult?.message || msg.message || \"未知错误\";\n              ElMessage.error(`TIFF上传失败: ${errorMsg}`);\n            }\n          } catch (error) {\n            console.error(\"解析上传结果失败:\", error, msg);\n            ElMessage.error(`TIFF上传失败: 响应解析错误`);\n          }\n        };\n\n        scanonweb.value.onUploadJpgImageByIndexEvent = (msg) => {\n          console.log(\"JPG上传回调:\", msg);\n          loading.upload = false;\n\n          try {\n            // 解析uploadResult字符串\n            let uploadResult = null;\n            if (msg.uploadResult) {\n              uploadResult =\n                typeof msg.uploadResult === \"string\"\n                  ? JSON.parse(msg.uploadResult)\n                  : msg.uploadResult;\n            }\n\n            if (uploadResult && uploadResult.success) {\n              ElMessage.success(`JPG图像上传成功！`);\n              uploadDialog.visible = false;\n              console.log(\"上传详情:\", uploadResult);\n            } else {\n              const errorMsg =\n                uploadResult?.message || msg.message || \"未知错误\";\n              ElMessage.error(`JPG上传失败: ${errorMsg}`);\n            }\n          } catch (error) {\n            console.error(\"解析上传结果失败:\", error, msg);\n            ElMessage.error(`JPG上传失败: 响应解析错误`);\n          }\n        };\n\n        // 调用控件内置的上传方法\n        if (format === \"pdf\") {\n          ElMessage.info(\"开始上传PDF文档...\");\n          scanonweb.value.uploadAllImageAsPdfToUrl(uploadUrl, id, description);\n        } else if (format === \"tiff\") {\n          ElMessage.info(\"开始上传TIFF文档...\");\n          const tiffUploadUrl = \"http://localhost:8080/upload-tiff\";\n          scanonweb.value.uploadAllImageAsTiffToUrl(\n            tiffUploadUrl,\n            id,\n            description\n          );\n        } else if (format === \"jpg\") {\n          ElMessage.info(\"开始上传JPG图像...\");\n          const jpgUploadUrl = \"http://localhost:8080/upload-jpg\";\n          // 上传第一张图像作为示例，实际可以循环上传所有图像\n          scanonweb.value.uploadJpgImageByIndex(\n            jpgUploadUrl,\n            id,\n            description,\n            0\n          );\n        }\n      } catch (error) {\n        loading.upload = false;\n        console.error(\"上传失败:\", error);\n        ElMessage.error(`上传失败: ${error.message || \"未知错误\"}`);\n      }\n    };\n\n    // 预览图像\n    const previewImage = (index) => {\n      if (index >= 0 && index < images.value.length) {\n        previewDialog.currentIndex = index;\n        previewDialog.currentImage = images.value[index];\n        previewDialog.visible = true;\n      }\n    };\n\n    // 上一张图像\n    const prevImage = () => {\n      if (previewDialog.currentIndex > 0) {\n        previewDialog.currentIndex--;\n        previewDialog.currentImage = images.value[previewDialog.currentIndex];\n      }\n    };\n\n    // 下一张图像\n    const nextImage = () => {\n      if (previewDialog.currentIndex < images.value.length - 1) {\n        previewDialog.currentIndex++;\n        previewDialog.currentImage = images.value[previewDialog.currentIndex];\n      }\n    };\n\n    // 下载图像\n    const downloadImage = (index) => {\n      if (index >= 0 && index < images.value.length) {\n        const image = images.value[index];\n        const filename = `scan_image_${index + 1}.jpg`;\n        utils.downloadBase64Image(image.base64, filename);\n        ElMessage.success(`图像 ${index + 1} 下载成功`);\n      }\n    };\n\n    // 下载当前预览图像\n    const downloadCurrentImage = () => {\n      downloadImage(previewDialog.currentIndex);\n    };\n\n    // 删除图像\n    const deleteImage = async (index) => {\n      try {\n        await ElMessageBox.confirm(\n          `确定要删除图像 ${index + 1} 吗？`,\n          \"确认删除\",\n          {\n            confirmButtonText: \"确定\",\n            cancelButtonText: \"取消\",\n            type: \"warning\",\n          }\n        );\n\n        images.value.splice(index, 1);\n        // 重新设置索引\n        images.value.forEach((img, idx) => {\n          img.index = idx;\n        });\n\n        ElMessage.success(\"图像删除成功\");\n      } catch {\n        // 用户取消操作\n      }\n    };\n\n    // 本地保存\n    const saveAs = () => {\n      if (!scanonweb.value) {\n        ElMessage.error(\"扫描服务未初始化\");\n        return;\n      }\n\n      if (images.value.length === 0) {\n        ElMessage.warning(\"没有可保存的图像\");\n        return;\n      }\n\n      try {\n        const filename = `d:/scan_${Date.now()}.pdf`;\n        scanonweb.value.saveAllImageToLocal(filename);\n        ElMessage.success(\"文件保存成功\");\n      } catch (error) {\n        ElMessage.error(\"保存失败\");\n      }\n    };\n\n    // 生命周期钩子\n    onMounted(() => {\n      initScanService();\n      // 自动加载设备列表\n      setTimeout(() => {\n        loadDevices();\n      }, 1000);\n    });\n\n    onUnmounted(() => {\n      // 清理资源\n      if (scanonweb.value && scanonweb.value.h5socket) {\n        scanonweb.value.h5socket.close();\n      }\n    });\n\n    // 返回模板需要的数据和方法\n    return {\n      // 响应式数据\n      isConnected,\n      devices,\n      selectedDevice,\n      images,\n      viewMode,\n      config,\n      loading,\n      uploadDialog,\n      previewDialog,\n\n      // 方法\n      loadDevices,\n      onDeviceChange,\n      startScan,\n      getAllImage,\n      clearAll,\n      showUploadDialog,\n      confirmUpload,\n      previewImage,\n      prevImage,\n      nextImage,\n      downloadImage,\n      downloadCurrentImage,\n      deleteImage,\n      saveAs,\n    };\n  },\n};\n</script>\n\n<style scoped>\n.scan-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n  background-color: #f5f7fa;\n  min-height: 100vh;\n}\n\n.header-card {\n  margin-bottom: 20px;\n  text-align: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  border: none;\n}\n\n.page-title {\n  margin: 0;\n  font-size: 2.5em;\n  font-weight: 300;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 15px;\n}\n\n.page-subtitle {\n  margin: 10px 0 0 0;\n  font-size: 1.1em;\n  opacity: 0.9;\n}\n\n.connection-alert {\n  margin-bottom: 20px;\n}\n\n.config-card,\n.action-card,\n.result-card {\n  margin-bottom: 20px;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n  border: none;\n}\n\n.card-header {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.header-actions {\n  margin-left: auto;\n}\n\n.dpi-separator {\n  text-align: center;\n  line-height: 32px;\n  font-weight: bold;\n  color: #909399;\n}\n\n.advanced-options-compact {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.options-row {\n  display: flex;\n  gap: 15px;\n  flex-wrap: wrap;\n}\n\n.image-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  gap: 20px;\n  padding: 10px 0;\n}\n\n.image-item {\n  background: white;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  transition: transform 0.2s, box-shadow 0.2s;\n}\n\n.image-item:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n}\n\n.image-wrapper {\n  position: relative;\n  overflow: hidden;\n}\n\n.scan-image {\n  width: 100%;\n  height: 200px;\n  object-fit: cover;\n  cursor: pointer;\n  transition: transform 0.2s;\n}\n\n.scan-image:hover {\n  transform: scale(1.05);\n}\n\n.image-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transition: opacity 0.2s;\n}\n\n.image-wrapper:hover .image-overlay {\n  opacity: 1;\n}\n\n.image-info {\n  padding: 15px;\n  text-align: center;\n}\n\n.image-index {\n  font-weight: 600;\n  color: #303133;\n}\n\n.table-thumbnail {\n  width: 80px;\n  height: 60px;\n  object-fit: cover;\n  border-radius: 4px;\n  cursor: pointer;\n  transition: transform 0.2s;\n}\n\n.table-thumbnail:hover {\n  transform: scale(1.1);\n}\n\n.empty-state {\n  margin: 40px 0;\n  padding: 40px;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.preview-container {\n  text-align: center;\n  padding: 20px;\n}\n\n.preview-image {\n  max-width: 100%;\n  max-height: 70vh;\n  object-fit: contain;\n  border-radius: 8px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n}\n\n.preview-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 10px 0;\n}\n\n.preview-info {\n  font-weight: 600;\n  color: #606266;\n}\n\n.preview-actions {\n  display: flex;\n  gap: 10px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .scan-container {\n    padding: 10px;\n  }\n\n  .page-title {\n    font-size: 2em;\n  }\n\n  .image-grid {\n    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n    gap: 15px;\n  }\n\n  .preview-footer {\n    flex-direction: column;\n    gap: 15px;\n  }\n\n  .preview-actions {\n    flex-wrap: wrap;\n    justify-content: center;\n  }\n}\n\n/* 动画效果 */\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.config-card,\n.action-card,\n.result-card {\n  animation: fadeIn 0.5s ease-out;\n}\n\n/* Element Plus 组件样式覆盖 */\n.el-card__header {\n  background-color: #fafafa;\n  border-bottom: 1px solid #ebeef5;\n}\n\n.el-button {\n  transition: all 0.2s;\n}\n\n.el-button:hover {\n  transform: translateY(-1px);\n}\n\n.el-form-item__label {\n  font-weight: 600;\n  color: #606266;\n}\n\n.el-alert {\n  border-radius: 8px;\n}\n</style>\n", "// API服务模块\nimport axios from 'axios'\n\n// 创建axios实例\nconst api = axios.create({\n  baseURL: 'http://localhost:8080',\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n})\n\n// 请求拦截器\napi.interceptors.request.use(\n  config => {\n    console.log('发送请求:', config.method?.toUpperCase(), config.url)\n    return config\n  },\n  error => {\n    console.error('请求错误:', error)\n    return Promise.reject(error)\n  }\n)\n\n// 响应拦截器\napi.interceptors.response.use(\n  response => {\n    console.log('收到响应:', response.status, response.config.url)\n    return response\n  },\n  error => {\n    console.error('响应错误:', error.response?.status, error.message)\n    if (error.response?.status === 404) {\n      console.error('API接口不存在')\n    } else if (error.response?.status >= 500) {\n      console.error('服务器内部错误')\n    } else if (error.code === 'ECONNREFUSED') {\n      console.error('无法连接到服务器，请确保后台服务已启动')\n    }\n    return Promise.reject(error)\n  }\n)\n\n// API接口定义\nexport const scanAPI = {\n  // 健康检查\n  healthCheck() {\n    return api.get('/health')\n  },\n\n  // 上传PDF格式的所有图像\n  uploadAllImageAsPdf(imageData, id = 'scan', description = '扫描文档') {\n    return api.post('/upload', {\n      id,\n      desc: description,\n      format: 'pdf',\n      imageData\n    })\n  },\n\n  // 上传TIFF格式的所有图像\n  uploadAllImageAsTiff(imageData, id = 'scan', description = '扫描文档') {\n    return api.post('/upload/tiff', {\n      id,\n      desc: description,\n      format: 'tiff',\n      imageData\n    })\n  },\n\n  // 上传单个JPG图像\n  uploadJpgImageByIndex(imageData, index, id = 'scan', description = '扫描图像') {\n    return api.post('/upload/jpg', {\n      id,\n      desc: description,\n      index,\n      imageData\n    })\n  },\n\n  // 批量上传多个JPG图像\n  uploadMultipleJpgImages(images, id = 'scan', description = '扫描图像') {\n    const uploadPromises = images.map((imageData, index) => \n      this.uploadJpgImageByIndex(imageData, index, id, description)\n    )\n    return Promise.all(uploadPromises)\n  },\n\n  // 获取已上传文件列表\n  getUploadedFiles() {\n    return api.get('/files')\n  },\n\n  // 删除文件\n  deleteFile(filename) {\n    return api.delete(`/files/${filename}`)\n  }\n}\n\n// 工具函数\nexport const utils = {\n  // 将base64字符串转换为Blob\n  base64ToBlob(base64, mimeType = 'image/jpeg') {\n    const byteCharacters = atob(base64)\n    const byteNumbers = new Array(byteCharacters.length)\n    for (let i = 0; i < byteCharacters.length; i++) {\n      byteNumbers[i] = byteCharacters.charCodeAt(i)\n    }\n    const byteArray = new Uint8Array(byteNumbers)\n    return new Blob([byteArray], { type: mimeType })\n  },\n\n  // 下载base64图像\n  downloadBase64Image(base64, filename = 'image.jpg') {\n    const blob = this.base64ToBlob(base64)\n    const url = URL.createObjectURL(blob)\n    const link = document.createElement('a')\n    link.href = url\n    link.download = filename\n    document.body.appendChild(link)\n    link.click()\n    document.body.removeChild(link)\n    URL.revokeObjectURL(url)\n  },\n\n  // 格式化文件大小\n  formatFileSize(bytes) {\n    if (bytes === 0) return '0 Bytes'\n    const k = 1024\n    const sizes = ['Bytes', 'KB', 'MB', 'GB']\n    const i = Math.floor(Math.log(bytes) / Math.log(k))\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n  },\n\n  // 生成唯一ID\n  generateId() {\n    return Date.now().toString(36) + Math.random().toString(36).substr(2)\n  }\n}\n\nexport default api\n", "import { render } from \"./ScanOnWeb.vue?vue&type=template&id=5fde91ee&scoped=true\"\nimport script from \"./ScanOnWeb.vue?vue&type=script&lang=js\"\nexport * from \"./ScanOnWeb.vue?vue&type=script&lang=js\"\n\nimport \"./ScanOnWeb.vue?vue&type=style&index=0&id=5fde91ee&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-5fde91ee\"]])\n\nexport default __exports__", "import { render } from \"./App.vue?vue&type=template&id=e17a2c94\"\nimport script from \"./App.vue?vue&type=script&lang=js\"\nexport * from \"./App.vue?vue&type=script&lang=js\"\n\nimport \"./App.vue?vue&type=style&index=0&id=e17a2c94&lang=css\"\n\nimport exportComponent from \"../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render]])\n\nexport default __exports__", "import { createApp } from 'vue'\nimport ElementPlus from 'element-plus'\nimport 'element-plus/dist/index.css'\nimport * as ElementPlusIconsVue from '@element-plus/icons-vue'\nimport App from './App.vue'\n\nconst app = createApp(App)\n\n// 注册Element Plus\napp.use(ElementPlus)\n\n// 注册所有图标\nfor (const [key, component] of Object.entries(ElementPlusIconsVue)) {\n  app.component(key, component)\n}\n\napp.mount('#app')\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = function(result, chunkIds, fn, priority) {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar chunkIds = deferred[i][0];\n\t\tvar fn = deferred[i][1];\n\t\tvar priority = deferred[i][2];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every(function(key) { return __webpack_require__.O[key](chunkIds[j]); })) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = function(module) {\n\tvar getter = module && module.__esModule ?\n\t\tfunction() { return module['default']; } :\n\t\tfunction() { return module; };\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t524: 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = function(chunkId) { return installedChunks[chunkId] === 0; };\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = function(parentChunkLoadingFunction, data) {\n\tvar chunkIds = data[0];\n\tvar moreModules = data[1];\n\tvar runtime = data[2];\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some(function(id) { return installedChunks[id] !== 0; })) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkvue3demo\"] = self[\"webpackChunkvue3demo\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [504], function() { return __webpack_require__(8163); })\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n"], "names": ["id", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_ScanOnWeb", "class", "_createElementVNode", "key", "_Fragment", "_component_el_card", "default", "_withCtx", "_hoisted_2", "_component_el_icon", "_component_Camera", "_", "_createTextVNode", "_hoisted_3", "$setup", "isConnected", "_createCommentVNode", "_createBlock", "_component_el_alert", "title", "description", "type", "closable", "header", "_hoisted_4", "_component_Setting", "_hoisted_5", "_component_el_row", "gutter", "_component_el_col", "span", "_component_el_form_item", "label", "_component_el_select", "modelValue", "selected<PERSON><PERSON><PERSON>", "_cache", "$event", "onChange", "onDeviceChange", "placeholder", "style", "_renderList", "devices", "device", "index", "_component_el_option", "value", "config", "colorMode", "_component_el_input_number", "dpi_x", "min", "max", "step", "dpi_y", "_hoisted_6", "_hoisted_7", "_component_el_checkbox", "showDialog", "autoFeedEnable", "dupxMode", "_hoisted_8", "autoDeskew", "autoBorderDetection", "_component_el_button", "onClick", "loadDevices", "loading", "_component_Refresh", "startScan", "scan", "disabled", "getAllImage", "images", "_component_Picture", "clearAll", "showUploadDialog", "length", "saveAs", "_hoisted_9", "_toDisplayString", "_hoisted_10", "_component_el_button_group", "viewMode", "size", "_hoisted_11", "image", "_hoisted_12", "src", "alt", "previewImage", "_hoisted_13", "_hoisted_14", "downloadImage", "deleteImage", "_hoisted_15", "_hoisted_16", "_component_el_table", "data", "stripe", "_component_el_table_column", "width", "row", "$index", "_hoisted_17", "prop", "_component_el_tag", "_component_el_empty", "_component_el_dialog", "uploadDialog", "visible", "footer", "confirmUpload", "upload", "_component_el_form", "model", "form", "_component_el_input", "rows", "_component_el_radio_group", "format", "_component_el_radio", "previewDialog", "_hoisted_20", "_hoisted_21", "currentIndex", "_hoisted_22", "prevImage", "nextImage", "downloadCurrentImage", "currentImage", "_hoisted_18", "_hoisted_19", "api", "axios", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "console", "log", "method", "toUpperCase", "url", "error", "Promise", "reject", "response", "status", "message", "code", "utils", "base64ToBlob", "base64", "mimeType", "byteCharacters", "atob", "byteNumbers", "Array", "i", "charCodeAt", "byteArray", "Uint8Array", "Blob", "downloadBase64Image", "filename", "blob", "this", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "formatFileSize", "bytes", "k", "sizes", "Math", "floor", "parseFloat", "pow", "toFixed", "generateId", "Date", "now", "toString", "random", "substr", "name", "components", "Camera", "Setting", "Picture", "Refresh", "setup", "scanonweb", "ref", "reactive", "autoFeed", "initScanService", "ScanOnWeb", "onGetDevicesListEvent", "msg", "setTimeout", "ElMessage", "success", "onScanFinishedEvent", "imageAfterCount", "onGetAllImageEvent", "map", "info", "onGetImageByIdEvent", "imageBase64", "addImage", "onImageEditedEvent", "imageIndex", "editImage", "onUploadEvent", "warning", "async", "deviceIndex", "selectScanDevice", "scaner_work_config", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "push", "toLocaleString", "trim", "uploadUrl", "onUploadAllImageAsPdfToUrlEvent", "uploadResult", "JSON", "parse", "imageCount", "errorMsg", "onUploadAllImageAsTiffToUrlEvent", "onUploadJpgImageByIndexEvent", "uploadAllImageAsPdfToUrl", "tiffUploadUrl", "uploadAllImageAsTiffToUrl", "jpgUploadUrl", "uploadJpgImageByIndex", "splice", "for<PERSON>ach", "img", "idx", "saveAllImageToLocal", "onMounted", "onUnmounted", "h5socket", "close", "__exports__", "render", "app", "createApp", "App", "ElementPlus", "component", "Object", "entries", "ElementPlusIconsVue", "mount", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "exports", "module", "__webpack_modules__", "call", "m", "deferred", "O", "result", "chunkIds", "fn", "priority", "notFulfilled", "Infinity", "fulfilled", "j", "keys", "every", "r", "n", "getter", "__esModule", "d", "a", "definition", "o", "defineProperty", "enumerable", "get", "g", "globalThis", "Function", "e", "window", "obj", "prototype", "hasOwnProperty", "Symbol", "toStringTag", "installedChunks", "chunkId", "webpackJsonpCallback", "parentChunkLoadingFunction", "moreModules", "runtime", "some", "chunkLoadingGlobal", "self", "bind", "__webpack_exports__"], "sourceRoot": ""}