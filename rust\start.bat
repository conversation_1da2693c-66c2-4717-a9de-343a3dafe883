@echo off
chcp 65001 >nul
echo ================================
echo ScanOnWeb Rust Server
echo ================================

REM Check Rust environment
rustc --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Rust not found, please install Rust
    echo Visit: https://rustup.rs/
    pause
    exit /b 1
)

REM Check Cargo
cargo --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Cargo not found, please install Rust with Cargo
    pause
    exit /b 1
)

echo Building project...
cargo build --release

if %errorlevel% neq 0 (
    echo Build failed, please check error messages
    pause
    exit /b 1
)

echo Starting Rust server...
echo Access URL: http://localhost:8080
echo Press Ctrl+C to stop server

cargo run --release

pause
