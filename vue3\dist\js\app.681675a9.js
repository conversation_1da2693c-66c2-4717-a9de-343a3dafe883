(function(){"use strict";var e={8163:function(e,l,a){var o=a(5130),n=a(7186),t=(a(4188),a(7477)),i=a(6768);const s={id:"app"};function r(e,l,a,o,n,t){const r=(0,i.g2)("ScanOnWeb");return(0,i.uX)(),(0,i.CE)("div",s,[(0,i.bF)(r)])}var d=a(4232);const u=e=>((0,i.Qi)("data-v-5fde91ee"),e=e(),(0,i.jt)(),e),c={class:"scan-container"},g={class:"page-title"},p=u((()=>(0,i.Lk)("p",{class:"page-subtitle"},"专业的文档扫描与管理解决方案",-1))),f={class:"card-header"},m=u((()=>(0,i.Lk)("span",null,"扫描配置",-1))),k={class:"advanced-options-compact"},v={class:"options-row"},b={class:"options-row"},F={class:"card-header"},h={class:"header-actions"},_={key:0,class:"image-grid"},y={class:"image-wrapper"},w=["src","alt","onClick"],I={class:"image-overlay"},C={class:"image-info"},D={class:"image-index"},x=["src","onClick"],V={key:0,class:"preview-container"},W=["src","alt"],U={class:"preview-footer"},R={class:"preview-info"},A={class:"preview-actions"};function L(e,l,a,o,n,t){const s=(0,i.g2)("Camera"),r=(0,i.g2)("el-icon"),u=(0,i.g2)("el-card"),L=(0,i.g2)("el-alert"),E=(0,i.g2)("Setting"),B=(0,i.g2)("el-option"),T=(0,i.g2)("el-select"),P=(0,i.g2)("el-form-item"),S=(0,i.g2)("el-col"),$=(0,i.g2)("el-input-number"),j=(0,i.g2)("el-row"),O=(0,i.g2)("el-checkbox"),M=(0,i.g2)("Refresh"),G=(0,i.g2)("el-button"),K=(0,i.g2)("Picture"),J=(0,i.g2)("el-button-group"),X=(0,i.g2)("el-table-column"),z=(0,i.g2)("el-tag"),N=(0,i.g2)("el-table"),Q=(0,i.g2)("el-empty"),q=(0,i.g2)("el-input"),Y=(0,i.g2)("el-radio"),H=(0,i.g2)("el-radio-group"),Z=(0,i.g2)("el-form"),ee=(0,i.g2)("el-dialog");return(0,i.uX)(),(0,i.CE)(i.FK,null,[(0,i.Lk)("div",c,[(0,i.bF)(u,{class:"header-card"},{default:(0,i.k6)((()=>[(0,i.Lk)("h1",g,[(0,i.bF)(r,null,{default:(0,i.k6)((()=>[(0,i.bF)(s)])),_:1}),(0,i.eW)(" 文档扫描系统 ")]),p])),_:1}),o.isConnected?(0,i.Q3)("",!0):((0,i.uX)(),(0,i.Wv)(L,{key:0,title:"扫描服务未连接",description:"请确保扫描服务程序已启动并正在运行",type:"warning",closable:!1,"show-icon":"",class:"connection-alert"})),(0,i.bF)(u,{class:"config-card"},{header:(0,i.k6)((()=>[(0,i.Lk)("div",f,[(0,i.bF)(r,null,{default:(0,i.k6)((()=>[(0,i.bF)(E)])),_:1}),m])])),default:(0,i.k6)((()=>[(0,i.bF)(j,{gutter:20},{default:(0,i.k6)((()=>[(0,i.bF)(S,{span:12},{default:(0,i.k6)((()=>[(0,i.bF)(P,{label:"扫描设备"},{default:(0,i.k6)((()=>[(0,i.bF)(T,{modelValue:o.selectedDevice,"onUpdate:modelValue":l[0]||(l[0]=e=>o.selectedDevice=e),onChange:o.onDeviceChange,placeholder:"请选择扫描设备",style:{width:"100%"}},{default:(0,i.k6)((()=>[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(o.devices,((e,l)=>((0,i.uX)(),(0,i.Wv)(B,{key:l,label:e,value:l},null,8,["label","value"])))),128))])),_:1},8,["modelValue","onChange"])])),_:1})])),_:1}),(0,i.bF)(S,{span:12},{default:(0,i.k6)((()=>[(0,i.bF)(P,{label:"色彩模式"},{default:(0,i.k6)((()=>[(0,i.bF)(T,{modelValue:o.config.colorMode,"onUpdate:modelValue":l[1]||(l[1]=e=>o.config.colorMode=e),style:{width:"100%"}},{default:(0,i.k6)((()=>[(0,i.bF)(B,{label:"彩色",value:"RGB"}),(0,i.bF)(B,{label:"灰度",value:"GRAY"}),(0,i.bF)(B,{label:"黑白",value:"BW"})])),_:1},8,["modelValue"])])),_:1})])),_:1}),(0,i.bF)(S,{span:12},{default:(0,i.k6)((()=>[(0,i.bF)(P,{label:"分辨率 (DPI)"},{default:(0,i.k6)((()=>[(0,i.bF)(j,{gutter:10},{default:(0,i.k6)((()=>[(0,i.bF)(S,{span:11},{default:(0,i.k6)((()=>[(0,i.bF)($,{modelValue:o.config.dpi_x,"onUpdate:modelValue":l[2]||(l[2]=e=>o.config.dpi_x=e),min:75,max:1200,step:25,style:{width:"100%"}},null,8,["modelValue"])])),_:1}),(0,i.bF)(S,{span:2,class:"dpi-separator"},{default:(0,i.k6)((()=>[(0,i.eW)("×")])),_:1}),(0,i.bF)(S,{span:11},{default:(0,i.k6)((()=>[(0,i.bF)($,{modelValue:o.config.dpi_y,"onUpdate:modelValue":l[3]||(l[3]=e=>o.config.dpi_y=e),min:75,max:1200,step:25,style:{width:"100%"}},null,8,["modelValue"])])),_:1})])),_:1})])),_:1})])),_:1}),(0,i.bF)(S,{span:12},{default:(0,i.k6)((()=>[(0,i.bF)(P,{label:"高级选项"},{default:(0,i.k6)((()=>[(0,i.Lk)("div",k,[(0,i.Lk)("div",v,[(0,i.bF)(O,{modelValue:o.config.showDialog,"onUpdate:modelValue":l[4]||(l[4]=e=>o.config.showDialog=e)},{default:(0,i.k6)((()=>[(0,i.eW)("设备对话框")])),_:1},8,["modelValue"]),(0,i.bF)(O,{modelValue:o.config.autoFeedEnable,"onUpdate:modelValue":l[5]||(l[5]=e=>o.config.autoFeedEnable=e)},{default:(0,i.k6)((()=>[(0,i.eW)("自动进纸")])),_:1},8,["modelValue"]),(0,i.bF)(O,{modelValue:o.config.dupxMode,"onUpdate:modelValue":l[6]||(l[6]=e=>o.config.dupxMode=e)},{default:(0,i.k6)((()=>[(0,i.eW)("双面扫描")])),_:1},8,["modelValue"])]),(0,i.Lk)("div",b,[(0,i.bF)(O,{modelValue:o.config.autoDeskew,"onUpdate:modelValue":l[7]||(l[7]=e=>o.config.autoDeskew=e)},{default:(0,i.k6)((()=>[(0,i.eW)("自动纠偏")])),_:1},8,["modelValue"]),(0,i.bF)(O,{modelValue:o.config.autoBorderDetection,"onUpdate:modelValue":l[8]||(l[8]=e=>o.config.autoBorderDetection=e)},{default:(0,i.k6)((()=>[(0,i.eW)("边框检测")])),_:1},8,["modelValue"])])])])),_:1})])),_:1})])),_:1})])),_:1}),(0,i.bF)(u,{class:"action-card"},{default:(0,i.k6)((()=>[(0,i.bF)(j,{gutter:15},{default:(0,i.k6)((()=>[(0,i.bF)(S,{span:4},{default:(0,i.k6)((()=>[(0,i.bF)(G,{type:"primary",onClick:o.loadDevices,loading:o.loading.devices,style:{width:"100%"}},{default:(0,i.k6)((()=>[(0,i.bF)(r,null,{default:(0,i.k6)((()=>[(0,i.bF)(M)])),_:1}),(0,i.eW)(" 刷新设备 ")])),_:1},8,["onClick","loading"])])),_:1}),(0,i.bF)(S,{span:4},{default:(0,i.k6)((()=>[(0,i.bF)(G,{type:"success",onClick:o.startScan,loading:o.loading.scan,disabled:-1===o.selectedDevice||!o.isConnected,style:{width:"100%"}},{default:(0,i.k6)((()=>[(0,i.bF)(r,null,{default:(0,i.k6)((()=>[(0,i.bF)(s)])),_:1}),(0,i.eW)(" 开始扫描 ")])),_:1},8,["onClick","loading","disabled"])])),_:1}),(0,i.bF)(S,{span:4},{default:(0,i.k6)((()=>[(0,i.bF)(G,{type:"info",onClick:o.getAllImage,loading:o.loading.images,style:{width:"100%"}},{default:(0,i.k6)((()=>[(0,i.bF)(r,null,{default:(0,i.k6)((()=>[(0,i.bF)(K)])),_:1}),(0,i.eW)(" 获取图像 ")])),_:1},8,["onClick","loading"])])),_:1}),(0,i.bF)(S,{span:4},{default:(0,i.k6)((()=>[(0,i.bF)(G,{type:"warning",onClick:o.clearAll,style:{width:"100%"}},{default:(0,i.k6)((()=>[(0,i.eW)(" 🗑️ 清空结果 ")])),_:1},8,["onClick"])])),_:1}),(0,i.bF)(S,{span:4},{default:(0,i.k6)((()=>[(0,i.bF)(G,{type:"primary",onClick:o.showUploadDialog,disabled:0===o.images.length,style:{width:"100%"}},{default:(0,i.k6)((()=>[(0,i.eW)(" 📤 上传文档 ")])),_:1},8,["onClick","disabled"])])),_:1}),(0,i.bF)(S,{span:4},{default:(0,i.k6)((()=>[(0,i.bF)(G,{type:"default",onClick:o.saveAs,disabled:0===o.images.length,style:{width:"100%"}},{default:(0,i.k6)((()=>[(0,i.eW)(" 💾 本地保存 ")])),_:1},8,["onClick","disabled"])])),_:1})])),_:1})])),_:1}),o.images.length>0?((0,i.uX)(),(0,i.Wv)(u,{key:1,class:"result-card"},{header:(0,i.k6)((()=>[(0,i.Lk)("div",F,[(0,i.bF)(r,null,{default:(0,i.k6)((()=>[(0,i.bF)(K)])),_:1}),(0,i.Lk)("span",null,"扫描结果 ("+(0,d.v_)(o.images.length)+" 张图像)",1),(0,i.Lk)("div",h,[(0,i.bF)(J,null,{default:(0,i.k6)((()=>[(0,i.bF)(G,{type:"grid"===o.viewMode?"primary":"default",onClick:l[9]||(l[9]=e=>o.viewMode="grid"),size:"small"},{default:(0,i.k6)((()=>[(0,i.eW)(" 🔲 网格 ")])),_:1},8,["type"]),(0,i.bF)(G,{type:"list"===o.viewMode?"primary":"default",onClick:l[10]||(l[10]=e=>o.viewMode="list"),size:"small"},{default:(0,i.k6)((()=>[(0,i.eW)(" 📋 列表 ")])),_:1},8,["type"])])),_:1})])])])),default:(0,i.k6)((()=>["grid"===o.viewMode?((0,i.uX)(),(0,i.CE)("div",_,[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(o.images,((e,l)=>((0,i.uX)(),(0,i.CE)("div",{key:l,class:"image-item"},[(0,i.Lk)("div",y,[(0,i.Lk)("img",{src:e.src,alt:`扫描图像 ${l+1}`,onClick:e=>o.previewImage(l),class:"scan-image"},null,8,w),(0,i.Lk)("div",I,[(0,i.bF)(J,null,{default:(0,i.k6)((()=>[(0,i.bF)(G,{type:"primary",size:"small",onClick:e=>o.previewImage(l)},{default:(0,i.k6)((()=>[(0,i.eW)(" 👁️ ")])),_:2},1032,["onClick"]),(0,i.bF)(G,{type:"success",size:"small",onClick:e=>o.downloadImage(l)},{default:(0,i.k6)((()=>[(0,i.eW)(" 💾 ")])),_:2},1032,["onClick"]),(0,i.bF)(G,{type:"danger",size:"small",onClick:e=>o.deleteImage(l)},{default:(0,i.k6)((()=>[(0,i.eW)(" 🗑️ ")])),_:2},1032,["onClick"])])),_:2},1024)])]),(0,i.Lk)("div",C,[(0,i.Lk)("span",D,"图像 "+(0,d.v_)(l+1),1)])])))),128))])):((0,i.uX)(),(0,i.Wv)(N,{key:1,data:o.images,stripe:""},{default:(0,i.k6)((()=>[(0,i.bF)(X,{label:"预览",width:"120"},{default:(0,i.k6)((({row:e,$index:l})=>[(0,i.Lk)("img",{src:e.src,class:"table-thumbnail",onClick:e=>o.previewImage(l)},null,8,x)])),_:1}),(0,i.bF)(X,{label:"图像编号",prop:"index",width:"100"},{default:(0,i.k6)((({$index:e})=>[(0,i.eW)(" 图像 "+(0,d.v_)(e+1),1)])),_:1}),(0,i.bF)(X,{label:"格式",width:"100"},{default:(0,i.k6)((()=>[(0,i.bF)(z,{type:"info"},{default:(0,i.k6)((()=>[(0,i.eW)("JPEG")])),_:1})])),_:1}),(0,i.bF)(X,{label:"操作",width:"200"},{default:(0,i.k6)((({$index:e})=>[(0,i.bF)(J,null,{default:(0,i.k6)((()=>[(0,i.bF)(G,{type:"primary",size:"small",onClick:l=>o.previewImage(e)},{default:(0,i.k6)((()=>[(0,i.eW)(" 👁️ 预览 ")])),_:2},1032,["onClick"]),(0,i.bF)(G,{type:"success",size:"small",onClick:l=>o.downloadImage(e)},{default:(0,i.k6)((()=>[(0,i.eW)(" 💾 下载 ")])),_:2},1032,["onClick"]),(0,i.bF)(G,{type:"danger",size:"small",onClick:l=>o.deleteImage(e)},{default:(0,i.k6)((()=>[(0,i.eW)(" 🗑️ 删除 ")])),_:2},1032,["onClick"])])),_:2},1024)])),_:1})])),_:1},8,["data"]))])),_:1})):((0,i.uX)(),(0,i.Wv)(Q,{key:2,description:"暂无扫描结果",class:"empty-state"},{default:(0,i.k6)((()=>[(0,i.bF)(G,{type:"primary",onClick:o.startScan,disabled:-1===o.selectedDevice||!o.isConnected},{default:(0,i.k6)((()=>[(0,i.eW)(" 开始扫描 ")])),_:1},8,["onClick","disabled"])])),_:1}))]),(0,i.bF)(ee,{modelValue:o.uploadDialog.visible,"onUpdate:modelValue":l[15]||(l[15]=e=>o.uploadDialog.visible=e),title:"上传文档",width:"500px","close-on-click-modal":!1},{footer:(0,i.k6)((()=>[(0,i.bF)(G,{onClick:l[14]||(l[14]=e=>o.uploadDialog.visible=!1)},{default:(0,i.k6)((()=>[(0,i.eW)("取消")])),_:1}),(0,i.bF)(G,{type:"primary",onClick:o.confirmUpload,loading:o.loading.upload},{default:(0,i.k6)((()=>[(0,i.eW)(" 确认上传 ")])),_:1},8,["onClick","loading"])])),default:(0,i.k6)((()=>[(0,i.bF)(Z,{model:o.uploadDialog.form,"label-width":"100px"},{default:(0,i.k6)((()=>[(0,i.bF)(P,{label:"文档ID"},{default:(0,i.k6)((()=>[(0,i.bF)(q,{modelValue:o.uploadDialog.form.id,"onUpdate:modelValue":l[11]||(l[11]=e=>o.uploadDialog.form.id=e),placeholder:"请输入文档ID"},null,8,["modelValue"])])),_:1}),(0,i.bF)(P,{label:"文档描述"},{default:(0,i.k6)((()=>[(0,i.bF)(q,{modelValue:o.uploadDialog.form.description,"onUpdate:modelValue":l[12]||(l[12]=e=>o.uploadDialog.form.description=e),type:"textarea",rows:3,placeholder:"请输入文档描述"},null,8,["modelValue"])])),_:1}),(0,i.bF)(P,{label:"上传格式"},{default:(0,i.k6)((()=>[(0,i.bF)(H,{modelValue:o.uploadDialog.form.format,"onUpdate:modelValue":l[13]||(l[13]=e=>o.uploadDialog.form.format=e)},{default:(0,i.k6)((()=>[(0,i.bF)(Y,{label:"pdf"},{default:(0,i.k6)((()=>[(0,i.eW)("PDF文档")])),_:1}),(0,i.bF)(Y,{label:"tiff"},{default:(0,i.k6)((()=>[(0,i.eW)("TIFF图像")])),_:1}),(0,i.bF)(Y,{label:"jpg"},{default:(0,i.k6)((()=>[(0,i.eW)("JPG图像")])),_:1})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"]),(0,i.bF)(ee,{modelValue:o.previewDialog.visible,"onUpdate:modelValue":l[17]||(l[17]=e=>o.previewDialog.visible=e),title:"图像预览",width:"80%","close-on-click-modal":!1},{footer:(0,i.k6)((()=>[(0,i.Lk)("div",U,[(0,i.Lk)("div",R," 图像 "+(0,d.v_)(o.previewDialog.currentIndex+1)+" / "+(0,d.v_)(o.images.length),1),(0,i.Lk)("div",A,[(0,i.bF)(G,{onClick:o.prevImage,disabled:o.previewDialog.currentIndex<=0},{default:(0,i.k6)((()=>[(0,i.eW)(" ⬅️ 上一张 ")])),_:1},8,["onClick","disabled"]),(0,i.bF)(G,{onClick:o.nextImage,disabled:o.previewDialog.currentIndex>=o.images.length-1},{default:(0,i.k6)((()=>[(0,i.eW)(" 下一张 ➡️ ")])),_:1},8,["onClick","disabled"]),(0,i.bF)(G,{type:"success",onClick:o.downloadCurrentImage},{default:(0,i.k6)((()=>[(0,i.eW)(" 💾 下载 ")])),_:1},8,["onClick"]),(0,i.bF)(G,{onClick:l[16]||(l[16]=e=>o.previewDialog.visible=!1)},{default:(0,i.k6)((()=>[(0,i.eW)("关闭")])),_:1})])])])),default:(0,i.k6)((()=>[o.previewDialog.currentImage?((0,i.uX)(),(0,i.CE)("div",V,[(0,i.Lk)("img",{src:o.previewDialog.currentImage.src,class:"preview-image",alt:`预览图像 ${o.previewDialog.currentIndex+1}`},null,8,W)])):(0,i.Q3)("",!0)])),_:1},8,["modelValue"])],64)}a(4114);var E=a(144),B=a(1219),T=a(2933),P=a(9551),S=a.n(P),$=(a(6573),a(8100),a(7936),a(7467),a(4732),a(9577),a(4979),a(4603),a(7566),a(8721),a(4373));const j=$.A.create({baseURL:"http://localhost:8080",timeout:3e4,headers:{"Content-Type":"application/json"}});j.interceptors.request.use((e=>(console.log("发送请求:",e.method?.toUpperCase(),e.url),e)),(e=>(console.error("请求错误:",e),Promise.reject(e)))),j.interceptors.response.use((e=>(console.log("收到响应:",e.status,e.config.url),e)),(e=>(console.error("响应错误:",e.response?.status,e.message),404===e.response?.status?console.error("API接口不存在"):e.response?.status>=500?console.error("服务器内部错误"):"ECONNREFUSED"===e.code&&console.error("无法连接到服务器，请确保后台服务已启动"),Promise.reject(e))));const O={base64ToBlob(e,l="image/jpeg"){const a=atob(e),o=new Array(a.length);for(let t=0;t<a.length;t++)o[t]=a.charCodeAt(t);const n=new Uint8Array(o);return new Blob([n],{type:l})},downloadBase64Image(e,l="image.jpg"){const a=this.base64ToBlob(e),o=URL.createObjectURL(a),n=document.createElement("a");n.href=o,n.download=l,document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(o)},formatFileSize(e){if(0===e)return"0 Bytes";const l=1024,a=["Bytes","KB","MB","GB"],o=Math.floor(Math.log(e)/Math.log(l));return parseFloat((e/Math.pow(l,o)).toFixed(2))+" "+a[o]},generateId(){return Date.now().toString(36)+Math.random().toString(36).substr(2)}};var M={name:"ScanOnWeb",components:{Camera:t.Camera,Setting:t.Setting,Picture:t.Picture,Refresh:t.Refresh},setup(){const e=(0,E.KR)(null),l=(0,E.KR)(!1),a=(0,E.KR)([]),o=(0,E.KR)(-1),n=(0,E.KR)([]),t=(0,E.KR)("grid"),s=(0,E.Kh)({dpi_x:300,dpi_y:300,colorMode:"RGB",showDialog:!1,autoFeedEnable:!0,autoFeed:!1,dupxMode:!1,autoDeskew:!1,autoBorderDetection:!1}),r=(0,E.Kh)({devices:!1,scan:!1,images:!1,upload:!1}),d=(0,E.Kh)({visible:!1,form:{id:"",description:"",format:"pdf"}}),u=(0,E.Kh)({visible:!1,currentIndex:0,currentImage:null}),c=()=>{try{e.value=new(S()),e.value.onGetDevicesListEvent=e=>{a.value=e.devices||[],a.value.length>0?(o.value=e.currentIndex>=0?e.currentIndex:0,o.value>=0&&setTimeout((()=>{p(o.value)}),100)):o.value=-1,r.devices=!1,l.value=!0,B.nk.success(`发现 ${a.value.length} 个扫描设备${o.value>=0?"，已自动选择第一个设备":""}`)},e.value.onScanFinishedEvent=e=>{r.scan=!1,B.nk.success(`扫描完成！共扫描 ${e.imageAfterCount} 张图像`),m()},e.value.onGetAllImageEvent=e=>{r.images=!1,e.images&&e.images.length>0?(n.value=e.images.map(((e,l)=>({src:`data:image/jpg;base64,${e}`,index:l,base64:e}))),B.nk.success(`获取到 ${n.value.length} 张图像`)):B.nk.info("暂无扫描图像")},e.value.onGetImageByIdEvent=e=>{e.imageBase64&&v(e.imageBase64)},e.value.onImageEditedEvent=e=>{B.nk.info(`图像 ${e.imageIndex+1} 已编辑`),e.imageBase64&&b(e.imageIndex,e.imageBase64)},e.value.onUploadEvent=()=>{B.nk.info("用户点击了上传按钮"),F()},setTimeout((()=>{0===a.value.length&&(l.value=!1,B.nk.warning("扫描服务连接失败，请检查服务是否启动"))}),3e3)}catch(t){console.error("初始化扫描服务失败:",t),l.value=!1,B.nk.error("初始化扫描服务失败")}},g=async()=>{if(e.value){r.devices=!0;try{e.value.loadDevices()}catch(l){r.devices=!1,B.nk.error("获取设备列表失败")}}else B.nk.error("扫描服务未初始化")},p=l=>{e.value&&(e.value.selectScanDevice(l),B.nk.info(`已选择设备: ${a.value[l]}`))},f=async()=>{if(e.value)if(-1!==o.value){r.scan=!0;try{e.value.scaner_work_config={...e.value.scaner_work_config,...s,deviceIndex:o.value},e.value.startScan(),B.nk.info("开始扫描...")}catch(l){r.scan=!1,B.nk.error("启动扫描失败")}}else B.nk.warning("请先选择扫描设备");else B.nk.error("扫描服务未初始化")},m=async()=>{if(e.value){r.images=!0;try{e.value.getAllImage()}catch(l){r.images=!1,B.nk.error("获取图像失败")}}else B.nk.error("扫描服务未初始化")},k=async()=>{try{await T.s.confirm("确定要清空所有扫描结果吗？","确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),e.value&&(e.value.clearAll(),n.value=[],B.nk.success("已清空所有扫描结果"))}catch{}},v=e=>{n.value.push({src:`data:image/jpg;base64,${e}`,index:n.value.length,base64:e})},b=(e,l)=>{e>=0&&e<n.value.length&&(n.value[e]={src:`data:image/jpg;base64,${l}`,index:e,base64:l})},F=()=>{0!==n.value.length?(d.form.id=O.generateId(),d.form.description=`扫描文档_${(new Date).toLocaleString()}`,d.visible=!0):B.nk.warning("没有可上传的图像")},h=async()=>{if(d.form.id.trim())if(e.value)if(0!==n.value.length){r.upload=!0;try{const{id:l,description:a,format:o}=d.form,n="http://localhost:8080/upload";if(e.value.onUploadAllImageAsPdfToUrlEvent=e=>{console.log("PDF上传回调:",e),r.upload=!1;try{let l=null;if(e.uploadResult&&(l="string"===typeof e.uploadResult?JSON.parse(e.uploadResult):e.uploadResult),l&&l.success)B.nk.success(`PDF文档上传成功！共 ${e.imageCount||0} 张图像`),d.visible=!1,console.log("上传详情:",l);else{const a=l?.message||e.message||"未知错误";B.nk.error(`PDF上传失败: ${a}`)}}catch(l){console.error("解析上传结果失败:",l,e),B.nk.error("PDF上传失败: 响应解析错误")}},e.value.onUploadAllImageAsTiffToUrlEvent=e=>{console.log("TIFF上传回调:",e),r.upload=!1;try{let l=null;if(e.uploadResult&&(l="string"===typeof e.uploadResult?JSON.parse(e.uploadResult):e.uploadResult),l&&l.success)B.nk.success(`TIFF文档上传成功！共 ${e.imageCount||0} 张图像`),d.visible=!1,console.log("上传详情:",l);else{const a=l?.message||e.message||"未知错误";B.nk.error(`TIFF上传失败: ${a}`)}}catch(l){console.error("解析上传结果失败:",l,e),B.nk.error("TIFF上传失败: 响应解析错误")}},e.value.onUploadJpgImageByIndexEvent=e=>{console.log("JPG上传回调:",e),r.upload=!1;try{let l=null;if(e.uploadResult&&(l="string"===typeof e.uploadResult?JSON.parse(e.uploadResult):e.uploadResult),l&&l.success)B.nk.success("JPG图像上传成功！"),d.visible=!1,console.log("上传详情:",l);else{const a=l?.message||e.message||"未知错误";B.nk.error(`JPG上传失败: ${a}`)}}catch(l){console.error("解析上传结果失败:",l,e),B.nk.error("JPG上传失败: 响应解析错误")}},"pdf"===o)B.nk.info("开始上传PDF文档..."),e.value.uploadAllImageAsPdfToUrl(n,l,a);else if("tiff"===o){B.nk.info("开始上传TIFF文档...");const o="http://localhost:8080/upload-tiff";e.value.uploadAllImageAsTiffToUrl(o,l,a)}else if("jpg"===o){B.nk.info("开始上传JPG图像...");const o="http://localhost:8080/upload-jpg";e.value.uploadJpgImageByIndex(o,l,a,0)}}catch(l){r.upload=!1,console.error("上传失败:",l),B.nk.error(`上传失败: ${l.message||"未知错误"}`)}}else B.nk.warning("没有可上传的图像");else B.nk.error("扫描服务未初始化");else B.nk.warning("请输入文档ID")},_=e=>{e>=0&&e<n.value.length&&(u.currentIndex=e,u.currentImage=n.value[e],u.visible=!0)},y=()=>{u.currentIndex>0&&(u.currentIndex--,u.currentImage=n.value[u.currentIndex])},w=()=>{u.currentIndex<n.value.length-1&&(u.currentIndex++,u.currentImage=n.value[u.currentIndex])},I=e=>{if(e>=0&&e<n.value.length){const l=n.value[e],a=`scan_image_${e+1}.jpg`;O.downloadBase64Image(l.base64,a),B.nk.success(`图像 ${e+1} 下载成功`)}},C=()=>{I(u.currentIndex)},D=async e=>{try{await T.s.confirm(`确定要删除图像 ${e+1} 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),n.value.splice(e,1),n.value.forEach(((e,l)=>{e.index=l})),B.nk.success("图像删除成功")}catch{}},x=()=>{if(e.value)if(0!==n.value.length)try{const l=`d:/scan_${Date.now()}.pdf`;e.value.saveAllImageToLocal(l),B.nk.success("文件保存成功")}catch(l){B.nk.error("保存失败")}else B.nk.warning("没有可保存的图像");else B.nk.error("扫描服务未初始化")};return(0,i.sV)((()=>{c(),setTimeout((()=>{g()}),1e3)})),(0,i.hi)((()=>{e.value&&e.value.h5socket&&e.value.h5socket.close()})),{isConnected:l,devices:a,selectedDevice:o,images:n,viewMode:t,config:s,loading:r,uploadDialog:d,previewDialog:u,loadDevices:g,onDeviceChange:p,startScan:f,getAllImage:m,clearAll:k,showUploadDialog:F,confirmUpload:h,previewImage:_,prevImage:y,nextImage:w,downloadImage:I,downloadCurrentImage:C,deleteImage:D,saveAs:x}}},G=a(1241);const K=(0,G.A)(M,[["render",L],["__scopeId","data-v-5fde91ee"]]);var J=K,X={name:"App",components:{ScanOnWeb:J}};const z=(0,G.A)(X,[["render",r]]);var N=z;const Q=(0,o.Ef)(N);Q.use(n.A);for(const[q,Y]of Object.entries(t))Q.component(q,Y);Q.mount("#app")}},l={};function a(o){var n=l[o];if(void 0!==n)return n.exports;var t=l[o]={exports:{}};return e[o].call(t.exports,t,t.exports,a),t.exports}a.m=e,function(){var e=[];a.O=function(l,o,n,t){if(!o){var i=1/0;for(u=0;u<e.length;u++){o=e[u][0],n=e[u][1],t=e[u][2];for(var s=!0,r=0;r<o.length;r++)(!1&t||i>=t)&&Object.keys(a.O).every((function(e){return a.O[e](o[r])}))?o.splice(r--,1):(s=!1,t<i&&(i=t));if(s){e.splice(u--,1);var d=n();void 0!==d&&(l=d)}}return l}t=t||0;for(var u=e.length;u>0&&e[u-1][2]>t;u--)e[u]=e[u-1];e[u]=[o,n,t]}}(),function(){a.n=function(e){var l=e&&e.__esModule?function(){return e["default"]}:function(){return e};return a.d(l,{a:l}),l}}(),function(){a.d=function(e,l){for(var o in l)a.o(l,o)&&!a.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:l[o]})}}(),function(){a.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){a.o=function(e,l){return Object.prototype.hasOwnProperty.call(e,l)}}(),function(){a.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){var e={524:0};a.O.j=function(l){return 0===e[l]};var l=function(l,o){var n,t,i=o[0],s=o[1],r=o[2],d=0;if(i.some((function(l){return 0!==e[l]}))){for(n in s)a.o(s,n)&&(a.m[n]=s[n]);if(r)var u=r(a)}for(l&&l(o);d<i.length;d++)t=i[d],a.o(e,t)&&e[t]&&e[t][0](),e[t]=0;return a.O(u)},o=self["webpackChunkvue3demo"]=self["webpackChunkvue3demo"]||[];o.forEach(l.bind(null,0)),o.push=l.bind(null,o.push.bind(o))}();var o=a.O(void 0,[504],(function(){return a(8163)}));o=a.O(o)})();
//# sourceMappingURL=app.681675a9.js.map