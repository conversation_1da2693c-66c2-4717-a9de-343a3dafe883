use axum::{response::<PERSON><PERSON>, http::StatusCode};
use crate::models::HealthResponse;

/// 健康检查处理器
pub async fn health_check() -> Result<Json<HealthResponse>, StatusCode> {
    let response = HealthResponse {
        status: "ok".to_string(),
        time: chrono::Utc::now().to_rfc3339(),
        service: "ScanOnWeb Rust Server".to_string(),
        version: "1.0.0".to_string(),
    };
    
    Ok(<PERSON><PERSON>(response))
}
