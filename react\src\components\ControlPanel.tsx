import React from 'react';
import './ControlPanel.css';
import { ScanConfig, ScanImage } from './ScannerApp';

interface ControlPanelProps {
  isConnected: boolean;
  devices: string[];
  selectedDevice: number;
  config: ScanConfig;
  loading: {
    devices: boolean;
    scan: boolean;
    images: boolean;
    upload: boolean;
  };
  images: ScanImage[];
  onDeviceChange: (index: number) => void;
  onConfigChange: (config: ScanConfig) => void;
  onLoadDevices: () => void;
  onStartScan: () => void;
  onGetImages: () => void;
  onClearAll: () => void;
  onUpload: () => void;
  onSaveAs: () => void;
}

const ControlPanel: React.FC<ControlPanelProps> = ({
  isConnected,
  devices,
  selectedDevice,
  config,
  loading,
  images,
  onDeviceChange,
  onConfigChange,
  onLoadDevices,
  onStartScan,
  onGetImages,
  onClearAll,
  onUpload,
  onSaveAs
}) => {
  // 添加调试信息
  console.log('ControlPanel渲染:', {
    isConnected,
    devices: devices.length,
    selectedDevice,
    deviceName: devices[selectedDevice]
  });

  const handleConfigChange = (key: keyof ScanConfig, value: any) => {
    onConfigChange({
      ...config,
      [key]: value
    });
  };

  return (
    <div className="control-panel">
      {/* 设备配置区域 */}
      <div className="panel-section">
        <div className="section-header">
          <i className="fas fa-cog"></i>
          <span>设备配置</span>
        </div>
        <div className="section-content">
          <div className="form-group">
            <label>扫描设备</label>
            <select
              value={selectedDevice}
              onChange={(e) => onDeviceChange(parseInt(e.target.value))}
              className="form-control"
            >
              <option value={-1}>请选择扫描设备</option>
              {devices.map((device, index) => (
                <option key={index} value={index}>
                  {device}
                </option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label>色彩模式</label>
            <select
              value={config.colorMode}
              onChange={(e) => handleConfigChange('colorMode', e.target.value)}
              className="form-control"
            >
              <option value="RGB">彩色</option>
              <option value="GRAY">灰度</option>
              <option value="BW">黑白</option>
            </select>
          </div>

          <div className="form-group">
            <label>分辨率 (DPI)</label>
            <div className="dpi-inputs">
              <input
                type="number"
                value={config.dpi_x}
                onChange={(e) => handleConfigChange('dpi_x', parseInt(e.target.value))}
                className="form-control"
                min="75"
                max="1200"
                step="25"
              />
              <span className="dpi-separator">×</span>
              <input
                type="number"
                value={config.dpi_y}
                onChange={(e) => handleConfigChange('dpi_y', parseInt(e.target.value))}
                className="form-control"
                min="75"
                max="1200"
                step="25"
              />
            </div>
          </div>
        </div>
      </div>

      {/* 高级选项 */}
      <div className="panel-section">
        <div className="section-header">
          <i className="fas fa-sliders-h"></i>
          <span>高级选项</span>
        </div>
        <div className="section-content">
          <div className="checkbox-grid">
            <label className="checkbox-item">
              <input
                type="checkbox"
                checked={config.showDialog}
                onChange={(e) => handleConfigChange('showDialog', e.target.checked)}
              />
              <span>设备对话框</span>
            </label>
            <label className="checkbox-item">
              <input
                type="checkbox"
                checked={config.autoFeedEnable}
                onChange={(e) => handleConfigChange('autoFeedEnable', e.target.checked)}
              />
              <span>自动进纸</span>
            </label>
            <label className="checkbox-item">
              <input
                type="checkbox"
                checked={config.dupxMode}
                onChange={(e) => handleConfigChange('dupxMode', e.target.checked)}
              />
              <span>双面扫描</span>
            </label>
            <label className="checkbox-item">
              <input
                type="checkbox"
                checked={config.autoDeskew}
                onChange={(e) => handleConfigChange('autoDeskew', e.target.checked)}
              />
              <span>自动纠偏</span>
            </label>
            <label className="checkbox-item">
              <input
                type="checkbox"
                checked={config.autoBorderDetection}
                onChange={(e) => handleConfigChange('autoBorderDetection', e.target.checked)}
              />
              <span>边框检测</span>
            </label>
          </div>
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="panel-section">
        <div className="section-header">
          <i className="fas fa-play"></i>
          <span>扫描操作</span>
        </div>
        <div className="section-content">
          <div className="button-grid">
            <button
              className="btn btn-primary"
              onClick={onLoadDevices}
              disabled={loading.devices}
            >
              <i className="fas fa-sync-alt"></i>
              {loading.devices ? '刷新中...' : '刷新设备'}
            </button>

            <button
              className="btn btn-success"
              onClick={onStartScan}
              disabled={selectedDevice === -1 || !isConnected || loading.scan}
            >
              <i className="fas fa-camera"></i>
              {loading.scan ? '扫描中...' : '开始扫描'}
            </button>

            <button
              className="btn btn-info"
              onClick={onGetImages}
              disabled={loading.images}
            >
              <i className="fas fa-images"></i>
              {loading.images ? '获取中...' : '获取图像'}
            </button>

            <button
              className="btn btn-warning"
              onClick={onClearAll}
            >
              <i className="fas fa-trash"></i>
              清空结果
            </button>
          </div>
        </div>
      </div>

      {/* 文件操作 */}
      <div className="panel-section">
        <div className="section-header">
          <i className="fas fa-file"></i>
          <span>文件操作</span>
        </div>
        <div className="section-content">
          <div className="button-grid">
            <button
              className="btn btn-primary"
              onClick={onUpload}
              disabled={images.length === 0 || loading.upload}
            >
              <i className="fas fa-upload"></i>
              {loading.upload ? '上传中...' : '上传文档'}
            </button>

            <button
              className="btn btn-secondary"
              onClick={onSaveAs}
              disabled={images.length === 0}
            >
              <i className="fas fa-save"></i>
              本地保存
            </button>
          </div>
        </div>
      </div>

      {/* 状态信息 */}
      <div className="panel-section">
        <div className="section-header">
          <i className="fas fa-info-circle"></i>
          <span>状态信息</span>
        </div>
        <div className="section-content">
          <div className="status-info">
            <div className="status-item">
              <span className="status-label">连接状态:</span>
              <span className={`status-value ${isConnected ? 'connected' : 'disconnected'}`}>
                {isConnected ? '已连接' : '未连接'}
              </span>
            </div>
            <div className="status-item">
              <span className="status-label">设备数量:</span>
              <span className="status-value">{devices.length} 个</span>
            </div>
            <div className="status-item">
              <span className="status-label">图像数量:</span>
              <span className="status-value">{images.length} 张</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ControlPanel;
