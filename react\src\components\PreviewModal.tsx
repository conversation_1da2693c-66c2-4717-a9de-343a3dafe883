import React, { useEffect } from 'react';
import './PreviewModal.css';
import { ScanImage } from './ScannerApp';

interface PreviewModalProps {
  visible: boolean;
  images: ScanImage[];
  currentIndex: number;
  onClose: () => void;
  onPrev: () => void;
  onNext: () => void;
  onDownload: () => void;
}

const PreviewModal: React.FC<PreviewModalProps> = ({
  visible,
  images,
  currentIndex,
  onClose,
  onPrev,
  onNext,
  onDownload
}) => {
  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!visible) return;
      
      switch (e.key) {
        case 'Escape':
          onClose();
          break;
        case 'ArrowLeft':
          if (currentIndex > 0) onPrev();
          break;
        case 'ArrowRight':
          if (currentIndex < images.length - 1) onNext();
          break;
        default:
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [visible, currentIndex, images.length, onClose, onPrev, onNext]);

  if (!visible || images.length === 0) return null;

  const currentImage = images[currentIndex];
  const canGoPrev = currentIndex > 0;
  const canGoNext = currentIndex < images.length - 1;

  return (
    <div className="preview-overlay" onClick={onClose}>
      <div className="preview-container" onClick={(e) => e.stopPropagation()}>
        {/* 预览标题栏 */}
        <div className="preview-header">
          <div className="preview-title">
            <i className="fas fa-eye"></i>
            图像预览
          </div>
          <div className="preview-info">
            {currentIndex + 1} / {images.length}
          </div>
          <button className="preview-close" onClick={onClose}>
            <i className="fas fa-times"></i>
          </button>
        </div>

        {/* 预览内容 */}
        <div className="preview-content">
          {/* 左侧导航按钮 */}
          <button
            className={`nav-btn nav-prev ${!canGoPrev ? 'disabled' : ''}`}
            onClick={onPrev}
            disabled={!canGoPrev}
            title="上一张 (←)"
          >
            <i className="fas fa-chevron-left"></i>
          </button>

          {/* 图像显示区域 */}
          <div className="image-container">
            <img
              src={currentImage.src}
              alt={`预览图像 ${currentIndex + 1}`}
              className="preview-image"
            />
          </div>

          {/* 右侧导航按钮 */}
          <button
            className={`nav-btn nav-next ${!canGoNext ? 'disabled' : ''}`}
            onClick={onNext}
            disabled={!canGoNext}
            title="下一张 (→)"
          >
            <i className="fas fa-chevron-right"></i>
          </button>
        </div>

        {/* 预览底部操作栏 */}
        <div className="preview-footer">
          <div className="image-details">
            <span className="detail-item">
              <i className="fas fa-image"></i>
              图像 {currentIndex + 1}
            </span>
            <span className="detail-item">
              <i className="fas fa-file"></i>
              JPEG 格式
            </span>
          </div>

          <div className="preview-actions">
            <button
              className="action-btn prev-btn"
              onClick={onPrev}
              disabled={!canGoPrev}
              title="上一张"
            >
              <i className="fas fa-chevron-left"></i>
              上一张
            </button>

            <button
              className="action-btn download-btn"
              onClick={onDownload}
              title="下载当前图像"
            >
              <i className="fas fa-download"></i>
              下载
            </button>

            <button
              className="action-btn next-btn"
              onClick={onNext}
              disabled={!canGoNext}
              title="下一张"
            >
              下一张
              <i className="fas fa-chevron-right"></i>
            </button>

            <button
              className="action-btn close-btn"
              onClick={onClose}
              title="关闭预览"
            >
              <i className="fas fa-times"></i>
              关闭
            </button>
          </div>
        </div>

        {/* 缩略图导航 */}
        {images.length > 1 && (
          <div className="thumbnail-nav">
            <div className="thumbnail-list">
              {images.map((image, index) => (
                <div
                  key={index}
                  className={`thumbnail-item ${index === currentIndex ? 'active' : ''}`}
                  onClick={() => {
                    const diff = index - currentIndex;
                    if (diff < 0) {
                      for (let i = 0; i < Math.abs(diff); i++) {
                        onPrev();
                      }
                    } else if (diff > 0) {
                      for (let i = 0; i < diff; i++) {
                        onNext();
                      }
                    }
                  }}
                >
                  <img
                    src={image.src}
                    alt={`缩略图 ${index + 1}`}
                    className="thumbnail-image"
                  />
                  <span className="thumbnail-number">{index + 1}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PreviewModal;
